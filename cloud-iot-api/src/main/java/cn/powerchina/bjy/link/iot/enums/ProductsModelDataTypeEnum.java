package cn.powerchina.bjy.link.iot.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
public enum ProductsModelDataTypeEnum {
    PRODUCT_MODEL_CREATE("products_model_create", "产品创建"),
    PRODUCT_MODEL_UPDATE("products_model_update", "产品更新"),
    PRODUCT_MODEL_DELETE("products_model_delete", "产品删除");

    private final String code;
    private final String desc;

    ProductsModelDataTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ProductsModelDataTypeEnum getByCode(String code) {
        for (ProductsModelDataTypeEnum item : values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return null;
    }


    /**
     * 返回包含所有枚举信息的Map列表
     * @return List<Map<String, Object>> 枚举信息列表
     */
    public static List<Map<String, Object>> getEnumList() {
        return Arrays.stream(values())
                .map(enumItem -> {
                    Map<String, Object> map = new HashMap<>();
                    map.put("code", enumItem.getCode());
                    map.put("desc", enumItem.getDesc());
                    return map;
                })
                .collect(Collectors.toList());
    }

}
