package cn.powerchina.bjy.link.iot.api.devicegroup.dto;

import com.fhs.core.trans.vo.VO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "管理后台 - 设备分组信息 Response VO")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DeviceGroupRespDTO implements VO {

    @Schema(description = "主键id")
    private Long id;

    @Schema(description = "资源空间id")
    private Long resourceSpaceId;

    @Schema(description = "父节点id")
    private Long parentId;

    @Schema(description = "设备分组名称")
    private String groupName;

}
