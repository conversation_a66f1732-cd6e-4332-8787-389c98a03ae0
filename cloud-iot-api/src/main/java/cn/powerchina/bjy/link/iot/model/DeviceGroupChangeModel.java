package cn.powerchina.bjy.link.iot.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @Description: 描述
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @CreateDate: 2024/10/16
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeviceGroupChangeModel implements MessageToEdge, Serializable {

    private Long deviceGroupId;

    private Integer changeType;

    private List<String> deviceCodeList;
}
