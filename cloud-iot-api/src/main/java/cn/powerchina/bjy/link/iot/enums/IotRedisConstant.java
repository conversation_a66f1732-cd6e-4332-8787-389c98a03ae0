package cn.powerchina.bjy.link.iot.enums;

/**
 * @Description: 描述
 * @Author: yhx
 * @CreateDate: 2024/8/22
 */
public class IotRedisConstant {

    /**
     * 消息数按日统计key
     */
    public static final String STATISTIC_MESSAGE_DAY_KEY = "iot:statistic:message:%s:day:%s";

    /**
     * 生效时间段内的规则配置
     */
    public static final String RULE_TIME_CONFIG_KEY_PREFIX = "iot:ruleTimeConfig:";

    /**
     * 被抑制的规则
     */
    public static final String RULE_INHIBITION_KEY= "iot:inhibitionRuleId";
}
