package cn.powerchina.bjy.link.iot.enums;

import cn.hutool.core.util.ArrayUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 触发类型
 *
 * <AUTHOR>
 **/
@Getter
@AllArgsConstructor
public enum AlarmLevelEnum {

    SLIGHT(1, "轻微"),
    MEDIUM(2, "中等"),
    SERIOUS(3, "严重"),
    VERY_SERIOUS(4, "非常严重");

    private final Integer code;
    private final String desc;

    public static AlarmLevelEnum getByCode(Integer code) {
        return ArrayUtil.firstMatch(o -> o.getCode().equals(code), values());
    }

}
