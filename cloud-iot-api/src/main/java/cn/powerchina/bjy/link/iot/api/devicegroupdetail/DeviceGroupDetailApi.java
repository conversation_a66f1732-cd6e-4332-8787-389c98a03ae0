package cn.powerchina.bjy.link.iot.api.devicegroupdetail;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.link.iot.api.devicegroup.dto.DeviceGroupRespDTO;
import cn.powerchina.bjy.link.iot.api.devicegroup.dto.DeviceRespDTO;
import cn.powerchina.bjy.link.iot.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

@FeignClient(name = ApiConstants.NAME)
@Tag(name = "RPC 服务 - 设备分组明细")
public interface DeviceGroupDetailApi {

    String PREFIX = ApiConstants.PREFIX + "/deviceGroupDetail";

    @GetMapping(PREFIX + "/getDevice")
    @Operation(summary = "根据deviceCode获取设备")
    @Parameter(name = "deviceCodeList", description = "设备code集合", required = true)
    CommonResult<List<DeviceRespDTO>> getDeviceByDeviceCodeList(@RequestParam("deviceCodeList") List<String> deviceCodeList);

}