package cn.powerchina.bjy.link.iot.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @Description: 描述
 * @Author: yhx
 * @CreateDate: 2024/9/29
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeviceCommandModel implements MessageToEdge, Serializable {

    /**
     * 设备编码
     */
    private String deviceCode;

    /**
     * MCU通道编码
     */
    private List<String> mcuChannelList;

}
