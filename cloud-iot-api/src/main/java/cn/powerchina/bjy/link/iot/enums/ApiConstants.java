package cn.powerchina.bjy.link.iot.enums;

import cn.powerchina.bjy.cloud.framework.common.enums.RpcConstants;

/**
 * API 相关的枚举
 *
 * <AUTHOR>
 */
public class ApiConstants {

    /**
     * 服务名
     *
     * 注意，需要保证和 spring.application.name 保持一致
     */
    public static final String NAME = "iot-server";

    public static final String PREFIX = RpcConstants.RPC_API_PREFIX +  "/iot";

    public static final String VERSION = "1.0.0";

    /**
     * 边缘网关配置下发锁前缀
     * 格式为edge:register:{type}:{devicecode||edgecode}
     */
    public static final String EDGE_REGISTER_LOCK = "edge:register:";

}
