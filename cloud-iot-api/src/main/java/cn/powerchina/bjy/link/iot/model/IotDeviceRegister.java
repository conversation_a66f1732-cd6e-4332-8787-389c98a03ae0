package cn.powerchina.bjy.link.iot.model;

import cn.powerchina.bjy.link.iot.enums.device.IotDeviceMessageMethodEnum;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * IoT 设备消息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class IotDeviceRegister {


    /**
     * 应用的设备消息 Topic，由 iot-gateway 发给 iot-biz 进行消费
     */

    public static final String TOPIC_IOT_DEVICE_REGISTER = "topic_iot_device_register";
    public static final String GROUP_IOT_DEVICE_REGISTER = "group_iot_device_register";

    public static final String TOPIC_IOT_DEVICE_REGISTER_RESPONSE = "/iot/devices/+/register/response";

    public static final String TOPIC_IOT_DEVICE_GATEWAY_REGISTER = "topic_iot_device_gateway_register";
    public static final String GROUP_IOT_DEVICE_GATEWAY_REGISTER = "group_iot_device_gateway_register";

    public static final String TOPIC_IOT_DEVICE_GATEWAY_REGISTER_RESPONSE = "/iot/devices/common/+/register/response";

    public static final String DEVICE_REGISTER_METHOD = "device.register";
    public static final String DEVICE_REGISTER_GATEWAY_METHOD = "gateway.register";

    /**
     * 消息编号
     * <p>
     */
    private String id;
    /**
     * 设备编号
     */
    private String deviceId;
    private String deviceName;
    private String userName;
    private String password;
    private String productId;
    private String gatewayId;

    /**
     * 上报时间
     * 为空，设置当前时间
     */
    private Long reportTime;

    /**
     * 类型(0:edge实例; 1:设备）
     */
    private Integer deviceType;

    /**
     * 唯一标识
     */
    private String thingIdentity;

    /**
     * 请求方法
     * <p>
     * 枚举 {@link IotDeviceMessageMethodEnum}
     * 例如说：thing.property.report 属性上报
     */
    private String method;

    /**
     * 边缘实例code
     */
    private String edgeCode;

    private List<IotDeviceRegister> gatewayDevices = new ArrayList<>();

    private List<IotDeviceRegister> subDevices = new ArrayList<>();


}