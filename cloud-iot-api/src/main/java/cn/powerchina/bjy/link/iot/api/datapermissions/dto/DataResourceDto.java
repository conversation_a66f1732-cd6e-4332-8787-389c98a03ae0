package cn.powerchina.bjy.link.iot.api.datapermissions.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 数据权限新增/修改 Request VO")
@Data
public class DataResourceDto {


    @NotNull(message = "资源空间ID不能为空")
    private String resourceSpaceId;

    private List<DataProductDto> productList;

    private List<DataGatewayDto> edgeList;


}
