package cn.powerchina.bjy.link.iot.api.transportTarget;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.link.iot.api.transportTarget.dto.TransportTargetRespDTO;
import cn.powerchina.bjy.link.iot.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = ApiConstants.NAME)
@Tag(name = "RPC 服务 - 转发目标")
public interface TransportTargetApi {

    String PREFIX = ApiConstants.PREFIX + "/transportTarget";

    @GetMapping(PREFIX + "/getTransportTarget")
    @Operation(summary = "获得转发目标")
    @Parameter(name = "target", description = "转发目标", required = true)
    CommonResult<List<TransportTargetRespDTO>> getTransportTarget(@RequestParam(value = "target", required = false) String target);

}