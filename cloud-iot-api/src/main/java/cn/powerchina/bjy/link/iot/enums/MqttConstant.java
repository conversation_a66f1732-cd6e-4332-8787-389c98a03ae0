package cn.powerchina.bjy.link.iot.enums;

/**
 * mqtt相关
 */
public class MqttConstant {

    private MqttConstant() {
        throw new IllegalStateException(ExceptionConstant.UTILITY_CLASS);
    }

    /**
     * 遗嘱消息主题
     */
    public static final String TOPIC_WILL = "iot-edge/willTopic";


    /**
     * 断开连接
     */
    public static final String MQTT_STATUS_DISCONNECTED = "disconnected";

    /**
     * 连接
     */
    public static final String MQTT_STATUS_CONNECTED = "connected";

    /**
     * 共享组订阅前缀
     */
    public static final String MQTT_SHARE_GROUP_PREFIX = "$share/";
}
