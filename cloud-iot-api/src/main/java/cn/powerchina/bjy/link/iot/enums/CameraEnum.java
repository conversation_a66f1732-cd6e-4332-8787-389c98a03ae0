package cn.powerchina.bjy.link.iot.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public  enum CameraEnum {
    // 摄像头控制参数
    UP(1, "上"),
    DOWN(2, "下"),
    LEFT(3, "左"),
    RIGHT(4, "右"),
    LEFT_UP(5, "左上"),
    RIGHT_UP(6, "右上"),
    LEFT_DOWN(7, "左下"),
    RIGHT_DOWN(8, "右下"),
    ZOOM_OUT(9, "变倍减"),
    ZOOM_IN(10, "变倍增"),
    FOCUS_IN(11, "变焦增"),
    FOCUS_OUT(12, "变焦减"),
    IRIS_DECREASE(13, "光圈减"),
    IRIS_INCREASE(14, "光圈增");

    private final Integer code;
    private final String desc;


    public static CameraEnum getByCode(Integer code) {
        for (CameraEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
