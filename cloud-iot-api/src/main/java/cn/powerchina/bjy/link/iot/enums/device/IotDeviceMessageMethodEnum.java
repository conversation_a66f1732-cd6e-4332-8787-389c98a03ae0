package cn.powerchina.bjy.link.iot.enums.device;

import cn.hutool.core.util.ArrayUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Set;

/**
 * IoT 设备消息的方法枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum IotDeviceMessageMethodEnum {

    // ========== 设备状态 ==========

    // TODO @芋艿：要合并下；thing.state.update
    STATE_ONLINE("thing.state.online", "设备上线", true),
    STATE_OFFLINE("thing.state.offline", "设备下线", true),

    STATE_UPDATE("status.update", "设备状态更新", true),

    // ========== 设备属性 ==========
    // 可参考：https://help.aliyun.com/zh/iot/user-guide/device-properties-events-and-services

    PROPERTIES_REPORT("properties.report", "属性上报", true),
    PROPERTIES_SET("properties.set", "属性设置", false),
    PROPERTIES_GET_REQUEST("properties.get.request", "查询设备属性", false),
    PROPERTIES_GET_RESPONSE("properties.get.response", "查询设备属性", false),

    // ========== 设备事件 ==========

    EVENT_REPORT("events.report", "事件上报", true),

    // ========== 设备服务调用 ==========

    SERVICE_INVOKE("commands.request", "服务调用", false),

    COMMANDS_RESPONSE("commands.response", "命令响应", false),

    DEVICE_REGISTER("device.register", "设备注册", false),

    GATEWAY_REGISTER("gateway.register", "网关设备注册", false),

    // ========== 设备配置 ==========
    // 可参考：https://help.aliyun.com/zh/iot/user-guide/remote-configuration-1

    CONFIG_PUSH("thing.config.push", "配置推送", true),

    ;

    public static final String[] ARRAYS = Arrays.stream(values()).map(IotDeviceMessageMethodEnum::getMethod)
            .toArray(String[]::new);

    /**
     * 不进行 reply 回复的方法集合
     */
    public static final Set<String> REPLY_DISABLED = Set.of(STATE_ONLINE.getMethod(), STATE_OFFLINE.getMethod());

    private final String method;

    private final String name;

    private final Boolean upstream;


    public static IotDeviceMessageMethodEnum of(String method) {
        return ArrayUtil.firstMatch(item -> item.getMethod().equals(method),
                IotDeviceMessageMethodEnum.values());
    }

    public static boolean isReplyDisabled(String method) {
        return REPLY_DISABLED.contains(method);
    }

    public static IotDeviceMessageMethodEnum getEnumByMethod(String method) {
        for (IotDeviceMessageMethodEnum methodEnum : IotDeviceMessageMethodEnum.values()) {
            if (method.equals(methodEnum.getMethod())) {
                return methodEnum;
            }
        }
        return null;
    }

}
