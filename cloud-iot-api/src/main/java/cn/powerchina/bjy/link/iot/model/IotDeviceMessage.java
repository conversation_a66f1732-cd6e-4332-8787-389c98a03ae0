package cn.powerchina.bjy.link.iot.model;

import cn.hutool.core.map.MapUtil;
import cn.powerchina.bjy.cloud.framework.common.exception.enums.GlobalErrorCodeConstants;
import cn.powerchina.bjy.link.iot.enums.device.IotDeviceMessageMethodEnum;
import cn.powerchina.bjy.link.iot.enums.device.IotDeviceStateEnum;
import cn.powerchina.bjy.link.iot.utils.IotDeviceMessageUtils;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * IoT 设备消息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class IotDeviceMessage {


    /**
     * 应用的设备消息 Topic，由 iot-gateway 发给 iot-biz 进行消费
     */
    public static final String TOPIC_IOT_DEVICE_MESSAGE = "topic_iot_device_message";
    public static final String GROUP_IOT_DEVICE_MESSAGE = "group_iot_device_message";

    /**
     * 【消息总线】设备消息 Topic，由 iot-biz 发送给 iot-gateway 进行消费
     */
    public static final String TOPIC_GATEWAY_DEVICE_MESSAGE = "topic_gateway_device_message";
    public static final String MESSAGE_BUS_GATEWAY_DEVICE_MESSAGE_TOPIC = TOPIC_IOT_DEVICE_MESSAGE + "_%s";


    /**
     * 消息编号
     * <p>
     * 由后端生成，通过 {@link IotDeviceMessageUtils#generateMessageId()}
     */
    private String id;
    /**
     * 上报时间
     * <p>
     * 为空，设置当前时间
     */
    private Long reportTime;

    /**
     * 设备编号
     */
    private String deviceCode;
    private String subDeviceCode;
    private String serviceId = "default";

    /**
     * 唯一标识
     */
    private String thingIdentity;

    /**
     * 事件类型
     */
    private String eventType;


    /**
     * 请求编号
     * <p>
     * 由设备生成，对应阿里云 IoT 的 Alink 协议中的 id、华为云 IoTDA 协议的 request_id
     */
    private String requestId;
    /**
     * 请求方法
     * <p>
     * 枚举 {@link IotDeviceMessageMethodEnum}
     * 例如说：thing.property.report 属性上报
     */
    private String method;
    /**
     * 请求参数
     * <p>
     * 例如说：指令的参数、事件上报的 params
     */
    private Object params;

    /**
     * 上报的属性
     */
    private List<ServiceProperty> services;

    /**
     * 相应结果
     */
    private Integer resultCode;

    /**
     * 设备的状态
     */
    private Integer status;

    private String subDeviceId;

    // ========== 基础方法：只传递"codec（编解码）字段" ==========

    public static IotDeviceMessage requestOf(String method) {
        return requestOf(null, method, null);
    }

    public static IotDeviceMessage requestOf(String method, Object params) {
        return requestOf(null, method, params);
    }

    public static IotDeviceMessage requestOf(String requestId, String method, Object params) {
        return of(requestId, method, params, null, null, null);
    }

    public static IotDeviceMessage replyOf(String requestId, String method,
                                           Object data, Integer code, String msg) {
        if (code == null) {
            code = GlobalErrorCodeConstants.SUCCESS.getCode();
            msg = GlobalErrorCodeConstants.SUCCESS.getMsg();
        }
        return of(requestId, method, null, data, code, msg);
    }

    public static IotDeviceMessage of(String requestId, String method,
                                      Object params, Object data, Integer code, String msg) {
        // 通用参数
        IotDeviceMessage message = new IotDeviceMessage()
                .setId(IotDeviceMessageUtils.generateMessageId()).setReportTime(System.currentTimeMillis());
        // 当前参数
        message.setRequestId(requestId).setMethod(method).setParams(params);
        return message;
    }

    // ========== 核心方法：在 of 基础方法之上，添加对应 method ==========

    public static IotDeviceMessage buildStateUpdateOnline() {
        return requestOf(IotDeviceMessageMethodEnum.STATE_UPDATE.getMethod(),
                MapUtil.of("state", IotDeviceStateEnum.ONLINE.getState()));
    }

    public static IotDeviceMessage buildStateOffline() {
        return requestOf(IotDeviceMessageMethodEnum.STATE_UPDATE.getMethod(),
                MapUtil.of("state", IotDeviceStateEnum.OFFLINE.getState()));
    }
}