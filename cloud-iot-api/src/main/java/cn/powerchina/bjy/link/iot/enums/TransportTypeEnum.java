package cn.powerchina.bjy.link.iot.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 数据转发类型枚举
 *
 * <AUTHOR>
 **/
@Getter
@AllArgsConstructor
public enum TransportTypeEnum {

    HTTP(1, "HTTP推送"),
    MQTT(2, "MQTT推送");

    private final Integer type;
    private final String desc;

    /**
     * 根据类型获取描述
     *
     * @param type 类型值
     * @return 描述
     */
    public static String getDescByType(Integer type) {
        for (TransportTypeEnum typeEnum : TransportTypeEnum.values()) {
            if (typeEnum.getType().equals(type)) {
                return typeEnum.getDesc();
            }
        }
        return null;
    }
}
