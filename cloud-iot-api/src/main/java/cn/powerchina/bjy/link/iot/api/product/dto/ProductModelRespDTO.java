package cn.powerchina.bjy.link.iot.api.product.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: 描述
 * @Author: yhx
 * @CreateDate: 2024/9/4
 */
@Schema(description = "管理后台 - 产品物模型信息 Response VO")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProductModelRespDTO {

    @Schema(description = "物模型标识符")
    private String thingIdentity;

    @Schema(description = "物模型名称")
    private String thingName;

    @Schema(description = "物模型类型，1-属性；2-服务；3-事件；")
    private Integer thingType;

    @Schema(description = "数据类型（integer、decimal、string、bool、array、enum）")
    private String datatype;

    @Schema(description = "读写类型，thing_type为1时必填")
    private Integer readWriteType;
}
