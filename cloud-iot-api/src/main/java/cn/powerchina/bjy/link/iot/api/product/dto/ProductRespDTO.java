package cn.powerchina.bjy.link.iot.api.product.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: 描述
 * @Author: yhx
 * @CreateDate: 2024/9/3
 */
@Schema(description = "管理后台 - 产品信息 Response VO")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProductRespDTO {

    /**
     * 产品编码
     */
    private String productCode;
    /**
     * 产品名称
     */
    private String productName;
    /**
     * 产品型号
     */
    private String productModel;
    /**
     * 厂商
     */
    private String firmName;
    /**
     * 节点类型（0直连，1网关，2网关子设备）
     */
    private Integer nodeType;

    /**
     * 产品描述
     */
    private String description;
}
