package cn.powerchina.bjy.link.iot.api.datapermissions;

import cn.powerchina.bjy.link.iot.api.datapermissions.dto.DataPermissionsDto;
import cn.powerchina.bjy.link.iot.api.devicegroup.dto.DeviceRespDTO;
import cn.powerchina.bjy.link.iot.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Collection;
import java.util.List;
import java.util.Map;

@FeignClient(name = ApiConstants.NAME)
@Tag(name = "RPC 服务 - 权限信息")
public interface DatapermissionsApi {

    String PREFIX = ApiConstants.PREFIX + "/data/permissions";

    @PostMapping(PREFIX + "/create")
    @Operation(summary = "创建数据权限")
    Boolean createDataPermissions(@RequestBody DataPermissionsDto createReqVO);


}