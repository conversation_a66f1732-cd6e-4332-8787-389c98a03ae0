package cn.powerchina.bjy.link.iot.api.devicegroup;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.link.iot.api.devicegroup.dto.DeviceGroupRespDTO;
import cn.powerchina.bjy.link.iot.api.devicegroup.dto.DeviceRespDTO;
import cn.powerchina.bjy.link.iot.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

@FeignClient(name = ApiConstants.NAME)
@Tag(name = "RPC 服务 - 设备分组")
public interface DeviceGroupApi {

    String PREFIX = ApiConstants.PREFIX + "/deviceGroup";

    @GetMapping(PREFIX + "/getByResourceSpaceId")
    @Operation(summary = "获得资源空间对应的设备分组")
    @Parameter(name = "resourceSpaceId", description = "资源空间id", required = true)
    CommonResult<List<DeviceGroupRespDTO>> getDeviceGroupListByResourceSpaceId(@RequestParam("resourceSpaceId") Long resourceSpaceId);

    @GetMapping(PREFIX + "/getDevice")
    @Operation(summary = "获得设备分组对应的设备")
    @Parameter(name = "deviceGroupId", description = "设备分组id", required = true)
    CommonResult<List<DeviceRespDTO>> getDeviceListByDeviceGroupId(@RequestParam("deviceGroupId") Long deviceGroupId);


    @GetMapping(PREFIX + "/batch/getDevice")
    @Operation(summary = "批量获得设备分组对应的设备")
    @Parameter(name = "deviceGroupIds", description = "设备分组id集合", required = true)
    CommonResult<Map<Long, List<DeviceRespDTO>>> batchGetDeviceListByDeviceGroupIds(@RequestParam("deviceGroupIds") List<Long> deviceGroupIds);
}