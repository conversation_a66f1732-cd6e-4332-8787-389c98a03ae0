package cn.powerchina.bjy.link.iot.api.edgegateway;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.link.iot.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * @Description: 描述
 * @Author: yhx
 * @CreateDate: 2024/9/23
 */
@FeignClient(name = ApiConstants.NAME)
@Tag(name = "RPC 服务 - 边缘网关信息")
public interface EdgeGatewayApi {

    String PREFIX = ApiConstants.PREFIX + "/xxljob/edgeGateway";

    @GetMapping(PREFIX + "/detect")
    @Operation(summary = "检测边缘网关在线离线")
    CommonResult<Boolean> detectEdgeGatewayOnlineStatus();
}
