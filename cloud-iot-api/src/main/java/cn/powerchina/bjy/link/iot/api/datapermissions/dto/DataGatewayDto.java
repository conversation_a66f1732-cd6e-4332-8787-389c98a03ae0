package cn.powerchina.bjy.link.iot.api.datapermissions.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 数据权限新增/修改 Request VO")
@Data
public class DataGatewayDto {


    @Schema(description = "边缘计算ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "27371")
    @NotNull(message = "边缘计算ID")
    private String edgeId;

    @Schema(description = "边缘实例集合", requiredMode = Schema.RequiredMode.REQUIRED, example = "27371")
    @NotNull(message = "边缘实例ID集合")
    private List<String> gatewayIds;


}
