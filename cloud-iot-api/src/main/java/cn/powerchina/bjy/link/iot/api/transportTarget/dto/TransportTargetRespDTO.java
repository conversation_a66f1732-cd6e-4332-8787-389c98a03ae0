package cn.powerchina.bjy.link.iot.api.transportTarget.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * @Description: 转发目标
 * @Author: handl
 * @CreateDate: 2025/7/11
 */
@Schema(description = "管理后台 - 转发目标 Response VO")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TransportTargetRespDTO {

    @Schema(description = "目标名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    private String name;

    @Schema(description = "转发topic")
    private String topic;

    @Schema(description = "描述")
    private String remark;

}
