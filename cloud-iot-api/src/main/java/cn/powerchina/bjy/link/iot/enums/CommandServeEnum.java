package cn.powerchina.bjy.link.iot.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Description: 服务指令下发
 * @Author: yhx
 * @CreateDate: 2024/9/14
 */
@Getter
@AllArgsConstructor
public enum CommandServeEnum {

    CHANNEL_VALUE("channel_value", "channels", "基康采集通道数据"),
    SET_DATETIME("set_datetime", "datetime", "基康设置MCU采集仪时间"),
    CAMERA_MOVE("camera_move", "move", "摄像头方向控制"),
    STREAM_START("stream_start", "", "发起设备实时推流"),
    STREAM_TOUCH("stream_touch", "", "直接保活"),
    STREAM_STOP("stream_stop", "", "停止设备实时推流"),
    PTZ_START("ptz_start", "", "云台控制开始"),
    ;

    private final String thingIdentity;
    private final String thingParam;
    private final String desc;

}
