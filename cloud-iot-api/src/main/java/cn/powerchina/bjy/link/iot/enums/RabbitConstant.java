package cn.powerchina.bjy.link.iot.enums;

/**
 * 消息相关常量
 *
 * <AUTHOR>
 */
public class RabbitConstant {

    private RabbitConstant() {
        throw new IllegalStateException(ExceptionConstant.UTILITY_CLASS);
    }

    // Arguments
    public static final String MESSAGE_TTL = "x-message-ttl";
    public static final String AUTO_DELETE = "x-auto-delete";

    //Sync
    public static String TOPIC_EXCHANGE_SYNC = "edge.e.sync";
    public static final String ROUTING_SYNC_UP_PREFIX = "edge.r.sync.up.";
    public static String QUEUE_SYNC_UP = "edge.q.sync.up";
    public static final String ROUTING_SYNC_DOWN_PREFIX = "edge.r.sync.down.";
    public static String QUEUE_SYNC_DOWN_PREFIX = "edge.q.sync.down.";
    /**
     * 配置下发routing
     * 场景一：边缘网关一键注册用
     */
    public static final String ROUTING_SYNC_CONFIG_PREFIX = "edge.r.sync.config.";
    /**
     * 配置下发返回队列
     * 场景一：边缘网关一键注册
     */
    public static String QUEUE_SYNC_CONFIG = "edge.q.sync.config";

    //online
    public static String TOPIC_EXCHANGE_ONLINE = "edge.e.online";
    public static final String ROUTING_DEVICE_ONLINE_CHECK_PREFIX = "edge.r.online.check.";
    public static String QUEUE_DEVICE_ONLINE_CHECK_PRE = "edge.q.online.check.";
    public static final String ROUTING_DEVICE_ONLINE_STATUS_PREFIX = "edge.r.online.status.";
    public static String QUEUE_DEVICE_ONLINE_STATUS = "edge.q.online.status";

    //Command
    public static String TOPIC_EXCHANGE_COMMAND = "edge.e.command";
    public static final String ROUTING_DEVICE_COMMAND_PREFIX = "edge.r.command.device.";
    public static String QUEUE_DEVICE_COMMAND_PREFIX = "edge.q.command.device.";
    public static final String ROUTING_DEVICE_COMMAND_RESULT_PREFIX = "edge.r.command.result.device.";
    public static String QUEUE_DEVICE_COMMAND_RESULT = "edge.q.command.result.device";

    //Property
    /**
     * 物模型属性exchange
     */
    public static String TOPIC_EXCHANGE_PROPERTY = "edge.e.property";
    public static final String ROUTING_DEVICE_PROPERTY_PREFIX = "edge.r.property.device.";
    public static String QUEUE_DEVICE_PROPERTY_PREFIX = "edge.q.property.device.";
    /**
     * 物模型属性上报routing
     */
    public static final String ROUTING_DEVICE_PROPERTY_VALUE_PREFIX = "edge.r.property.value.device.";
    /**
     * 物模型属性上报队列
     */
    public static String QUEUE_DEVICE_PROPERTY_VALUE = "edge.q.property.value.device";

    //Event
    public static String TOPIC_EXCHANGE_EVENT = "edge.e.event";
    public static final String ROUTING_DEVICE_EVENT_PREFIX = "edge.r.event.device.";
    public static String QUEUE_DEVICE_EVENT = "edge.q.event.device";

}
