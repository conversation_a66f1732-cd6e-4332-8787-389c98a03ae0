package cn.powerchina.bjy.link.iot.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 注册状态
 * 0未注册，1已注册
 *
 * <AUTHOR>
 **/
@Getter
@AllArgsConstructor
public enum LinkStateEnum {

    NO_ACTIVE(0, "未激活"),
    OFF_LINE(1, "离线"),
    ON_LINE(2, "在线"),
    IS_DISABLE(3, "禁用");

    private static final Map<Integer, String> ENUM_MAP= Arrays.stream(LinkStateEnum.values()).collect(Collectors.toMap(LinkStateEnum::getType, LinkStateEnum::getDesc));

    public static String getDescByType(Integer type){
        return ENUM_MAP.get(type);
    }

    private final Integer type;
    private final String desc;
}
