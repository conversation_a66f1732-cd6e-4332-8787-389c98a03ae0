package cn.powerchina.bjy.link.iot.model;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Description: 设备状态转发
 * @Author: dzj
 * @CreateDate: 2024/10/16
 */
@Data
public class DeviceCheckOnlineModel implements MessageToEdge, Serializable {

    /**
     * 设备编码
     */
    private String deviceCode;

    /**
     * 检测结果，1：正常，0：失败
     */
    private Integer checkResult;

    /**
     * 子设备状态
     */
    private List<ChildDevice> childDeviceStatus;

    @Data
    public static class ChildDevice {

        /**
         * 设备编码
         */
        private String deviceCode;

        /**
         * 检测结果，1：正常，0：失败
         */
        private Integer checkResult;
    }

}
