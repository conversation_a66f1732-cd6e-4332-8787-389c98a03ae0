package cn.powerchina.bjy.link.iot.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum DriveEnum {


    VIDEO("VIDEO", "视频驱动"),
    MODBUS("MODBUS", "MODBUS驱动");

    private static final Map<String, String> ENUM_MAP = Arrays.stream(DriveEnum.values()).collect(Collectors.toMap(DriveEnum::getType, DriveEnum::getDesc));

    public static String getDescByType(String type) {
        return ENUM_MAP.get(type);
    }

    private final String type;
    private final String desc;
}
