package cn.powerchina.bjy.link.iot.api.product;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.link.iot.api.product.dto.ProductModelRespDTO;
import cn.powerchina.bjy.link.iot.api.product.dto.ProductRespDTO;
import cn.powerchina.bjy.link.iot.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

@FeignClient(name = ApiConstants.NAME)
@Tag(name = "RPC 服务 - 产品信息")
public interface ProductApi {

    String PREFIX = ApiConstants.PREFIX + "/product";

    @GetMapping(PREFIX + "/getProduct")
    @Operation(summary = "获得产品信息")
    @Parameter(name = "productCode", description = "产品编码", required = true)
    CommonResult<ProductRespDTO> getProductByProductCode(@RequestParam("productCode") String productCode);


    @GetMapping(PREFIX + "/batch/getProduct")
    @Operation(summary = "批量获得产品信息")
    @Parameter(name = "productCodes", description = "产品编码集合", required = true)
    CommonResult<Map<String, ProductRespDTO>> batchGetProductByProductCodes(@RequestParam("productCodes") List<String> productCodes);

    @GetMapping(PREFIX + "/getProductModel")
    @Operation(summary = "获得产品物模型信息")
    @Parameter(name = "productCode", description = "产品编码", required = true)
    @Parameter(name = "thingType", description = "物模型类型，1-属性；2-服务；3-事件；", required = false)
    CommonResult<List<ProductModelRespDTO>> getProductModelByProductCode(@RequestParam("productCode") String productCode,
                                                                         @RequestParam(value = "thingType", required = false) Integer thingType);

    @GetMapping(PREFIX + "/batch/getProductModel")
    @Operation(summary = "获得产品物模型信息")
    @Parameter(name = "productCode", description = "产品编码", required = true)
    @Parameter(name = "thingType", description = "物模型类型，1-属性；2-服务；3-事件；", required = false)
    CommonResult<Map<String, List<ProductModelRespDTO>>> batchGetProductModelByProductCode(@RequestParam("productCodes") List<String> productCodes,
                                                                                           @RequestParam(value = "thingType", required = false) Integer thingType);
}