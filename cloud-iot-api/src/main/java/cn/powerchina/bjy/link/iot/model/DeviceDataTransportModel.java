package cn.powerchina.bjy.link.iot.model;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Description: 设备数据转发
 * @Author: yhx
 * @CreateDate: 2024/9/18
 */
@Data
public class DeviceDataTransportModel implements MessageToEdge, Serializable {

    /**
     * 设备编码
     */
    private String deviceCode;

    /**
     * 采集时刻时间
     */
    private Long currentTime;

    /**
     * MCU通道编码
     */
    private String mcuChannel;

    /**
     * 物模型采集值
     */
    private List<DeviceData> deviceDataList;

    @Data
    public static class DeviceData {

        /**
         * 物模型标识符
         */
        private String thingIdentity;

        /**
         * 物模型采集值
         */
        private String thingValue;

    }
}
