package cn.powerchina.bjy.link.iot.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: 描述
 * @Author: yhx
 * @CreateDate: 2024/9/29
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeviceTransportModel {

    /**
     * 设备编码
     */
    private String deviceCode;

    /**
     * MCU通道编码
     */
    private String mcuChannel;

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 物模型标识符
     */
    private String thingIdentity;

    /**
     * 物模型采集值
     */
    private String thingValue;

}
