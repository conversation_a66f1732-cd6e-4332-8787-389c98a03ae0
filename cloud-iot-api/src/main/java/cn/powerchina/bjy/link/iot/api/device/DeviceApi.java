package cn.powerchina.bjy.link.iot.api.device;

import cn.powerchina.bjy.link.iot.api.devicegroup.dto.DeviceRespDTO;
import cn.powerchina.bjy.link.iot.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Collection;
import java.util.List;
import java.util.Map;

@FeignClient(name = ApiConstants.NAME)
@Tag(name = "RPC 服务 - 设备信息")
public interface DeviceApi {

    String PREFIX = ApiConstants.PREFIX + "/deviceGroup";

    @GetMapping(PREFIX + "/loadDevices")
    @Operation(summary = "查询设备")
    @Parameter(name = "deviceIds", description = "设备id", required = true)
    Map<Long, DeviceRespDTO> loadDevices(@RequestParam("deviceIds") Collection<Long> deviceIds);

    @GetMapping(PREFIX + "/loadChildDevices")
    @Operation(summary = "查询子设备")
    @Parameter(name = "parentCodes", description = "父设备code", required = true)
    List<DeviceRespDTO> loadChildDevices(@RequestParam("parentCodes") Collection<String> parentCodes);

}