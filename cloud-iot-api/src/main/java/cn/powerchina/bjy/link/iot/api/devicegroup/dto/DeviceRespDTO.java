package cn.powerchina.bjy.link.iot.api.devicegroup.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * @Description: 设备信息
 * @Author: yhx
 * @CreateDate: 2024/9/2
 */
@Schema(description = "管理后台 - 设备信息 Response VO")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DeviceRespDTO {

    /**
     * 设备主键id
     */
    private Long id;
    /**
     * 产品编码
     */
    private String productCode;
    /**
     * 设备名称
     */
    private String deviceName;
    /**
     * 设备编号
     */
    private String deviceCode;
    /**
     * 父设备号（网关）
     */
    private String parentCode;
    /**
     * 父设备名称
     */
    private String parentName;
    /**
     * 父设备产品编码
     */
    private String parentProductCode;
    /**
     * 父设备唯一标识
     */
    private String parentSerial;
    /**
     * 设备唯一标识
     */
    private String deviceSerial;
    /**
     * mcu通道号
     */
    private String mcuChannel;
    /**
     * 连接状态（0-离线；1-在线；）
     */
    private Integer linkState;
    /**
     * 节点类型(0直连，1网关，2网关子设备）
     */
    private Integer nodeType;
    /**
     * 最后上线时间
     */
    private LocalDateTime lastUpTime;
    /**
     * 注册时间
     */
    private LocalDateTime registerTime;
    /**
     * 备注
     */
    private String remark;
}

