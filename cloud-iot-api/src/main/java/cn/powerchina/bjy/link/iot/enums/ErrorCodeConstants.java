package cn.powerchina.bjy.link.iot.enums;

import cn.powerchina.bjy.cloud.framework.common.exception.ErrorCode;

/**
 * iot 错误码枚举类
 * <p>
 * iot 系统，使用 1-003-000-000 段
 */
public interface ErrorCodeConstants {

    // ========== 产品 1_003_000_000 ==========
    ErrorCode PRODUCT_NOT_EXISTS = new ErrorCode(1_003_000_000, "产品不存在");
    ErrorCode PRODUCT_REL_DEVICE = new ErrorCode(1_003_000_001, "产品已关联设备");
    ErrorCode PRODUCT_NAME_EXITS = new ErrorCode(1_003_000_002, "产品名称重复");
    ErrorCode PRODUCT_NODE_TYPE_UPDATE = new ErrorCode(1_003_000_003, "产品已关联设备，不允许修改节点类型");
    ErrorCode PRODUCT_TEMPLATE_CANNOT_MODIFY = new ErrorCode(1_003_000_004, "产品创建后，所属品类不能修改");
    ErrorCode PRODUCT_ATTRIBUTE_CANNOT_MODIFY = new ErrorCode(1_003_000_005, "产品相关属性不能修改");
    ErrorCode PRODUCT_DEVICE_LIMIT_LESS_USED_QUOTA = new ErrorCode(1_003_000_006, "产品设备限额不能比已使用额度小");
    ErrorCode PRODUCT_USED_QUOTA_MORE_DEVICE_LIMIT = new ErrorCode(1_003_000_007, "产品已使用额度超过设备限额");
    // ========== 厂商 1_003_001_000 ==========
    ErrorCode PRODUCT_FIRM_NOT_EXISTS = new ErrorCode(1_003_001_000, "厂商不存在");

    // ========== 产品物模型 1_003_002_000 ==========
    ErrorCode PRODUCT_MODEL_NOT_EXISTS = new ErrorCode(1_003_002_000, "产品物模型不存在");
    ErrorCode PRODUCT_MODEL_NAME_EXISTS = new ErrorCode(1_003_002_001, "{}名称已存在");
    ErrorCode PRODUCT_MODEL_IDENTIFY_EXISTS = new ErrorCode(1_003_002_002, "{}标识符已存在");
    ErrorCode PRODUCT_MODEL_PARAM_NAME_EXISTS = new ErrorCode(1_003_002_003, "物模型参数名称重复");
    ErrorCode PRODUCT_MODEL_PARAM_IDENTIFY_EXISTS = new ErrorCode(1_003_002_004, "物模型参数标识符重复");
    ErrorCode PRODUCT_MODEL_TD_IDENTIFY_UPDATE = new ErrorCode(1_003_002_004, "时序数据库编辑标识符错误");
    ErrorCode PRODUCT_MODEL_TD_IDENTIFY_CREATE = new ErrorCode(1_003_002_004, "时序数据库创建标识符失败");
    ErrorCode PRODUCT_MODEL_TD_CREATE = new ErrorCode(1_003_002_004, "创建时序数据库超级表失败");
    ErrorCode PRODUCT_MODEL_TD_INSERT_DATA = new ErrorCode(1_003_002_004, "保存上报数据失败");
    ErrorCode PRODUCT_MODEL_TD_DATA_EXISTS = new ErrorCode(1_003_002_004, "已存在上报数据，不允许修改唯一标识和数据类型");

    // ========== 项目 1_003_003_000 ==========
    ErrorCode PROJECT_NOT_EXISTS = new ErrorCode(1_003_003_000, "项目不存在");

    // ========== 设备 1_003_004_000 ==========
    ErrorCode DEVICE_NOT_EXISTS = new ErrorCode(1_003_004_000, "设备不存在");
    ErrorCode DEVICE_ORDERCOLUMN_NOT_EXISTS = new ErrorCode(1_003_004_001, "排序字段不存在");
    ErrorCode DEVICE_CHANNEL_IS_NULL = new ErrorCode(1_003_004_002, "设备未设置通道");
    ErrorCode DEVICE_SERIAL_EXISTS = new ErrorCode(1_003_004_003, "设备唯一标识已存在");
    ErrorCode DEVICE_NOT_EXISTS_OR_IS_DEL = new ErrorCode(1_003_004_004, "设备不存在或已删除");
    ErrorCode DEVICE_HAVE_SUB = new ErrorCode(1_003_004_005, "网关设备已挂载子设备，不能删除");
    ErrorCode DEVICE_SHADOW_UPDATE = new ErrorCode(1_003_004_006, "更新设备影子失败");
    ErrorCode DEVICE_STATUS_EXISTS = new ErrorCode(1_003_004_007, "设备状态不是未激活");
    ErrorCode DEVICE_REGISTER_EXISTS = new ErrorCode(1_003_004_008, "设备已存在，不能重复注册");
    ErrorCode DEVICE_NOT_REGISTER_EXISTS = new ErrorCode(1_003_004_009, "设备未预注册");

    // ========== 设备事件日志 1_003_005_000 ==========
    ErrorCode DEVICE_EVENT_LOG_NOT_EXISTS = new ErrorCode(1_003_005_000, "设备事件日志不存在");

    // ========== 设备日志 1_003_006_000 ==========
    ErrorCode DEVICE_LOG_NOT_EXISTS = new ErrorCode(1_003_006_000, "设备日志不存在");

    // ========== 设备属性日志 1_003_007_000 ==========
    ErrorCode DEVICE_PROPERTY_LOG_NOT_EXISTS = new ErrorCode(1_003_007_000, "设备属性日志不存在");


    // ========== 通道 1_003_008_000 ==========
    ErrorCode EDGE_CHANNEL_NOT_EXISTS = new ErrorCode(1_003_008_000, "通道不存在");

    // ========== 边缘网关采集器关联关系 1_003_009_000 ==========
    ErrorCode EDGE_COLLECTOR_NOT_EXISTS = new ErrorCode(1_003_009_000, "边缘网关采集器关联关系不存在");

    // ========== 边缘网关 1_003_010_000 ==========
    ErrorCode EDGE_GATEWAY_NOT_EXISTS = new ErrorCode(1_003_010_000, "边缘网关不存在");

    ErrorCode EDGE_GATEWAY_DEVICE_EXISTS = new ErrorCode(1_003_010_001, "边缘网关下存在设备，无法删除");
    ErrorCode EDGE_CHANNEL_NAME_EXISTS = new ErrorCode(1_003_010_002, "通道已存在");
    ErrorCode EDGE_GATEWAY_SLAVE_ID_EXISTS = new ErrorCode(1_003_010_003, "从站号已存在");
    ErrorCode EDGE_GATEWAY_ALL_REGISTER = new ErrorCode(1_003_010_004, "已存在全局注册任务");
    ErrorCode EDGE_GATEWAY_SUB_REGISTER = new ErrorCode(1_003_010_005, "网关设备注册中");
    ErrorCode EDGE_GATEWAY_SLAVE_ID_REQUIRE = new ErrorCode(1_003_010_006, "设备id必填");
    ErrorCode EDGE_GATEWAY_SUB_MCU_REQUIRE = new ErrorCode(1_003_010_007, "MCU通道号必填");
    ErrorCode EDGE_GATEWAY_REQUIRE = new ErrorCode(1_003_010_008, "所属网关必填");
    ErrorCode EDGE_GATEWAY_SUB_MCU_EXISTS = new ErrorCode(1_003_010_009, "MCU通道已存在");
    /**
     * 获取锁失败
     */
    ErrorCode EDGE_GATEWAY_LOCK_FAILED = new ErrorCode(1_003_010_006, "请稍后重试");

    // ========== 指令下发操作记录 1_003_011_000 ==========
    ErrorCode INSTRUCTION_DOWN_LOG_NOT_EXISTS = new ErrorCode(1_003_011_000, "指令下发操作记录不存在");
    ErrorCode VIDEO_STREAM_INACTIVE_SKIP_KEEPALIVE = new ErrorCode(1_003_011_001, "视频设备未开始推流，无需保活");
    ErrorCode VIDEO_STREAM_INACTIVE_SKIP_STOP = new ErrorCode(1_003_011_002, "视频设备未开始推流，无需停止");

    // ========== 数据转发规则 1_003_012_000 ==========
    ErrorCode TRANSPORT_RULE_NOT_EXISTS = new ErrorCode(1_003_012_000, "数据转发规则不存在");
    ErrorCode TRANSPORT_EXCEPTIOM = new ErrorCode(1_003_012_000, "数据转发异常");

    // ========== 转发规则对应的设备 1_003_013_000 ==========
    ErrorCode TRANSPORT_DETAIL_NOT_EXISTS = new ErrorCode(1_003_013_000, "转发规则对应的产品或设备不存在");
    ErrorCode TRANSPORT_DEVICE_NOT_EXISTS = new ErrorCode(1_003_013_001, "转发规则对应设备不存在");
    ErrorCode TRANSPORT_SOURCE_NOT_EXISTS = new ErrorCode(1_003_013_002, "转发规则数据源不存在");
    ErrorCode TRANSPORT_SOURCE_PRODUCT_EXISTS = new ErrorCode(1_003_013_003, "产品已存在");
    ErrorCode TRANSPORT_SOURCE_DEVICE_EXISTS = new ErrorCode(1_003_013_004, "设备已存在");
    ErrorCode TRANSPORT_TARGET_NOT_EXISTS = new ErrorCode(1_003_013_005, "转发规则-转发目标不存在");
    ErrorCode TRANSPORT_TARGET_NOT_TEN = new ErrorCode(1_003_013_006, "最多添加10个转发目标");
    ErrorCode TRANSPORT_TARGET_NAME_EXISTS = new ErrorCode(1_003_013_005, "转发目标名称已存在");
    // ========== 资源空间 1_003_014_000 ==========
    ErrorCode RESOURCE_SPACE_NOT_EXISTS = new ErrorCode(1_003_014_000, "资源空间不存在");
    ErrorCode RESOURCE_SPACE_NAME_EXISTS = new ErrorCode(1_003_014_001, "资源空间名称已存在");
    ErrorCode RESOURCE_SPACE_NAME_REFERENCE_EXISTS = new ErrorCode(1_003_014_002, "资源空间下存在资源，不允许删除");

    // ========== 设备分组 1_003_015_000==========
    ErrorCode DEVICE_GROUP_NOT_EXISTS = new ErrorCode(1_003_015_000, "设备分组不存在");
    ErrorCode DEVICE_GROUP_BRANCH_EXISTS = new ErrorCode(1_003_015_001, "当前节点存在子节点，不允许删除");
    ErrorCode DEVICE_GROUP_DATA_EXISTS = new ErrorCode(1_003_015_002, "当前节点存在数据，不允许删除");
    // ========== 设备分组明细  1_003_016_000==========
    ErrorCode DEVICE_GROUP_DETAIL_NOT_EXISTS = new ErrorCode(1_003_016_000, "设备分组明细不存在");

    // ========== 设备消息数按日统计  1_003_017_000==========
    ErrorCode MESSAGE_STATISTIC_DAY_NOT_EXISTS = new ErrorCode(1_003_017_000, "设备消息数按日统计不存在");
    ErrorCode MESSAGE_STATISTIC_DAY_INTERVAL_ERROR = new ErrorCode(1_003_017_001, "时间间隔最大不超过{}天，结束时间-开始时间<={}");

    // ========== 物模型 1_003_018_000 ==========
    ErrorCode CATEGORIZE_EXITS_CHILDREN = new ErrorCode(1_003_018_000, "存在二级分类，无法删除");
    ErrorCode CATEGORIZE_EXITS_MODEL= new ErrorCode(1_003_018_001, "存在物模板，无法删除");
    ErrorCode CATEGORIZE_NOT_FOUND = new ErrorCode(1_003_018_002, "当前分类不存在");

    ErrorCode CATEGORIZE_PARENT_ERROR = new ErrorCode(1_003_018_003, "不能设置自己为父分类");

    ErrorCode CATEGORIZE_PARENT_NOT_EXITS = new ErrorCode(1_003_018_004, "父级分类不存在");

    ErrorCode CATEGORIZE_PARENT_IS_CHILD = new ErrorCode(1_003_018_005, "不能设置自己的子分类为父分类");

    ErrorCode CATEGORIZE_NAME_DUPLICATE = new ErrorCode(1_003_018_006, "已经存在该名字的分类");

    // ========== 物模型 1_003_019_000 ==========

    ErrorCode TEMPLATE_NAME_EXITS = new ErrorCode(1_003_019_000, "物模板名称重复");

    ErrorCode TEMPLATE_NOT_EXISTS = new ErrorCode(1_003_019_001, "物模板不存在");

    ErrorCode TEMPLATE_IS_EXISTS = new ErrorCode(1_003_019_002, "物模板已被引用，无法删除");

    // ========== 物模型 1_003_020_000 ==========

    ErrorCode TEMPLATE_DETAILS_NAME_EXITS = new ErrorCode(1_003_020_000, "名称重复");

    ErrorCode TEMPLATE_DETAILS_TEMPLATEIDENTITY_EXITS = new ErrorCode(1_003_020_000, "物模板标识符名称重复");

    ErrorCode TEMPLATE_DETAILS_NOT_EXISTS = new ErrorCode(1_003_020_001, "物模板明细不存在");

    // ========== 数据权限 1_003_021_000==========
    ErrorCode DATA_PERMISSIONS_NOT_EXISTS = new ErrorCode(1_003_021_000, "数据权限不存在");

    // ========== 场景规则 1_003_021_000 ==========
    ErrorCode SCENE_RULE_NOT_EXISTS = new ErrorCode(1_003_021_000, "场景规则不存在");
    ErrorCode SCENE_RULE_NO_PERMISSION = new ErrorCode(1_003_021_000, "无权限操作该场景规则");
    ErrorCode SCENE_RULE_USING = new ErrorCode(1_003_021_000, "场景规则被引用，无法删除");
    ErrorCode SCENE_RULE_NO_STATUS = new ErrorCode(1_003_021_000, "被禁用的场景无法执行");
    // ========== 场景规则触发条件 1_003_021_001 ==========
    ErrorCode SCENE_RULE_TRIGGER_NOT_EXISTS = new ErrorCode(1_003_021_001, "场景规则触发条件不存在");
    // ========== 场景规则执行动作 1_003_021_002 ==========
    ErrorCode SCENE_RULE_ACTION_NOT_EXISTS = new ErrorCode(1_003_021_002, "场景规则执行动作不存在");
    // ========== 场景规则定时触发 1_003_021_003 ==========
    ErrorCode SCENE_RULE_TIME_TRIGGER_NOT_EXISTS = new ErrorCode(1_003_021_003, "场景规则定时触发不存在");
    ErrorCode SCENE_RULE_TIME_TRIGGER_ERROR = new ErrorCode(1_003_021_003, "场景规则定时触发创建xxjob失败");
    // ========== 场景规则告警记录 1_003_021_004 ==========
    ErrorCode SCENE_ALARM_RECORD_NOT_EXISTS = new ErrorCode(1_003_021_004, "场景规则告警记录不存在");
    // ========== 场景规则告警模板 1_003_021_005 ==========
    ErrorCode ALARM_TEMPLATE_NOT_EXISTS = new ErrorCode(1_003_021_005, "场景规则告警模板不存在");
    // ========== 场景规则告警通知记录 1_003_021_006 ==========
    ErrorCode NOTIFICATION_METHOD_NOT_EXISTS = new ErrorCode(1_003_021_006, "场景规则告警通知记录不存在");
    ErrorCode NOTIFICATION_ACCOUNT_NOT_EXISTS = new ErrorCode(1_003_021_007, "场景规则告警通知账号不存在");
    ErrorCode NOTIFICATION_CONTENT_NOT_EXISTS = new ErrorCode(1_003_021_008, "场景规则告警通知内容不存在");
    ErrorCode SCENE_RULE_ALARM_NOT_EXISTS = new ErrorCode(1_003_021_009, "告警状态不是触发状态，无法处理");
    ErrorCode SCENE_RULE_ALARM_NOT_VERIFY_EXISTS = new ErrorCode(1_003_021_010, "告警状态不是待验证状态，无法恢复");

    // ========== 用户信息
    ErrorCode USER_NOT_EXISTS = new ErrorCode(1_003_022_001, "用户信息不存在");
    ErrorCode USER_ADMIN_NAME_EXISTS = new ErrorCode(1_003_022_002, "用户名已经存在");
    ErrorCode USER_ADMIN_IPHONE_EXISTS = new ErrorCode(1_003_022_003, "手机号已经存在");
    ErrorCode USER_PASSWORD_INCONSISTENCY = new ErrorCode(1_003_022_004, "两次输入的密码不一致");
    ErrorCode USER_ADMIN_EMAIL_EXISTS = new ErrorCode(1_003_022_005, "邮箱已经存在");

    // ========== 应用管理认证 ==========
    ErrorCode APP_AUTH_NOT_EXISTS = new ErrorCode(1_003_023_001, "应用管理认证不存在");
    ErrorCode APP_AUTH_NAME_EXISTS = new ErrorCode(1_003_023_002, "应用名称已存在");

    // ========== 告警记录详情 ==========
    ErrorCode SCENE_ALARM_RECORD_DETAIL_NOT_EXISTS = new ErrorCode(1_003_024_001, "告警记录详情不存在");

    // ========== 消息配置 ==========
    ErrorCode NOTIFICATION_CONFIG_NOT_EXISTS = new ErrorCode(1_003_025_001, "消息配置不存在");
    ErrorCode NOTIFICATION_CONFIG_CONNECTION_FAILED = new ErrorCode(1_003_025_002, "邮箱连接失败，请修改后重试");

    // ========== 消息记录 ==========
    ErrorCode NOTIFICATION_RECORD_NOT_EXISTS = new ErrorCode(1_003_026_001, "消息记录不存在");
    // ========== 驱动 ==========
    ErrorCode DRIVE_NOT_EXISTS = new ErrorCode(1_003_027_001, "驱动不存在");

    // ========== mqtt认证 ==========
    ErrorCode MQTT_AUTH_NOT_EXISTS = new ErrorCode(1_003_028_001, "mqtt认证不存在");

    // ========== MQTT 通信相关 1-050-009-000 ==========
    ErrorCode MQTT_TOPIC_ILLEGAL = new ErrorCode(1_050_009_000, "topic illegal");

    // ========== IoT 数据桥梁 1-050-010-000 ==========
    ErrorCode DATA_BRIDGE_NOT_EXISTS = new ErrorCode(1_050_010_000, "IoT 数据桥梁不存在");
}
