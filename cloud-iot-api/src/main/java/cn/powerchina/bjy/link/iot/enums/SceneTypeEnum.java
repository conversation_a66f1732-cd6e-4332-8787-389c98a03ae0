package cn.powerchina.bjy.link.iot.enums;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;

/**
 * SceneTypeEnum
 *
 * <AUTHOR>
 **/
@Getter
@AllArgsConstructor
public enum SceneTypeEnum {

    PRODUCT("CP", "产品"),
    DEVICE("D", "设备"),
    CHANNEL("C", "通道"),
    TRANSPORT("T", "转发规则"),
    TRANSPORT_SOURCE("TS", "转发规则数据源"),
    RESOURCE_SPACE("RS", "转发规则数据源"),

    ;

    private final String prefix;
    private final String desc;

}
