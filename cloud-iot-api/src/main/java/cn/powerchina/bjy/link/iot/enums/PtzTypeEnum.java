package cn.powerchina.bjy.link.iot.enums;

public enum PtzTypeEnum {
    PTZ_MOVE("ptz_move", "摄像头移动");

    private final String code;
    private final String desc;
    
    PtzTypeEnum (String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDesc() {
        return desc;
    }
    
    public static PtzTypeEnum fromCode(String code) {
        for (PtzTypeEnum type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("No matching type for code: " + code);
    }

}
