package cn.powerchina.bjy.link.iot.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * ThingModeTypeEnum
 *
 * <AUTHOR>
 **/
@Getter
@AllArgsConstructor
public enum ThingModeTypeEnum {

    PROPERTY(1, "属性"),
    FUNCTION(2, "服务"),
    EVENT(3, "事件"),

    ;

    private final int type;
    private final String desc;

    /**
     * 根据类型获取描述
     *
     * @param type
     * @return
     */
    public static String getDescByType(Integer type) {
        for (ThingModeTypeEnum modeType : ThingModeTypeEnum.values()) {
            if (Objects.equals(type, modeType.type)) {
                return modeType.getDesc();
            }
        }
        return null;
    }
}
