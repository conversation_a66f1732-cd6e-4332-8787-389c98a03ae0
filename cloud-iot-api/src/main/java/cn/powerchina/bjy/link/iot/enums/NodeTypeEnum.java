package cn.powerchina.bjy.link.iot.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 节点类型枚举
 * 0直连，1网关，2网关子设备
 *
 * <AUTHOR>
 **/
@Getter
@AllArgsConstructor
public enum NodeTypeEnum {

    DIRECT(0, "直连设备"),
    EDGE(1, "网关设备"),
    EDGE_SUB(2, "网关子设备")

    ;

    private static final Map<Integer, String> ENUM_MAP= Arrays.stream(NodeTypeEnum.values()).collect(Collectors.toMap(NodeTypeEnum::getType, NodeTypeEnum::getDesc));

    public static String getDescByType(Integer type){
        return ENUM_MAP.get(type);
    }

    private final Integer type;
    private final String desc;
}
