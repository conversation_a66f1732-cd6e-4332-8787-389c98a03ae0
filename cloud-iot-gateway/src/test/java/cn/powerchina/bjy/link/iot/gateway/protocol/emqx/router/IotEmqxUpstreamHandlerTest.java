package cn.powerchina.bjy.link.iot.gateway.protocol.emqx.router;

import io.netty.buffer.ByteBuf;
import io.netty.handler.codec.mqtt.MqttQoS;
import io.vertx.core.buffer.Buffer;
import io.vertx.mqtt.messages.MqttPublishMessage;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.StringJoiner;

@SpringBootTest
public class IotEmqxUpstreamHandlerTest {

    @Autowired
    private IotEmqxUpstreamHandler iotEmqxUpstreamHandler;

    @Test
    public void streamStarthandle(){
        StringJoiner sj = new StringJoiner("");
        sj.add("{ ");
        sj.add("    \"requestId\": \"msgb4896e30241746ffb19aaf88940f1a19\", ");
        sj.add("    \"subDeviceId\": \"D1757312056860262\", ");
        sj.add("    \"resultCode\": 1, ");
        sj.add("    \"params\": { ");
        sj.add("        \"msg\": \"\", ");
        sj.add("        \"hostName\": \"DESKTOP-7D6M8R6\", ");
        sj.add("        \"code\": 0, ");
        sj.add("        \"data\": \"https://sit-iot.bhidi.com/live/cfde65c6-b381-4545-8447-309367b4aacb.flv\" ");
        sj.add("    } ");
        sj.add("} ");

        String topic = "/iot/devices/D1757312056860262/commands/response";
        Buffer payload = Buffer.buffer(sj.toString());

        MqttPublishMessage mqttMessage = MqttPublishMessage.create(0, MqttQoS.AT_LEAST_ONCE, false, false, topic, (ByteBuf) payload);

        iotEmqxUpstreamHandler.handle(mqttMessage);
    }
}
