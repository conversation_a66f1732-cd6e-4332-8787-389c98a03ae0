package cn.powerchina.bjy.link.iot.gateway.protocol.emqx;


import cn.powerchina.bjy.link.iot.gateway.config.IotGatewayProperties;
import cn.powerchina.bjy.link.iot.gateway.protocol.emqx.router.IotEmqxAuthEventHandler;
import cn.powerchina.bjy.link.iot.utils.IotDeviceMessageUtils;
import cn.powerchina.bjy.link.iot.gateway.util.IotMqttTopicUtils;
import io.vertx.core.Vertx;
import io.vertx.core.http.HttpServer;
import io.vertx.ext.web.Router;
import io.vertx.ext.web.handler.BodyHandler;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;

/**
 * IoT 网关 EMQX 认证事件协议服务
 * <p>
 * 为 EMQX 提供 HTTP 接口服务，包括：
 * 1. 设备认证接口 - 对应 EMQX HTTP 认证插件
 * 2. 设备事件处理接口 - 对应 EMQX Webhook 事件通知
 *
 * <AUTHOR>
 */
@Slf4j
public class IotEmqxAuthEventProtocol {

    private final IotGatewayProperties.EmqxProperties emqxProperties;

    private final String serverId;

    private Vertx vertx;

    private HttpServer httpServer;

    public IotEmqxAuthEventProtocol(IotGatewayProperties.EmqxProperties emqxProperties) {
        this.emqxProperties = emqxProperties;
        this.serverId = IotDeviceMessageUtils.generateServerId(emqxProperties.getMqttPort());
    }

    @PostConstruct
    public void start() {
        try {
            // 创建 Vertx 实例
            this.vertx = Vertx.vertx();

            startHttpServer();
            log.info("[start][IoT 网关 EMQX 认证事件协议服务启动成功, 端口: {}]", emqxProperties.getHttpPort());
        } catch (Exception e) {
            log.error("[start][IoT 网关 EMQX 认证事件协议服务启动失败]", e);
            throw e;
        }
    }

    @PreDestroy
    public void stop() {
        stopHttpServer();

        // 关闭 Vertx 实例
        if (vertx != null) {
            try {
                vertx.close();
                log.debug("[stop][Vertx 实例已关闭]");
            } catch (Exception e) {
                log.warn("[stop][关闭 Vertx 实例失败]", e);
            }
        }

        log.info("[stop][IoT 网关 EMQX 认证事件协议服务已停止]");
    }

    /**
     * 启动 HTTP 服务器
     */
    private void startHttpServer() {
        int port = emqxProperties.getHttpPort();

        // 1. 创建路由
        Router router = Router.router(vertx);
        router.route().handler(BodyHandler.create());

        // 2. 创建处理器，传入 serverId
        IotEmqxAuthEventHandler handler = new IotEmqxAuthEventHandler(serverId);
        router.post(IotMqttTopicUtils.MQTT_AUTH_PATH).handler(handler::handleAuth);
        router.post(IotMqttTopicUtils.MQTT_EVENT_PATH).handler(handler::handleEvent);

        // 3. 启动 HTTP 服务器
        try {
            httpServer = vertx.createHttpServer()
                    .requestHandler(router)
                    .listen(port)
                    .result();
        } catch (Exception e) {
            log.error("[startHttpServer][HTTP 服务器启动失败, 端口: {}]", port, e);
            throw e;
        }
    }

    /**
     * 停止 HTTP 服务器
     */
    private void stopHttpServer() {
        if (httpServer == null) {
            return;
        }

        try {
            httpServer.close().result();
            log.info("[stopHttpServer][HTTP 服务器已停止]");
        } catch (Exception e) {
            log.error("[stopHttpServer][HTTP 服务器停止失败]", e);
        }
    }

}