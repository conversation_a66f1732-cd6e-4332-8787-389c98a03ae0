package cn.powerchina.bjy.link.iot.gateway.service.device.message;


import cn.powerchina.bjy.link.iot.model.IotDeviceMessage;

/**
 * IoT 设备消息 Service 接口
 *
 * <AUTHOR>
 */
public interface IotDeviceMessageService {

    /**
     * 编码消息
     *
     * @param message    消息
     * @param productKey 产品 Key
     * @param deviceName 设备名称
     * @return 编码后的消息内容
     */
    byte[] encodeDeviceMessage(IotDeviceMessage message,
                               String productKey, String deviceName);

    /**
     * 解码消息
     *
     * @param bytes      消息内容
     * @param productKey 产品 Key
     * @param deviceName 设备名称
     * @return 解码后的消息内容
     */
    IotDeviceMessage decodeDeviceMessage(byte[] bytes,
                                         String productKey, String deviceName);

    /**
     * 发送消息
     *
     * @param message 消息
     * @param productKey 产品 Key
     * @param deviceName 设备名称
     * @param serverId 设备连接的 serverId
     */
    void sendDeviceMessage(IotDeviceMessage message,
                           String productKey, String deviceName, String serverId);

}
