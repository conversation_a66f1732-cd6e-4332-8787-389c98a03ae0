package cn.powerchina.bjy.link.iot.gateway.mq.rocketmq;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.powerchina.bjy.cloud.framework.common.util.json.JsonUtils;
import cn.powerchina.bjy.link.iot.enums.device.IotDeviceMessageMethodEnum;
import cn.powerchina.bjy.link.iot.gateway.protocol.emqx.IotEmqxUpstreamProtocol;
import cn.powerchina.bjy.link.iot.gateway.util.IotMqttTopicUtils;
import cn.powerchina.bjy.link.iot.model.IotDeviceMessage;
import cn.powerchina.bjy.link.iot.utils.IotDeviceMessageUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.annotation.RocketMQMessageListener;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.apache.rocketmq.client.core.RocketMQListener;
import org.springframework.stereotype.Component;

import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;

/**
 * @Description: 监听设备上报的消息
 * @Author: zhaoqiang
 * @CreateDate: 2024/10/16
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = IotDeviceMessage.TOPIC_GATEWAY_DEVICE_MESSAGE, consumerGroup = IotDeviceMessage.GROUP_IOT_DEVICE_MESSAGE, requestTimeout = 10, consumptionThreadCount = 10)
public class IotRocketMQSubscriber implements RocketMQListener {

    @Resource
    private IotEmqxUpstreamProtocol iotEmqxUpstreamProtocol;

    @Override
    public ConsumeResult consume(MessageView messageView) {
        if (ObjectUtil.isNull(messageView)) {
            log.error("MessageView is null, skip consumption");
        }
        // 解析消息体
        IotDeviceMessage iotDeviceMessage = parseMessageBody(messageView);
        if (iotDeviceMessage == null) {
            log.error("MessageView is null, skip consumption");
            return ConsumeResult.SUCCESS;
        }

        log.info("receive message: {}", JSONObject.toJSON(iotDeviceMessage));
        if (null == iotDeviceMessage.getMethod()) {
            log.error("消息 {}缺少上报类型", messageView.getMessageId());
            return ConsumeResult.SUCCESS;
        }
        IotDeviceMessageMethodEnum enumByMethod = IotDeviceMessageMethodEnum.getEnumByMethod(iotDeviceMessage.getMethod());
        if (null == enumByMethod) {
            log.error("不支持的上报类型 {}", iotDeviceMessage.getMethod());
            return ConsumeResult.SUCCESS;
        }
        String topic = buildTopicByMethod(iotDeviceMessage, iotDeviceMessage.getDeviceCode());
        if (StrUtil.isBlank(topic)) {
            log.warn("[handle][未知的消息方法: {}]", iotDeviceMessage.getMethod());
            return ConsumeResult.SUCCESS;
        }
        //发送message给设备
        try {
            iotEmqxUpstreamProtocol.publishMessage(topic, JsonUtils.toJsonByte(iotDeviceMessage));
        } catch (Exception e) {
            log.error("发送下行消息{}失败:{}", iotDeviceMessage.getRequestId(), e.getMessage());
        }
        return ConsumeResult.SUCCESS;
    }

    private String buildTopicByMethod(IotDeviceMessage message, String deviceName) {
        // 1. 判断是否为回复消息
        boolean isReply = IotDeviceMessageUtils.isReplyMessage(message);
        // 2. 根据消息方法类型构建对应的主题
        return IotMqttTopicUtils.buildTopicByMethod(message.getMethod(), deviceName, isReply);
    }

    /**
     * 解析消息体为实体类
     */
    private IotDeviceMessage parseMessageBody(MessageView messageView) {
        try {
            ByteBuffer body = messageView.getBody();
            if (body.remaining() == 0) {
                log.error("Message body is empty, messageId: {}", messageView.getMessageId());
                return null;
            }

            String message = StandardCharsets.UTF_8.decode(body).toString();
            return JSON.parseObject(message, IotDeviceMessage.class);

        } catch (JSONException e) {
            log.error("JSON解析失败, messageId: {}, content: {}",
                    messageView.getMessageId(),
                    messageView.getBody(),
                    e
            );
            return null;
        }
    }

}
