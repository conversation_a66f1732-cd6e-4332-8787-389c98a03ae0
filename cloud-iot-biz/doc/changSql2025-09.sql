ALTER TABLE iot_product  ADD COLUMN device_limit bigint NOT NULL DEFAULT 1000 COMMENT '设备限额';
ALTER TABLE iot_product  ADD COLUMN used_quota bigint COMMENT '已使用额度';
ALTER TABLE iot_product  ADD COLUMN dynamic_register tinyint DEFAULT 1 COMMENT '动态注册（0-否，1-是）';
ALTER TABLE iot_product  ADD COLUMN pre_register tinyint DEFAULT 1 COMMENT '预注册（0-否，1-是）';
alter table iot_product add column auth_method tinyint COMMENT '认证方式(1:设备密钥)';

ALTER TABLE iot_device  ADD COLUMN device_secret varchar(64) COMMENT '设备密钥';
ALTER TABLE iot_edge_gateway  ADD COLUMN secret varchar(64) COMMENT '密钥';

ALTER TABLE iot_device MODIFY COLUMN driver_code VARCHAR(32);
ALTER TABLE iot_device MODIFY COLUMN edge_code VARCHAR(32);

alter table iot_device change column link_state link_state tinyint unsigned default '0' not null comment '连接状态（0:未激活; 1:离线; 2:在线）';
alter table iot_device add column device_type tinyint unsigned default 1 not null comment '类型(0:edge实例; 1:设备）';

alter table iot_device add column status tinyint default 1 not null comment '启用状态（0:禁用 1：启用）';

-- test库已添加
ALTER TABLE iot_device_shadow  ADD COLUMN shadow_type tinyint DEFAULT 0 COMMENT '类型（0-属性，1-事件，2-状态变更）' AFTER thing_value;
ALTER TABLE iot_device_shadow  ADD COLUMN shadow_payload VARCHAR(500) COMMENT '负载数据' AFTER shadow_type;

ALTER TABLE iot_edge_channel  ADD COLUMN edge_code VARCHAR(32) COMMENT '边缘实例编码' AFTER driver_code;


ALTER TABLE iot_device
    CONVERT TO CHARACTER SET utf8mb4
    COLLATE utf8mb4_unicode_ci;

CREATE TABLE iot_mqtt_auth(
                              id          bigint unsigned auto_increment comment '主键id' primary key,
                              user_name   varchar(64)                           not null comment '用户名',
                              secret      varchar(64)                           null comment '密钥',
                              type        tinyint                               null comment '认证类型(1:应用管理;2:产品管理;3:设备管理)',
                              status      tinyint     default '1'               null comment '状态(1:启用;0:禁用)',
                              creator     varchar(64) default ''                null comment '创建者',
                              create_time datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
                              updater     varchar(64) default ''                null comment '更新者',
                              update_time datetime                              null comment '更新时间',
                              deleted     bit         default b'0'              not null comment '是否删除，默认为0'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci comment 'mqtt认证';

alter table iot_mqtt_auth change column type type tinyint null comment '认证类型(1:应用管理; 2:产品管理; 3:设备管理; 4:edge)';

CREATE TABLE iot_drive(
                          id          bigint unsigned auto_increment comment '主键id' primary key,
                          drive_name   varchar(64)                           not null comment '驱动名称',
                          drive_code   varchar(64)                           not null comment '驱动编码',
                          remark      varchar(64)                       null comment '备注',
                          version    varchar(16)                       null comment '驱动版本',
                          status      tinyint                               null comment '状态(1:运行中;0:未启动)',
                          creator     varchar(64) default ''                null comment '创建者',
                          create_time datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
                          updater     varchar(64) default ''                null comment '更新者',
                          update_time datetime                              null comment '更新时间',
                          deleted     bit         default b'0'              not null comment '是否删除，默认为0'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin comment '驱动';

INSERT INTO iot_drive (drive_name,drive_code,remark,version,status,creator,create_time,updater,update_time,deleted)
VALUES
    ('MODBUS驱动','MODBUS','基康','v1.0.0',1,'1','2025-08-06 19:06:27','',NULL,0),
    ('视频驱动','VIDEO','视频驱动','v1.0.0',1,'1','2025-08-06 19:06:27','',NULL,0);


insert into iot_mqtt_auth(user_name, secret, type, status, create_time, update_time)
select product_code, product_secret, 2, product_state, now(), now() from iot_product where deleted=false;

insert into iot_mqtt_auth(user_name, secret, type, status, create_time, update_time)
select app_code, secret, 1, status, now(), now() from iot_app_auth where deleted=false;


UPDATE
    iot_product p
    JOIN
    (SELECT product_code,
    COUNT(id) AS device_count
    FROM iot_device
    WHERE deleted = false
    GROUP BY product_code) AS d_counts ON p.product_code = d_counts.product_code
    SET p.used_quota = d_counts.device_count;