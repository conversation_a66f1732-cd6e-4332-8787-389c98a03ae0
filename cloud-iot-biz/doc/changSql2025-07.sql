ALTER TABLE iot_scene_rule_trigger ADD is_invalid tinyint default 0 NULL COMMENT '是否失效 1失效,0未失效,2设备删除';
ALTER TABLE iot_scene_rule_action ADD is_invalid tinyint default 0 NULL COMMENT '是否失效 1失效,0未失效,2设备删除';

DROP TABLE iot_transport_rule;
CREATE TABLE iot_transport_rule
(
    id          bigint unsigned  auto_increment comment '主键id'
primary key,
    name        varchar(64)                           not null comment '规则名称',
    rule_code   varchar(64)                           not null comment '转发规则编码',
    status      tinyint     default '0'               not null comment '启用状态（0:未启动 1：运行中）',
    remark      varchar(255)                          null comment '规则描述',
    creator     varchar(64) default ''                null comment '创建者',
    create_time datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
    updater     varchar(64) default ''                null comment '更新者',
    update_time datetime                              null comment '更新时间',
    deleted     bit         default b'0'              not null comment '是否删除，默认为0'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin comment '转发规则';


CREATE TABLE iot_transport_source
(
    id                bigint unsigned auto_increment comment '主键id'
        primary key,
    rule_id           bigint                                not null comment '规则id',
    data_type         varchar(255)                           not null comment '数据类型，逗号分隔',
    resource_space_id bigint                                null comment '资源空间id：全部是-1',
    resource_space_name      varchar(64)                    null comment '资源空间名称',
    product_code      varchar(64)                           null comment '产品code：全部是-1',
    product_name      varchar(64)                           null comment '产品名称',
    device_code       varchar(64)                           null comment '设备code:全部-1',
    device_name       varchar(64)                           null comment '设备名称',
    creator           varchar(64) default ''                null comment '创建者',
    create_time       datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
    updater           varchar(64) default ''                null comment '更新者',
    update_time       datetime                              null comment '更新时间',
    deleted           bit         default b'0'              not null comment '是否删除，默认为0'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin comment '转发规则-数据源';


CREATE TABLE iot_transport_target
(
    id             bigint unsigned auto_increment comment '主键id'
        primary key,
    rule_id        bigint                                not null comment '规则id',
    name           varchar(64)                           not null comment '目标名称',
    transport_type tinyint                               not null comment '转发类型(1:HTTP推送 2MQTT推送)',
    url_address    varchar(255)                          null comment 'url地址',
    token          varchar(255)                          null comment 'token',
    topic          varchar(255)                          null comment '转发topic',
    remark         varchar(500)                          null comment '备注',
    creator        varchar(64) default ''                null comment '创建者',
    create_time    datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
    updater        varchar(64) default ''                null comment '更新者',
    update_time    datetime                              null comment '更新时间',
    deleted        bit         default b'0'              not null comment '是否删除，默认为0'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin comment '转发规则-转发目标';


CREATE TABLE iot_app_auth
(
    id          bigint unsigned auto_increment comment '主键id'
        primary key,
    app_name    varchar(64)                           not null comment '应用名称',
    app_code    varchar(64)                           not null comment '应用code',
    secret      varchar(64)                           null comment '密钥',
    description varchar(255)                          null comment '描述',
    status      tinyint                               null comment '应用状态(1:启用;0:禁用)',
    creator     varchar(64) default ''                null comment '创建者',
    create_time datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
    updater     varchar(64) default ''                null comment '更新者',
    update_time datetime                              null comment '更新时间',
    `tenant_id` bigint DEFAULT NULL COMMENT '租户编号',
    deleted     bit         default b'0'              not null comment '是否删除，默认为0'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin comment '应用管理认证';



CREATE TABLE iot_scene_alarm_record_detail (
    id            bigint unsigned auto_increment comment '主键id' primary key,
    alarm_id      bigint                               not null comment '告警id',
    rule_id       bigint                               not null comment '规则ID',
    alarm_time    datetime  default                    null comment '告警时间',
    device_name varchar(50) default                    null comment '触发设备',
    trigger_condition varchar(50) default              null comment '触发条件',
    creator     varchar(64) default ''                 null comment '创建者',
    create_time datetime    default CURRENT_TIMESTAMP  not null comment '创建时间',
    updater     varchar(64) default ''                 null comment '更新者',
    update_time datetime                               null comment '更新时间',
    deleted     bit         default b'0'               not null comment '是否删除，默认为0'
) ENGINE=InnoDB default CHARSET=utf8mb4 COLLATE=utf8mb4_bin comment='告警记录详情表';


CREATE TABLE iot_notification_config (
    id            bigint unsigned auto_increment comment '主键id' primary key,
    notification_name varchar(50)  default             null comment '配置名称',
    notification_method tinyint default                null comment '消息类型（1:钉钉 2：邮件 3：短信）',
    notification_host varchar(500) default             null comment '服务器地址',
    notification_port varchar(20)  default             null comment '端口',
    notification_pass varchar(100)                     not null comment '授权码',
    username varchar(30)                               not null comment '用户名',
    password varchar(100)                              not null comment '密码',
    creator     varchar(64) default ''                 null comment '创建者',
    create_time datetime    default CURRENT_TIMESTAMP  not null comment '创建时间',
    updater     varchar(64) default ''                 null comment '更新者',
    update_time datetime                               null comment '更新时间',
    deleted     bit         default b'0'               not null comment '是否删除，默认为0'
) ENGINE=InnoDB default CHARSET=utf8mb4 COLLATE=utf8mb4_bin comment='消息配置';


CREATE TABLE iot_notification_record (
    id            bigint unsigned auto_increment comment '主键id' primary key,
    alarm_id bigint                                   not null comment '告警id',
    alarm_detail_id bigint                            not null comment '告警详情id',
    subject varchar(50) default                       null comment '主题',
    inbox varchar(50) default                         null comment '收件箱',
    send_status bit default b'1'                      not null comment '发送状态，1成功 0失败',
    outbox varchar(50) default                        null comment '发件箱',
    send_time datetime default                        null comment '发送时间',
    email_content text                                null comment '邮件内容',
    remark varchar(500) default null comment '备注',
    creator     varchar(64) default ''                 null comment '创建者',
    create_time datetime    default CURRENT_TIMESTAMP  not null comment '创建时间',
    updater     varchar(64) default ''                 null comment '更新者',
    update_time datetime                               null comment '更新时间',
    deleted     bit         default b'0'               not null comment '是否删除，默认为0'
) ENGINE=InnoDB default CHARSET=utf8mb4 COLLATE=utf8mb4_bin comment='消息记录';



-- 消息配置
INSERT INTO iot_notification_config (notification_name,notification_method,notification_host,notification_port,notification_pass,username,password,creator,create_time,updater,update_time,deleted) VALUES
    ('邮件告警',2,'smtp.163.com','465','PUgfEK36VczAnwNL','<EMAIL>','12345','1','2025-07-03 18:25:56','1','2025-07-15 16:56:54',0);

