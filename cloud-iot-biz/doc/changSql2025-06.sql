-- V1.1 相关sql
ALTER TABLE iot_resource_space
    ADD is_all_products tinyint default 0 NULL COMMENT '是否勾选所有产品按钮';
ALTER TABLE iot_resource_space
    ADD is_all_gateway tinyint default 0 NULL COMMENT '是否勾选所有边缘实例';

ALTER TABLE iot_product
    ADD is_all_device tinyint default 0 NULL COMMENT '是否勾选所有设备';
ALTER TABLE iot_product
    ADD template_id bigint NULL COMMENT '模板id';

DROP TABLE iot_transport_rule;
CREATE TABLE iot_transport_rule
(
    id                  bigint auto_increment comment '主键id'
        primary key,
    transport_rule_code varchar(32)      default ''                not null comment '转发规则编码',
    transport_desc      varchar(255)                               null comment '规则描述',
    transport_name      varchar(64)      default ''                not null comment '转发名称',
    transport_type      tinyint                                    not null comment '转发类型(1:HTTP推送 2MQTT推送)',
    transport_url       varchar(200)                               null comment '推送的URL',
    transport_token     varchar(100)                               null comment '推送的token',
    host                varchar(64)      default ''                not null comment '服务器地址',
    port                int unsigned     default '0'               not null comment '端口号',
    topic               varchar(64)      default ''                not null comment '转发topic，未必都有',
    remark              varchar(255)     default ''                not null comment '备注',
    resource_space_id   bigint                                     null comment '资源空间id，0：全部',
    status              tinyint unsigned default '0'               not null comment '启用状态（0:未启动 1：运行中 2：停用）',
    creator             varchar(64)      default ''                null comment '创建者',
    create_time         datetime         default CURRENT_TIMESTAMP not null comment '创建时间',
    updater             varchar(64)      default ''                null comment '更新者',
    update_time         datetime                                   null comment '更新时间',
    deleted             bit              default b'0'              not null comment '是否删除，默认为0'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin comment '转发规则';

CREATE TABLE iot_user
(
    id          bigint auto_increment comment '主键id'
        primary key,
    username    varchar(30)                            not null comment '用户账号',
    name        varchar(128)                           not null comment '姓名',
    mobile      varchar(32)  default ''                null comment '手机号码',
    dept_name   varchar(128) default ''                null comment '部门名称',
    post_name   varchar(128) default ''                null comment '职务',
    email       varchar(50)  default ''                null comment '用户邮箱',
    status      tinyint      default 0                 not null comment '启用状态（0正常 1停用）',
    creator     varchar(64)                            null comment '创建者',
    create_time datetime     default CURRENT_TIMESTAMP null comment '创建时间',
    updater     varchar(64)                            null comment '更新者',
    update_time datetime     default CURRENT_TIMESTAMP null comment '更新时间',
    deleted     bit          default b'0'              null comment '是否删除，0未删除，1删除，默认为0'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin comment '用户表';

CREATE TABLE iot_model_categorize
(
    id              bigint auto_increment comment '主键id'
        primary key,
    parent_id       bigint           default 0                 not null comment '父ID',
    categorize_name varchar(32) charset utf8mb4                null comment '分类名称',
    level           int              default 0                 not null comment '分类层级',
    sort            int              default 0                 not null comment '显示顺序',
    state           tinyint unsigned default '0'               not null comment '启用状态（0不启用，1启用，默认0）',
    tenant_id       bigint           default 0                 not null comment '租户编号',
    remark          varchar(255)     default ''                not null comment '备注',
    creator         varchar(64)                                null comment '创建者',
    create_time     datetime         default CURRENT_TIMESTAMP null comment '创建时间',
    updater         varchar(64)                                null comment '更新者',
    update_time     datetime         default CURRENT_TIMESTAMP null comment '更新时间',
    deleted         bit              default b'0'              null comment '是否删除，0未删除，1删除，默认为0'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin comment '物模板分类信息表';

CREATE TABLE iot_model_template
(
    id                bigint auto_increment comment '主键id'
        primary key,
    categorize_one_id bigint                                 null comment '物模板一级分类ID',
    categorize_two_id bigint                                 null comment '物模板二级分类ID',
    template_name     varchar(32) charset utf8mb4            not null comment '物模板名称',
    remark            varchar(255) default ''                null comment '描述',
    tenant_id         bigint       default 0                 not null comment '租户编号',
    creator           varchar(64)                            null comment '创建者',
    create_time       datetime     default CURRENT_TIMESTAMP null comment '创建时间',
    updater           varchar(64)                            null comment '更新者',
    update_time       datetime     default CURRENT_TIMESTAMP null comment '更新时间',
    deleted           bit          default b'0'              null comment '是否删除，0未删除，1删除，默认为0'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin comment '物模板信息表';

CREATE TABLE iot_model_template_details
(
    id                    bigint auto_increment comment '主键id'
        primary key,
    template_id           bigint                                 not null comment '物模板ID',
    template_details_name varchar(64)  default ''                not null comment '物模板明细名称',
    template_identity     varchar(32)                            not null comment '物模板标识符',
    template_type         int          default 1                 not null comment '物模板类型，1-属性；2-服务；3-事件；',
    datatype              varchar(64)                            null comment '数据类型（integer、decimal、string、bool、array、enum）',
    read_write_type       int                                    null comment '读写类型，thing_type为1时必填；1-读写；2-只读；3-只写',
    event_type            int                                    null comment '事件类型，thing_type为3时必填,1-信息；2告警；3-故障',
    input_params          json                                   null comment '输入参数',
    output_params         json                                   null comment '输出参数',
    extra                 json                                   null comment '属性扩展信息',
    remark                varchar(255) default ''                null comment '描述',
    tenant_id             bigint       default 0                 not null comment '租户编号',
    creator               varchar(64)                            null comment '创建者',
    create_time           datetime     default CURRENT_TIMESTAMP null comment '创建时间',
    updater               varchar(64)                            null comment '更新者',
    update_time           datetime     default CURRENT_TIMESTAMP null comment '更新时间',
    deleted               bit          default b'0'              null comment '是否删除，0未删除，1删除，默认为0'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin comment '物模板信息明细表';

CREATE TABLE iot_role_data_permissions
(
    id          bigint auto_increment comment '主键id'
        primary key,
    role_id     bigint                             not null comment '角色ID',
    data_scope  tinyint  default 1                 not null comment '数据范围（1：全部数据权限 2：指定数据权限）',
    remark      varchar(500)                       null comment '备注',
    creator     varchar(64)                        null comment '创建者',
    create_time datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updater     varchar(64)                        null comment '更新者',
    update_time datetime default CURRENT_TIMESTAMP null comment '更新时间',
    deleted     bit      default b'0'              null comment '是否删除，0未删除，1删除，默认为0'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin comment '角色与权限的关系表';

CREATE TABLE iot_data_permissions
(
    id          bigint auto_increment comment '主键id'
        primary key,
    data_id     varchar(255)                       null comment '数据唯一id',
    role_id     bigint   default 0                 not null comment '角色ID',
    name        varchar(255)                       not null comment '名称',
    parent_id   varchar(255)                       null comment '父级id',
    level       tinyint                            null comment '等级(1：资源空间 2：所有产品 3：产品 4：所有设备 5：设备 6：边缘计算 7：所有实例 8：边缘实例)',
    remark      text                               null comment '描述',
    creator     varchar(64)                        null comment '创建者',
    create_time datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updater     varchar(64)                        null comment '更新者',
    update_time datetime default CURRENT_TIMESTAMP null comment '更新时间',
    deleted     bit      default b'0'              null comment '是否删除，0未删除，1删除，默认为0'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin comment '数据权限表';

-- 删除现有产品和设备
update iot_device
set deleted=1
where 1 = 1;
update iot_product
set deleted=1
where 1 = 1;

-- V1.2相关sql
CREATE TABLE iot_scene_rule(
    id                   bigint auto_increment comment '主键id'
        primary key,
    rule_name            varchar(50)                        not null comment '规则名称',
    resource_space_id    bigint                             not null comment '所属资源空间ID',
    status               tinyint  default 1                 not null comment '状态:0-禁用,1-启用',
    inhibition           tinyint  default 1                 not null comment '是否抑制:0-禁用抑制,1-启用抑制',
    effective_type       tinyint  default 1                 null comment '生效时段类型:1-全天,2-自定义',
    effective_start_time varchar(50)                        null comment '生效开始时间',
    effective_end_time   varchar(50)                        null comment '生效结束时间',
    repeat_type          tinyint  default 1                 null comment '重复类型:1-每天,2-指定日期,3-指定周期,4-自定义',
    repeat_start_date    date     DEFAULT NULL COMMENT '开始日期/指定日期',
    repeat_end_date      date     DEFAULT NULL COMMENT '结束日期',
    repeat_week_days     varchar(20)                        null comment '每周重复的星期几,如:1,2,3,4,5,6,7',
    rule_expression      text                               null comment '规则表达式',
    inhibition_expression      text                 null comment '抑制规则表达式',
    rule_action          varchar(255)                       null comment '执行动作',
    rule_priority        int      default 1                 null comment '规则优先级',
    rule_desc            varchar(255)                       null comment '规则描述',
    creator              varchar(64)                        null comment '创建者',
    create_time          datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updater              varchar(64)                        null comment '更新者',
    update_time          datetime default CURRENT_TIMESTAMP null comment '更新时间',
    deleted              bit      default b'0'              null comment '是否删除，0未删除，1删除，默认为0',
    unsatisfied_times        int      default 0             null comment '告警后未触发次数'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin comment '场景规则表';

CREATE TABLE iot_scene_rule_time_trigger
(
    id                bigint auto_increment comment '主键id'
        primary key,
    rule_id           bigint                             not null comment '规则ID',
    trigger_id        bigint                             not null comment '规则触发ID',
    execution_time    varchar(50)                        null comment '执行时刻',
    job_id            varchar(36)                        null comment 'XXXJob-id',
    repeat_type       tinyint  default 1                 null comment '重复类型:1-每天,2-指定日期,3-指定周期,4-自定义',
    repeat_start_date date     DEFAULT NULL COMMENT '开始日期/指定日期',
    repeat_end_date   date     DEFAULT NULL COMMENT '结束日期',
    repeat_week_days  varchar(20)                        null comment '每周重复的星期几,如:1,2,3,4,5,6,7',
    cron_expression   varchar(64)                        null comment 'cron表达式',
    sort              int      default 0                 not null comment '排序',
    creator           varchar(64)                        null comment '创建者',
    create_time       datetime default CURRENT_TIMESTAMP null comment '创建时间',
    updater           varchar(64)                        null comment '更新者',
    update_time       datetime default CURRENT_TIMESTAMP null comment '更新时间',
    deleted           bit      default b'0'              null comment '是否删除，0未删除，1删除，默认为0'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin comment '定时触发表';

CREATE TABLE iot_scene_rule_trigger
(
    id                   bigint auto_increment comment '主键id'
        primary key,
    rule_id              bigint                                not null comment '规则ID',
    condition_type       tinyint     default 1                 not null comment '条件类型:1-触发条件,2-限制条件',
    trigger_type         tinyint     default 2                 not null comment '触发类型:1-定时触发,2-设备触发;（限制类型：1-设备）',
    device_trigger_type  tinyint                               null comment '设备触发方式(限制方式):1-属性触发,2-事件触发,3-上下线触发',
    product_code         varchar(64)                           null comment '产品code',
    device_code          varchar(64)                           null comment '设备code,为空表示所有设备',
    attribute_expression varchar(200)                          null comment '属性表达式',
    attribute_identity   varchar(64)                           null comment '属性唯一标识',
    attribute_condition  tinyint                               null comment '条件:1-大于,2-小于,3-等于,4-不等于,5-变化速率',
    attribute_value      varchar(200)                          null comment '属性值',
    event_identity       varchar(64)                           null comment '事件唯一标识',
    online_status        tinyint                               null comment '上下线状态:1-上线,2-下线',
    sort                 int         default 0                 not null comment '排序',
    creator              varchar(64) default ''                null comment '创建者',
    create_time          datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
    updater              varchar(64) default ''                null comment '更新者',
    update_time          datetime                              null comment '更新时间',
    deleted              bit         default b'0'              not null comment '是否删除，默认为0'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin comment '规则触发/条件限制表';

CREATE TABLE iot_scene_rule_action
(
    id                bigint auto_increment comment '主键id'
        primary key,
    rule_id           bigint                                not null comment '规则ID',
    action_type       tinyint                               not null comment '动作类型:1-设备动作,2-告警动作,3-执行场景',
    delay_seconds     varchar(64)                      null comment '延迟执行时间',
    product_code      varchar(64)                           null comment '产品code',
    device_code       varchar(64)                           null comment '设备code,为空表示所有设备',
    command_config      text                         null comment '指令配置集合',
    scene_id          bigint                                null comment '场景ID：规则id',
    scene_status      tinyint                               null comment '场景状态:1-执行场景,2-开启场景,3-禁用场景',
    alarm_template_id bigint                                null comment '告警模板id',
    sort              int         default 0                 not null comment '排序',
    creator           varchar(64) default ''                null comment '创建者',
    create_time       datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
    updater           varchar(64) default ''                null comment '更新者',
    update_time       datetime                              null comment '更新时间',
    deleted           bit         default b'0'              not null comment '是否删除，默认为0'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin comment '场景规则执行动作表';


CREATE TABLE iot_alarm_template
(
    id            bigint auto_increment comment '主键id'
        primary key,
    rule_id       bigint                                not null comment '规则ID',
 action_id       bigint                                not null comment '动作ID',
    alarm_name    varchar(64)                           not null comment '告警名称',
    alarm_level   tinyint                               null comment '告警等级(1-轻微,2-中等,3-严重,4-非常严重)',
    alarm_content varchar(500)                          null comment '告警描述',
    sort          int         default 0                 not null comment '排序',
    creator       varchar(64) default ''                null comment '创建者',
    create_time   datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
    updater       varchar(64) default ''                null comment '更新者',
    update_time   datetime                              null comment '更新时间',
    deleted       bit         default b'0'              not null comment '是否删除，默认为0'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin comment '告警模板表';

CREATE TABLE iot_notification_method
(
    id                   bigint auto_increment comment '主键id'
        primary key,
    alarm_template_id    bigint                                null comment '关联的告警模板ID',
    notification_method  tinyint                               null comment '通知方式（1:钉钉 2：邮件 3：短信）',
    notification_account varchar(500)                          null comment '通知账号/webhook地址',
    notification_content varchar(500)                          null comment '通知内容',
    creator              varchar(64) default ''                null comment '创建者',
    create_time          datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
    updater              varchar(64) default ''                null comment '更新者',
    update_time          datetime                              null comment '更新时间',
    deleted              bit         default b'0'              not null comment '是否删除，默认为0'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin comment '通知方式表';

CREATE TABLE iot_scene_alarm_record
(
    id                bigint auto_increment comment '主键id'
        primary key,
    resource_space_id bigint                                null comment '资源空间ID',
    rule_id           bigint                                not null comment '规则ID',
    rule_name         varchar(50)                           null comment '规则名称',
    alarm_template_id bigint                                null comment '关联的告警模板ID',
    alarm_name        varchar(100)                          null comment '告警名称',
    alarm_content     varchar(200)                          null comment '告警内容',
    alarm_level       tinyint                               null comment '告警等级(1-轻微,2-中等,3-严重,4-非常严重)',
    alarm_status      tinyint                               null comment '告警状态(1-触发,2-待验证,3-恢复)',
    trigger_time      datetime                              null comment '触发时间',
    device_code       varchar(32)                           null comment '设备编码',
    product_code      varchar(32)                           null comment '产品编码',
    process_time      datetime                              null comment '处理时间',
    process_record    varchar(500)                          null comment '告警处理',
    process_user_id   bigint                                null comment '处理用户ID',
    recovery_type     tinyint                               null comment '恢复类型(0-自动恢复,1-人工恢复)',
    recovery_time     datetime                              null comment '恢复时间',
    recovery_user_id  bigint                                null comment '恢复用户ID',
    alarm_num           int       default 0                 null comment '告警次数',
    last_alarm_time   datetime                              null comment '最后告警时间',
    creator           varchar(64) default ''                null comment '创建者',
    create_time       datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
    updater           varchar(64) default ''                null comment '更新者',
    update_time       datetime                              null comment '更新时间',
    deleted           bit         default b'0'              not null comment '是否删除，默认为0'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin comment '告警记录表';

CREATE TABLE iot_device_shadow
(
    id             bigint auto_increment comment '主键id'
        primary key,
    product_code   varchar(64)                           not null comment '产品code',
    device_code    varchar(64)                           not null comment '设备code',
    model_code     varchar(64)                           null comment '传感器模型code',
    thing_identity varchar(64)                           null comment '属性标识',
    thing_value    varchar(64)                           null comment '属性值',
    report_time    datetime                              null comment '最新上报时间',
    creator        varchar(64) default ''                null comment '创建者',
    create_time    datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
    updater        varchar(64) default ''                null comment '更新者',
    update_time    datetime                              null comment '更新时间',
    deleted        bit         default b'0'              not null comment '是否删除，默认为0'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_bin comment '设备影子';