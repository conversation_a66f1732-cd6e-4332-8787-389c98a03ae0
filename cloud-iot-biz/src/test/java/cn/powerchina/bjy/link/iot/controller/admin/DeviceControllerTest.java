package cn.powerchina.bjy.link.iot.controller.admin;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.link.iot.controller.admin.device.DeviceController;
import cn.powerchina.bjy.link.iot.controller.admin.device.vo.DeviceAndProductVO;
import cn.powerchina.bjy.link.iot.controller.admin.device.vo.DevicePageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.device.vo.DeviceRespVO;
import cn.powerchina.bjy.link.iot.service.device.DeviceService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
public class DeviceControllerTest {

    @Autowired
    private DeviceController deviceController;

    @Autowired
    private DeviceService deviceService;

    @Test
    public void getDeviceByDeviceCode(){
        CommonResult<DeviceRespVO> aa = deviceController.getDeviceByDeviceCode("D1756274847317683");
        System.out.println(aa.getData());
    }

    @Test
    public void getDevicePage(){
        DevicePageReqVO devicePageReqVO = new DevicePageReqVO();
        devicePageReqVO.setPageNo(1);
        devicePageReqVO.setPageSize(10);
        devicePageReqVO.setDriverCode("MODBUS");
        devicePageReqVO.setEdgeCode("D1757062842059133");
        devicePageReqVO.setProductCode("CP1726102887459180");
        PageResult<DeviceAndProductVO>  pageResult = deviceService.getDevicePage(devicePageReqVO);
        System.out.println("123");
    }
}
