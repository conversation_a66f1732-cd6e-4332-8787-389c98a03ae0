package cn.powerchina.bjy.link.iot.service.edgegateway;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.link.iot.controller.admin.edgegateway.bo.EdgeGatewayBO;
import cn.powerchina.bjy.link.iot.controller.admin.edgegateway.vo.EdgeGatewayPageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.edgegateway.vo.EdgeGatewaySaveReqVO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
public class EdgeGatewayServiceImplTest {

    @Autowired
    private EdgeGatewayService edgeGatewayService;

    @Test
    public void getEdgeGatewayPage(){
        EdgeGatewayPageReqVO pageReqVO =  new EdgeGatewayPageReqVO();
        pageReqVO.setPageNo(1);
        pageReqVO.setPageSize(10);
        PageResult<EdgeGatewayBO> pageResult = edgeGatewayService.getEdgeGatewayPage(pageReqVO);
        System.out.println(pageResult);
    }

    @Test
    public void createEdgeGateway(){
        EdgeGatewaySaveReqVO createReqVO  = new EdgeGatewaySaveReqVO();
        createReqVO.setProductCode("CP1756712504348375");
        createReqVO.setEdgeName("sssddfdfdsfds1");
        createReqVO.setSerial("sssddfdfdsfds1");
        Long id = edgeGatewayService.createEdgeGateway(createReqVO);
        System.out.println(id);
    }
}
