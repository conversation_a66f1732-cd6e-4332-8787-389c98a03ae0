package cn.powerchina.bjy.link.iot.listener.device;

import cn.powerchina.bjy.link.iot.dal.dataobject.device.DeviceDO;
import cn.powerchina.bjy.link.iot.dal.mysql.device.DeviceMapper;
import cn.powerchina.bjy.link.iot.dto.message.DeviceStatusModel;
import cn.powerchina.bjy.link.iot.enums.LinkStateEnum;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
public class DeviceStatusChangeHandlerTest {

    @Autowired
    private DeviceStatusChangeHandler deviceStatusChangeHandler;

    @Autowired
    private DeviceMapper deviceMapper;

    @Test
    public void handler(){

        DeviceDO device = deviceMapper.selectById(1168);

        DeviceStatusModel deviceStatusModel =  new DeviceStatusModel();
        deviceStatusModel.setDeviceCode(device.getDeviceCode());
        deviceStatusModel.setProductCode(device.getProductCode());
        deviceStatusModel.setStatus(LinkStateEnum.OFF_LINE.getType());

        deviceStatusChangeHandler.handler(deviceStatusModel, device);
    }
}
