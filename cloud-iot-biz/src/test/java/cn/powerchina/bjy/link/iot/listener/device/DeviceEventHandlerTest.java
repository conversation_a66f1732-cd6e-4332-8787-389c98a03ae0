package cn.powerchina.bjy.link.iot.listener.device;

import cn.powerchina.bjy.link.iot.dto.message.DeviceEventModel;
import com.alibaba.fastjson.JSON;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.LocalDateTime;
import java.time.ZoneId;

@SpringBootTest
public class DeviceEventHandlerTest {

    @Autowired
    private DeviceEventHandler deviceEventHandler;

    @Test
    public void handler(){
        String a = "{\"max_records_num\":1000,\"first_record_num\":204,\"unrecorded_num\":203}";

        DeviceEventModel deviceEventModel  = new DeviceEventModel();
        deviceEventModel.setThingIdentity("storage_status");
        deviceEventModel.setDeviceCode("D1756712668798613");
        deviceEventModel.setProductCode("CP1756711690472149");
        deviceEventModel.setEventType("1");
        deviceEventModel.setCurrentTime(LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
        deviceEventModel.setReportTime(LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
        deviceEventModel.setParams(JSON.parseObject(a));
        deviceEventModel.setRemark("");
        deviceEventHandler.handler(deviceEventModel);
    }
}
