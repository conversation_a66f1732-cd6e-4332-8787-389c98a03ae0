package cn.powerchina.bjy.link.iot.service.edgecommand;

import cn.powerchina.bjy.link.iot.controller.admin.command.vo.EdgeCommandReqVO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
public class EdgeCommandServiceImplTest {

    @Autowired
    private EdgeCommandService edgeCommandService;

    /**
     * 开始推流
     * {
     *     "deviceCode": "D1757312056860262",
     *     "inputParams": "[{\"datatype\":\"STRING\",\"thingIdentity\":\"cdn\",\"thingValue\":\"\"},{\"datatype\":\"ENUM\",\"thingIdentity\":\"mode\",\"thingValue\":\"\"}]",
     *     "operatorSource": 0,
     *     "productCode": "CP1756949781391824",
     *     "thingIdentity": "stream_start",
     *     "thingName": "发起设备实时推流",
     *     "thingType": 2
     * }
     */
    @Test
    public void streamStart(){
        EdgeCommandReqVO edgeCommandReqVO = new EdgeCommandReqVO();
        edgeCommandReqVO.setDeviceCode("D1757312056860262");
        edgeCommandReqVO.setInputParams("[{\"datatype\":\"STRING\",\"thingIdentity\":\"cdn\",\"thingValue\":\"\"},{\"datatype\":\"ENUM\",\"thingIdentity\":\"mode\",\"thingValue\":\"\"}]");
        edgeCommandReqVO.setOperatorSource(0);
        edgeCommandReqVO.setProductCode("CP1756949781391824");
        edgeCommandReqVO.setThingIdentity("stream_start");
        edgeCommandReqVO.setThingName("发起设备实时推流");
        edgeCommandReqVO.setThingType(2);
        edgeCommandService.commandDown(edgeCommandReqVO);
    }


    /**
     * 保活
     * {
     *     "deviceCode": "D1757312056860262",
     *     "inputParams": "[]",
     *     "operatorSource": 0,
     *     "productCode": "CP1756949781391824",
     *     "thingIdentity": "stream_touch",
     *     "thingName": "直接保活",
     *     "thingType": 2
     * }
     */
    @Test
    public void streamTouch(){
        EdgeCommandReqVO edgeCommandReqVO = new EdgeCommandReqVO();
        edgeCommandReqVO.setDeviceCode("D1757312056860262");
        edgeCommandReqVO.setInputParams("[]");
        edgeCommandReqVO.setOperatorSource(0);
        edgeCommandReqVO.setProductCode("CP1756949781391824");
        edgeCommandReqVO.setThingIdentity("stream_touch");
        edgeCommandReqVO.setThingName("直接保活");
        edgeCommandReqVO.setThingType(2);
        edgeCommandService.commandDown(edgeCommandReqVO);
    }

    /**
     * 停止推流
     * {
     *     "deviceCode": "D1757312056860262",
     *     "inputParams": "[]",
     *     "operatorSource": 0,
     *     "productCode": "CP1756949781391824",
     *     "thingIdentity": "stream_stop",
     *     "thingName": "停止设备实时推流",
     *     "thingType": 2
     * }
     */
    @Test
    public void streamStop(){
        EdgeCommandReqVO edgeCommandReqVO = new EdgeCommandReqVO();
        edgeCommandReqVO.setDeviceCode("D1757312056860262");
        edgeCommandReqVO.setInputParams("[]");
        edgeCommandReqVO.setOperatorSource(0);
        edgeCommandReqVO.setProductCode("CP1756949781391824");
        edgeCommandReqVO.setThingIdentity("stream_stop");
        edgeCommandReqVO.setThingName("停止设备实时推流");
        edgeCommandReqVO.setThingType(2);
        edgeCommandService.commandDown(edgeCommandReqVO);
    }

    /**
     * 云台控制
     * {
     *     "deviceCode": "D1757312056860262",
     *     "inputParams": "[{\"datatype\":\"STRING\",\"thingIdentity\":\"ptz_command\",\"thingValue\":6},{\"datatype\":\"INT\",\"thingIdentity\":\"ptz_speed\",\"thingValue\":9}]",
     *     "operatorSource": 0,
     *     "productCode": "CP1756949781391824",
     *     "thingIdentity": "ptz_start",
     *     "thingName": "云台控制开始",
     *     "thingType": 2
     * }
     */
    @Test
    public void ptzStart(){
        EdgeCommandReqVO edgeCommandReqVO = new EdgeCommandReqVO();
        edgeCommandReqVO.setDeviceCode("D1757312056860262");
        edgeCommandReqVO.setInputParams("[{\"datatype\":\"STRING\",\"thingIdentity\":\"ptz_command\",\"thingValue\":6},{\"datatype\":\"INT\",\"thingIdentity\":\"ptz_speed\",\"thingValue\":9}]");
        edgeCommandReqVO.setOperatorSource(0);
        edgeCommandReqVO.setProductCode("CP1756949781391824");
        edgeCommandReqVO.setThingIdentity("ptz_start");
        edgeCommandReqVO.setThingName("云台控制开始");
        edgeCommandReqVO.setThingType(2);
        edgeCommandService.commandDown(edgeCommandReqVO);
    }
}
