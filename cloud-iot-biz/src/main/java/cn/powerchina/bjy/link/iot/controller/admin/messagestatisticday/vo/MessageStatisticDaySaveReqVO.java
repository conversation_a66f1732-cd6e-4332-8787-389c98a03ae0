package cn.powerchina.bjy.link.iot.controller.admin.messagestatisticday.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDate;
import java.util.*;
import jakarta.validation.constraints.*;

@Schema(description = "管理后台 - 设备消息数按日统计新增/修改 Request VO")
@Data
public class MessageStatisticDaySaveReqVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "21955")
    private Long id;

    @Schema(description = "统计日期")
    private LocalDate statisticDay;

    @Schema(description = "统计数量", example = "29138")
    private Long statisticCount;

}
