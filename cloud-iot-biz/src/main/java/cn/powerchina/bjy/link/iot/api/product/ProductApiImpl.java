package cn.powerchina.bjy.link.iot.api.product;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.link.iot.api.product.dto.ProductModelRespDTO;
import cn.powerchina.bjy.link.iot.api.product.dto.ProductRespDTO;
import cn.powerchina.bjy.link.iot.dal.dataobject.product.ProductDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.productmodel.ProductModelDO;
import cn.powerchina.bjy.link.iot.service.product.ProductService;
import cn.powerchina.bjy.link.iot.service.productmodel.ProductModelService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @Description: 产品
 * @Author: yhx
 * @CreateDate: 2024/9/3
 */
@RestController
@Validated
public class ProductApiImpl implements ProductApi {

    @Autowired
    private ProductService productService;

    @Autowired
    private ProductModelService productModelService;

    @Override
    public CommonResult<ProductRespDTO> getProductByProductCode(String productCode) {
        ProductDO productDO = productService.getProductByCode(productCode);
        ProductRespDTO respDTO = null;
        if (Objects.nonNull(productDO)) {
            respDTO = new ProductRespDTO();
            BeanUtils.copyProperties(productDO, respDTO);
        }
        return CommonResult.success(respDTO);
    }

    @Override
    public CommonResult<Map<String, ProductRespDTO>> batchGetProductByProductCodes(List<String> productCodes) {
        Map<String, ProductRespDTO> respDTOMap = new HashMap<>();
        for (String productCode : productCodes) {
            CommonResult<ProductRespDTO> result = getProductByProductCode(productCode);
            if (Objects.nonNull(result) && Objects.nonNull(result.getData())) {
                respDTOMap.put(productCode, result.getData());
            }
        }
        return CommonResult.success(respDTOMap);
    }

    @Override
    public CommonResult<List<ProductModelRespDTO>> getProductModelByProductCode(String productCode, Integer thingType) {
        List<ProductModelDO> modelDOList = productModelService.getProductModelByProductCodeAndThingType(productCode, thingType);
        List<ProductModelRespDTO> respDTOList = modelDOList.stream().map(item -> {
            ProductModelRespDTO respDTO = new ProductModelRespDTO();
            BeanUtils.copyProperties(item, respDTO);
            return respDTO;
        }).toList();
        return CommonResult.success(respDTOList);
    }

    @Override
    public CommonResult<Map<String, List<ProductModelRespDTO>>> batchGetProductModelByProductCode(List<String> productCodes, Integer thingType) {
        Map<String, List<ProductModelRespDTO>> respDTOMap = new HashMap<>();
        for (String productCode : productCodes) {
            CommonResult<List<ProductModelRespDTO>> result = getProductModelByProductCode(productCode, thingType);
            if (Objects.nonNull(result) && !CollectionUtils.isEmpty(result.getData())) {
                respDTOMap.put(productCode, result.getData());
            }
        }
        return CommonResult.success(respDTOMap);
    }
}
