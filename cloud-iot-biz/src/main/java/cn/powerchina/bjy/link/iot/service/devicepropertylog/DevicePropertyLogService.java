package cn.powerchina.bjy.link.iot.service.devicepropertylog;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.link.iot.controller.admin.devicepropertylog.vo.DevicePropertyLogModelRespVO;
import cn.powerchina.bjy.link.iot.controller.admin.devicepropertylog.vo.DevicePropertyLogPageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.devicepropertylog.vo.DevicePropertyLogSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.devicepropertylog.DevicePropertyLogDO;
import cn.powerchina.bjy.link.iot.dto.up.EdgeReadPropertyValue;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 设备属性日志 Service 接口
 *
 * <AUTHOR>
 */
public interface DevicePropertyLogService {

    /**
     * 创建设备属性日志
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createDevicePropertyLog(@Valid DevicePropertyLogSaveReqVO createReqVO);

    /**
     * 更新设备属性日志
     *
     * @param updateReqVO 更新信息
     */
    void updateDevicePropertyLog(@Valid DevicePropertyLogSaveReqVO updateReqVO);

    /**
     * 删除设备属性日志
     *
     * @param id 编号
     */
    void deleteDevicePropertyLog(Long id);

    /**
     * 获得设备属性日志
     *
     * @param id 编号
     * @return 设备属性日志
     */
    DevicePropertyLogDO getDevicePropertyLog(Long id);

    /**
     * 获得设备属性日志分页
     *
     * @param pageReqVO 分页查询
     * @return 设备属性日志分页
     */
    PageResult<DevicePropertyLogDO> getDevicePropertyLogPage(DevicePropertyLogPageReqVO pageReqVO);


    /**
     * 获得设备运行状态属性日志
     *
     * @param productCode     产品编号
     * @param deviceCode      设备编号
     * @param thingName       物模型名称
     * @param pageParam       分页参数
     * @param readOrWriteType 读写类型
     * @return
     */
    PageResult<DevicePropertyLogModelRespVO> getDevicePropertyLogRunningState(String productCode, String deviceCode, String thingName, PageParam pageParam, Integer[] readOrWriteType);


    /**
     * 保存设备属性
     *
     * @param propertyValueList
     * @param edgeCode
     */
    void savePropertyLog(List<EdgeReadPropertyValue.EdgeDevicePropertyValueDTO> propertyValueList, String edgeCode);
}