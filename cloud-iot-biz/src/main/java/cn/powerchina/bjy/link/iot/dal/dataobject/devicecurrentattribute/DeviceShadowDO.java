package cn.powerchina.bjy.link.iot.dal.dataobject.devicecurrentattribute;

import cn.powerchina.bjy.cloud.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 设备上报的最新属性 DO
 *
 * <AUTHOR>
 */
@TableName("iot_device_shadow")
@KeySequence("iot_device_shadow_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeviceShadowDO extends BaseDO {

    /**
     * 主键id
     */
    @TableId
    private Long id;
    /**
     * 产品code
     */
    private String productCode;
    /**
     * 设备code
     */
    private String deviceCode;
    /**
     * 传感器模型code
     */
    private String modelCode;
    /**
     * 属性标识
     */
    private String thingIdentity;
    /**
     * 属性值
     */
    private String thingValue;

    /**
     * 类型（0-属性，1-事件，2-状态变更）
     */
    private Integer shadowType;

    /**
     * 负载数据
     */
    private String shadowPayload;

    /**
     * 上报时间
     */
    private LocalDateTime reportTime;

}