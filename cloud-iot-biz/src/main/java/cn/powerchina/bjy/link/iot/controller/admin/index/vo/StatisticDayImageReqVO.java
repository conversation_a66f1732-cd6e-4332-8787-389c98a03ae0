package cn.powerchina.bjy.link.iot.controller.admin.index.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


@Schema(description = "管理后台 - 设备消息数统计 Request VO")
@Data
public class StatisticDayImageReqVO {

    @Schema(description = "折线图类型，1：设备消息数，2：消息转发数")
    @NotNull(message = "请选择折线图类型")
    private Integer statisticImageType;

    @Schema(description = "统计类型，1：今日，7：近7日，30：近30日")
    private Integer statisticType;

    @Schema(description = "统计时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] statisticTime;

}
