package cn.powerchina.bjy.link.iot.service.product;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.link.iot.controller.admin.product.bo.ProductBO;
import cn.powerchina.bjy.link.iot.controller.admin.product.vo.ProductCountReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.product.vo.ProductPageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.product.vo.ProductSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.product.ProductDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 产品 Service 接口
 *
 * <AUTHOR>
 */
public interface ProductService {

    /**
     * 根据关键字搜索产品列表
     *
     * @param keyword 搜索关键字
     * @return 产品列表
     */
    List<ProductDO> searchProductsByKeyword(String keyword, Long resourceSpaceId);
    /**
     * 创建产品
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createProduct(@Valid ProductSaveReqVO createReqVO);

    /**
     * 更新产品
     *
     * @param updateReqVO 更新信息
     */
    void updateProduct(@Valid ProductSaveReqVO updateReqVO);

    /**
     * 更新产品的设备限额
     * @param id 产品id
     * @param deviceLimit 设备限额
     */
    void updateDeviceLimit(Long id, Long deviceLimit);

    /**
     * 删除产品
     *
     * @param id 编号
     */
    void deleteProduct(Long id);

    /**
     * 获得产品
     *
     * @param id 编号
     * @return 产品
     */
    ProductDO getProduct(Long id);

    /**
     * 获得产品
     *
     * @param id 编号
     * @return 产品
     */
    ProductBO getProductBO(Long id);

    /**
     * 获得产品分页
     *
     * @param pageReqVO 分页查询
     * @return 产品分页
     */
    PageResult<ProductBO> getProductPage(ProductPageReqVO pageReqVO);


    /**
     * 根据产品code获取产品
     *
     * @return
     */
    ProductDO getProductByCode(String productCode);

    /**
     * 根据产品code获取产品BO
     *
     * @return
     */
    ProductBO getProductBOByCode(String productCode);

    /**
     * 统计产品数量
     *
     * @param reqVO
     * @return
     */
    Long countProduct(ProductCountReqVO reqVO);

    /**
     * 根据产品编码获取产品列表
     *
     * @param productCodeList
     * @return
     */
    List<ProductDO> getProductByCodes(List<String> productCodeList);

    /**
     * 更新产品动态注册状态
     *
     * @param id 编号
     * @param dynamicRegister 动态注册状态
     */
    void updateDynamicRegister(Long id, Integer dynamicRegister);

    /**
     * 更新产品预注册状态
     *
     * @param id 编号
     * @param preRegister 预注册状态
     */
    void updatePreRegister(Long id, Integer preRegister);

}