package cn.powerchina.bjy.link.iot.api.transportTarget;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.link.iot.api.transportTarget.dto.TransportTargetRespDTO;
import cn.powerchina.bjy.link.iot.dal.dataobject.transporttarget.TransportTargetDO;
import cn.powerchina.bjy.link.iot.service.transporttarget.TransportTargetService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Description: 转发目标
 * @Author: handl
 * @CreateDate: 2025/7/11
 */
@RestController
@Validated
public class TransportTargetApiImpl implements TransportTargetApi {

    @Autowired
    private TransportTargetService transportTargetService;

    @Override
    public CommonResult<List<TransportTargetRespDTO>> getTransportTarget(String target) {
        List<TransportTargetDO> targetList = transportTargetService.getTransportTargetList();
        List<TransportTargetRespDTO> respDTOList = targetList.stream().map(item -> {
            TransportTargetRespDTO respDTO = new TransportTargetRespDTO();
            BeanUtils.copyProperties(item, respDTO);
            return respDTO;
        }).toList();
        return CommonResult.success(respDTOList);
    }
}
