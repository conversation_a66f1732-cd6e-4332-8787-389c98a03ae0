package cn.powerchina.bjy.link.iot.service.edgegateway;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.link.iot.controller.admin.edgegateway.bo.EdgeGatewayBO;
import cn.powerchina.bjy.link.iot.controller.admin.edgegateway.vo.*;
import cn.powerchina.bjy.link.iot.dal.dataobject.edgegateway.EdgeGatewayDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.edgegateway.EdgeGatewayDeviceDO;
import cn.powerchina.bjy.link.iot.dto.down.EdgeSyncUpDTO;
import jakarta.validation.Valid;

import java.util.Date;

/**
 * 边缘网关 Service 接口
 *
 * <AUTHOR>
 */
public interface EdgeGatewayService {

    /**
     * 创建边缘网关
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createEdgeGateway(@Valid EdgeGatewaySaveReqVO createReqVO);

    /**
     * 更新边缘网关
     *
     * @param updateReqVO 更新信息
     */
    void updateEdgeGateway(@Valid EdgeGatewaySaveReqVO updateReqVO);

    /**
     * 删除边缘网关
     *
     * @param id 编号
     */
    void deleteEdgeGateway(Long id);

    /**
     * 获得边缘网关
     *
     * @param id 编号
     * @return 边缘网关
     */
    EdgeGatewayDO getEdgeGateway(Long id);

    /**
     * 获得边缘网关分页
     *
     * @param pageReqVO 分页查询
     * @return 边缘网关分页
     */
    PageResult<EdgeGatewayBO> getEdgeGatewayPage(EdgeGatewayPageReqVO pageReqVO);

    /**
     * 获得边缘实例下一级的设备
     * @param pageReqVO 分页查询条件
     * @return 边缘实例下一级的设备
     */
    PageResult<EdgeDevicePageResVO> getEdgeDevicePage(EdgeDevicePageReqVO pageReqVO);

    /**
     * 获得边缘实例下一级的设备的子设备
     * @param pageReqVO 分页查询条件
     * @return 边缘实例下一级的设备的子设备
     */
    PageResult<EdgeChildDevicePageResVO> getEdgeChildDevicePage(EdgeChildDevicePageReqVO pageReqVO);

    PageResult<EdgeGatewayDeviceDO> getEdgeGatewayDevicePage(EdgeGatewayPageReqVO pageReqVO);

    /**
     * 根据网关实例code查询网关应用名称
     *
     * @param edgeCode
     * @return
     */
    String getEdgeServiceNameByCode(String edgeCode);

    void receiveRegister(EdgeSyncUpDTO edgeSyncUpDTO);

    /**
     * 边缘网关设备状态
     *
     * @param edgeCode 网关实例编码
     */
    void edgeOnlineCheck(String edgeCode);

    /**
     * 修改网关实例在线状态
     *
     * @param edgeCode
     * @param edgeStatus
     * @param changeTime 变化时间
     * @return
     */
    Integer modifyEdgeGateWayEdgeStatus(String edgeCode, Integer edgeStatus, Date changeTime);

    /**
     * 获得边缘网关
     *
     * @param id
     * @return
     */
    EdgeGatewayBO getEdgeGatewayBO(Long id);

    /**
     * 检测边缘网关在线离线
     */
    void detectEdgeGatewayOnlineStatus();

    void processCameraMove(@Valid CameraMoveReqVO moveReqVO);

    String convertVideo(@Valid  ConvertVideoReqVO convertVideoReqVO)throws  Exception;

    void closeVideo(String videoName);

    void keepAliveVideo(String videoName);
}