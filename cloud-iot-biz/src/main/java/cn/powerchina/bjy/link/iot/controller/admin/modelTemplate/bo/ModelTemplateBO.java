package cn.powerchina.bjy.link.iot.controller.admin.modelTemplate.bo;

import cn.powerchina.bjy.link.iot.dal.dataobject.modelTemplate.ModelTemplateDO;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 物模板 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ModelTemplateBO extends ModelTemplateDO {

    /**
     * 引用产品数
     */
    private Integer productsNumber;

    /**
     * 物模板分类名称
     */
    private String categorizeName;
}
