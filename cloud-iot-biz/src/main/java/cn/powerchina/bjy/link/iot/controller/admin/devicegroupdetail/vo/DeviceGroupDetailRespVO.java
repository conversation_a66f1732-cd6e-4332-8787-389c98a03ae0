package cn.powerchina.bjy.link.iot.controller.admin.devicegroupdetail.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 设备分组明细 Response VO")
@Data
@ExcelIgnoreUnannotated
public class DeviceGroupDetailRespVO {

    @Schema(description = "主键id")
    @ExcelProperty("主键id")
    private Long id;

    @Schema(description = "设备分组id")
    @ExcelProperty("设备分组id")
    private Long deviceGroupId;

    @Schema(description = "设备名称")
    @ExcelProperty("设备名称")
    private String deviceName;

    @Schema(description = "设备编号")
    @ExcelProperty("设备编号")
    private String deviceCode;

    @Schema(description = "设备唯一标识")
    @ExcelProperty("设备唯一标识")
    private String deviceSerial;

    @Schema(description = "产品编码")
    @ExcelProperty("产品编码")
    private String productCode;

    @Schema(description = "产品名称")
    @ExcelProperty("产品名称")
    private String productName;

    @Schema(description = "节点类型(0直连，1网关，2网关子设备）")
    @ExcelProperty("节点类型(0直连，1网关，2网关子设备）")
    private Integer nodeType;

    @Schema(description = "连接状态（0-离线；1-在线；）")
    @ExcelProperty("连接状态（0-离线；1-在线；）")
    private Integer linkState;

    @Schema(description = "设备描述")
    @ExcelProperty("设备描述")
    private String remark;

}