package cn.powerchina.bjy.link.iot.controller.admin.devicecurrentattribute;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.link.iot.controller.admin.devicecurrentattribute.vo.DeviceShadowPageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.devicecurrentattribute.vo.DeviceShadowRespVO;
import cn.powerchina.bjy.link.iot.controller.admin.devicecurrentattribute.vo.DeviceShadowSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.devicecurrentattribute.DeviceShadowDO;
import cn.powerchina.bjy.link.iot.service.devicecurrentattribute.DeviceShadowService;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.*;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;


@Tag(name = "管理后台 - 设备上报的最新属性")
@RestController
@RequestMapping("/iot/device-current-attribute")
@Validated
public class DeviceShadowController {

    @Resource
    private DeviceShadowService deviceShadowService;

    @PostMapping("/create")
    @Operation(summary = "创建设备上报的最新属性")
    @PreAuthorize("@ss.hasPermission('iot:device-current-attribute:create')")
    public CommonResult<Long> createDeviceShadow(@Valid @RequestBody DeviceShadowSaveReqVO createReqVO) {
        return success(deviceShadowService.createDeviceShadow(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新设备上报的最新属性")
    @PreAuthorize("@ss.hasPermission('iot:device-current-attribute:update')")
    public CommonResult<Boolean> updateDeviceShadow(@Valid @RequestBody DeviceShadowSaveReqVO updateReqVO) {
        deviceShadowService.updateDeviceShadow(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除设备上报的最新属性")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('iot:device-current-attribute:delete')")
    public CommonResult<Boolean> deleteDeviceShadow(@RequestParam("id") Long id) {
        deviceShadowService.deleteDeviceShadow(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得设备上报的最新属性")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('iot:device-current-attribute:query')")
    public CommonResult<DeviceShadowRespVO> getDeviceShadow(@RequestParam("id") Long id) {
        DeviceShadowDO deviceShadow = deviceShadowService.getDeviceShadow(id);
        return success(BeanUtils.toBean(deviceShadow, DeviceShadowRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得设备上报的最新属性分页")
    @PreAuthorize("@ss.hasPermission('iot:device-current-attribute:query')")
    public CommonResult<PageResult<DeviceShadowRespVO>> getDeviceShadowPage(@Valid DeviceShadowPageReqVO pageReqVO) {
        PageResult<DeviceShadowDO> pageResult = deviceShadowService.getDeviceShadowPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, DeviceShadowRespVO.class));
    }



}