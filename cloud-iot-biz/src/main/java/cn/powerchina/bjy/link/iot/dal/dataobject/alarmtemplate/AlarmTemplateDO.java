package cn.powerchina.bjy.link.iot.dal.dataobject.alarmtemplate;

import cn.powerchina.bjy.cloud.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;
import com.baomidou.mybatisplus.annotation.*;

/**
 * 告警模板 DO
 *
 * <AUTHOR>
 */
@TableName("iot_alarm_template")
@KeySequence("iot_alarm_template_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AlarmTemplateDO extends BaseDO {

    /**
     * 主键id
     */
    @TableId
    private Long id;
    /**
     * 规则ID
     */
    private Long ruleId;
    private Long actionId;
    /**
     * 告警名称
     */
    private String alarmName;
    /**
     * 告警等级(1-轻微,2-中等,3-严重,4-非常严重)
     */
    private Integer alarmLevel;
    /**
     * 告警描述
     */
    private String alarmContent;
    /**
     * 排序
     */
    private Integer sort;

}
