package cn.powerchina.bjy.link.iot.job;

import lombok.Data;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;

import java.io.IOException;
import java.io.Serializable;

@Data
public class XxlJobInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 任务ID（主键，自增） */
    private Integer id;

    /** 执行器ID（对应 xxl_job_group 表的 id） */
    private Integer jobGroup;

    /** 任务描述（显示在管理端界面） */
    private String jobDesc;

    /** 添加时间（格式：yyyy-MM-dd HH:mm:ss） */
    private String addTime;

    /** 更新时间（格式：yyyy-MM-dd HH:mm:ss） */
    private String updateTime;

    /** 任务负责人（通常为用户名或邮箱） */
    private String author;

    /** 告警邮箱（多个邮箱用逗号分隔） */
    private String alarmEmail;

    /** 调度类型（值：CRON/FIX_RATE/FIX_DELAY） */
    private String scheduleType;

    /** 调度配置：
     *  - CRON类型：Cron表达式
     *  - FIX_RATE类型：固定速率（单位：毫秒）
     *  - FIX_DELAY类型：固定延迟（单位：毫秒）
     */
    private String scheduleConf;

    /** 调度过期策略（值：DO_NOTHING/FIRE_ONCE_NOW） */
    private String misfireStrategy;

    /** 执行器路由策略（值：FIRST/NORMAL/RANDOM/ROUND_ROBIN/CONSISTENT_HASH/LEAST_FREQUENTLY_USED/LEAST_RECENTLY_USED） */
    private String executorRouteStrategy;

    /** 执行器Handler名称（对应执行器中注册的方法名） */
    private String executorHandler;

    /** 执行器参数（传递给执行器的参数） */
    private String executorParam;

    /** 执行器阻塞策略（值：SERIAL_EXECUTION/DISCARD_LATER/COVER_EARLY） */
    private String executorBlockStrategy;

    /** 执行器超时时间（单位：秒，0表示不限制） */
    private Integer executorTimeout;

    /** 失败重试次数（0表示不重试） */
    private Integer executorFailRetryCount;

    /** GLUE代码类型（值：BEAN/GLUE/GROOVY等，通常使用 BEAN 表示自定义执行器） */
    private String glueType;

    /** GLUE代码内容（当 glueType 为 GLUE 时有效） */
    private String glueSource;

    /** GLUE代码备注（描述代码用途） */
    private String glueRemark;

    /** GLUE代码更新时间（格式：yyyy-MM-dd HH:mm:ss） */
    private String glueUpdatetime;

    /** 子任务ID（多个任务ID用逗号分隔，用于任务链） */
    private String childJobid;

    /** 调度状态（值：0-停止，1-运行） */
    private Integer triggerStatus;

    /** 上次调度时间（时间戳，单位：毫秒） */
    private Long triggerLastTime;

    /** 下次调度时间（时间戳，单位：毫秒） */
    private Long triggerNextTime;


}
