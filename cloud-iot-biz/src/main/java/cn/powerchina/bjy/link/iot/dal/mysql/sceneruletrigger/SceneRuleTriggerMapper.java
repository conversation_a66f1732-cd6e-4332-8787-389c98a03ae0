package cn.powerchina.bjy.link.iot.dal.mysql.sceneruletrigger;

import java.util.*;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.iot.controller.admin.sceneruletrigger.vo.SceneRuleTriggerPageReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.sceneruletrigger.SceneRuleTriggerDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 规则触发/条件限制 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface SceneRuleTriggerMapper extends BaseMapperX<SceneRuleTriggerDO> {

    default PageResult<SceneRuleTriggerDO> selectPage(SceneRuleTriggerPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<SceneRuleTriggerDO>()
                .eqIfPresent(SceneRuleTriggerDO::getRuleId, reqVO.getRuleId())
                .eqIfPresent(SceneRuleTriggerDO::getConditionType, reqVO.getConditionType())
                .eqIfPresent(SceneRuleTriggerDO::getTriggerType, reqVO.getTriggerType())
                .eqIfPresent(SceneRuleTriggerDO::getDeviceTriggerType, reqVO.getDeviceTriggerType())
                .eqIfPresent(SceneRuleTriggerDO::getProductCode, reqVO.getProductCode())
                .eqIfPresent(SceneRuleTriggerDO::getDeviceCode, reqVO.getDeviceCode())
                .eqIfPresent(SceneRuleTriggerDO::getAttributeExpression, reqVO.getAttributeExpression())
                .eqIfPresent(SceneRuleTriggerDO::getEventIdentity, reqVO.getEventIdentity())
                .eqIfPresent(SceneRuleTriggerDO::getOnlineStatus, reqVO.getOnlineStatus())
                .eqIfPresent(SceneRuleTriggerDO::getSort, reqVO.getSort())
                .betweenIfPresent(SceneRuleTriggerDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(SceneRuleTriggerDO::getId));
    }

}
