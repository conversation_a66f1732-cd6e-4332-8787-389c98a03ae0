package cn.powerchina.bjy.link.iot.dal.dataobject.mqttauth;

import cn.powerchina.bjy.cloud.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;
import com.baomidou.mybatisplus.annotation.*;

/**
 * mqtt认证 DO
 *
 * <AUTHOR>
 */
@TableName("iot_mqtt_auth")
@KeySequence("iot_mqtt_auth_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MqttAuthDO extends BaseDO {

    /**
     * 主键id
     */
    @TableId
    private Long id;
    /**
     * 用户名
     */
    private String userName;
    /**
     * 密钥
     */
    private String secret;
    /**
     * 认证类型(1:应用管理;2:产品管理;3:设备管理)
     */
    private Integer type;
    /**
     * 状态(1:启用;0:禁用)
     */
    private Integer status;

}