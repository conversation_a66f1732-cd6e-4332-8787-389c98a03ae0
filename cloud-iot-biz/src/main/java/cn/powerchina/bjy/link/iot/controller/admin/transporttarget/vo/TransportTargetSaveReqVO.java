package cn.powerchina.bjy.link.iot.controller.admin.transporttarget.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(description = "管理后台 - 转发规则-转发目标新增/修改 Request VO")
@Data
public class TransportTargetSaveReqVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "2097")
    private Long id;

    @Schema(description = "规则id", requiredMode = Schema.RequiredMode.REQUIRED, example = "26611")
    @NotNull(message = "规则id不能为空")
    private Long ruleId;

    @Schema(description = "目标名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    @NotEmpty(message = "目标名称不能为空")
    private String name;

    @Schema(description = "转发类型(1:HTTP推送 2MQTT推送)", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "转发类型(1:HTTP推送 2MQTT推送)不能为空")
    private Integer transportType;

    @Schema(description = "url地址")
    private String urlAddress;

    @Schema(description = "token")
    private String token;

    @Schema(description = "转发topic")
    private String topic;

    @Schema(description = "描述")
    private String remark;

}