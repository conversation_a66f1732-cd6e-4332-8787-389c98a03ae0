package cn.powerchina.bjy.link.iot.service.mqttauth;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.iot.controller.admin.mqttauth.vo.MqttAuthPageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.mqttauth.vo.MqttAuthSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.mqttauth.MqttAuthDO;
import cn.powerchina.bjy.link.iot.dal.mysql.mqttauth.MqttAuthMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.link.iot.enums.ErrorCodeConstants.MQTT_AUTH_NOT_EXISTS;

/**
 * mqtt认证 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class MqttAuthServiceImpl implements MqttAuthService {

    @Resource
    private MqttAuthMapper mqttAuthMapper;

    @Override
    public Long createMqttAuth(MqttAuthSaveReqVO createReqVO) {
        // 插入
        MqttAuthDO mqttAuth = BeanUtils.toBean(createReqVO, MqttAuthDO.class);
        mqttAuthMapper.insert(mqttAuth);
        // 返回
        return mqttAuth.getId();
    }

    @Override
    public void updateMqttAuth(MqttAuthSaveReqVO updateReqVO) {
        // 校验存在
        validateMqttAuthExists(updateReqVO.getId());
        // 更新
        MqttAuthDO updateObj = BeanUtils.toBean(updateReqVO, MqttAuthDO.class);
        mqttAuthMapper.updateById(updateObj);
    }

    @Override
    public void updateMqttAuth(String userName, String secret) {
        MqttAuthDO updateObj = Optional.ofNullable(mqttAuthMapper.selectList(new LambdaQueryWrapperX<MqttAuthDO>().eq(MqttAuthDO::getUserName, userName))).orElse(new ArrayList<>()).stream().findFirst().orElse(null);
        if (Objects.nonNull(updateObj)) {
            updateObj.setSecret(secret);
            mqttAuthMapper.updateById(updateObj);
        }
    }

    /**
     * 更新mqtt认证的状态
     * @param userName 用户名
     * @param status 状态
     */
    @Override
    public void updateMqttAuthStatus(String userName, Integer status) {
        MqttAuthDO updateObj = Optional.ofNullable(mqttAuthMapper.selectList(new LambdaQueryWrapperX<MqttAuthDO>().eq(MqttAuthDO::getUserName, userName))).orElse(new ArrayList<>()).stream().findFirst().orElse(null);
        if (Objects.nonNull(updateObj)) {
            updateObj.setStatus(status);
            mqttAuthMapper.updateById(updateObj);
        }
    }

    @Override
    public void deleteMqttAuth(Long id) {
        // 校验存在
        validateMqttAuthExists(id);
        // 删除
        mqttAuthMapper.deleteById(id);
    }

    /**
     * 根据用户名删除
     * @param userName 用户名
     */
    @Override
    public void deleteByUserName(String userName) {
        mqttAuthMapper.delete(new LambdaQueryWrapper<MqttAuthDO>().eq(MqttAuthDO::getUserName, userName));
    }

    /**
     * 根据多个用户名批量删除
     * @param userNameList 用户名列表
     */
    @Override
    public void deleteByUserNameList(List<String> userNameList) {
        if (CollectionUtils.isNotEmpty(userNameList)) {
            mqttAuthMapper.delete(new LambdaQueryWrapper<MqttAuthDO>().in(MqttAuthDO::getUserName, userNameList));
        }
    }

    private void validateMqttAuthExists(Long id) {
        if (mqttAuthMapper.selectById(id) == null) {
            throw exception(MQTT_AUTH_NOT_EXISTS);
        }
    }

    @Override
    public MqttAuthDO getMqttAuth(Long id) {
        return mqttAuthMapper.selectById(id);
    }

    @Override
    public PageResult<MqttAuthDO> getMqttAuthPage(MqttAuthPageReqVO pageReqVO) {
        return mqttAuthMapper.selectPage(pageReqVO);
    }

}