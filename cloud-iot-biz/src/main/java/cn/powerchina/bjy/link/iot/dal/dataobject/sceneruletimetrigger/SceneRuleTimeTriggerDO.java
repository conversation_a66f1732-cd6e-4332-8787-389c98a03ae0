package cn.powerchina.bjy.link.iot.dal.dataobject.sceneruletimetrigger;

import cn.powerchina.bjy.cloud.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;
import com.baomidou.mybatisplus.annotation.*;

import java.time.LocalDate;

/**
 * 定时触发 DO
 *
 * <AUTHOR>
 */
@TableName("iot_scene_rule_time_trigger")
@KeySequence("iot_scene_rule_time_trigger_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SceneRuleTimeTriggerDO extends BaseDO {

    /**
     * 主键id
     */
    @TableId
    private Long id;
    /**
     * 规则ID
     */
    private Long ruleId;
    private Long triggerId;
    /**
     * 执行时刻
     */
    private String executionTime;
    /**
     * 重复类型:1-每天,2-指定日期,3-指定周期,4-自定义
     */
    private Integer repeatType;
    /**
     * 开始日期/指定日期
     */
    private LocalDate repeatStartDate;
    /**
     * 结束日期
     */
    private LocalDate repeatEndDate;
    /**
     * 每周重复的星期几,如:1,2,3,4,5,6,7
     */
    private String repeatWeekDays;
    /**
     * cron表达式
     */
    private String cronExpression;
    private String jobId;
    /**
     * 排序
     */
    private Integer sort;

}
