package cn.powerchina.bjy.link.iot.dal.mysql.modelTemplate;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.iot.controller.admin.modelTemplate.vo.ModelTemplatePageReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.modelTemplate.ModelTemplateDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface ModelTemplateMapper extends BaseMapperX<ModelTemplateDO> {

    default PageResult<ModelTemplateDO> selectPage(ModelTemplatePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ModelTemplateDO>()
                .likeIfPresent(ModelTemplateDO::getTemplateName, reqVO.getTemplateName())
                .eqIfPresent(ModelTemplateDO::getCategorizeTwoId, reqVO.getCategorizeTwoId())
                .eqIfPresent(ModelTemplateDO::getCategorizeOneId, reqVO.getCategorizeOneId())
                .betweenIfPresent(ModelTemplateDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ModelTemplateDO::getId));
    }
    @Select("select count(1) from iot_model_template imt where imt.categorize_two_id = #{categorizeId}")
    int selectCountByCategorizeId(@Param("categorizeId") Long categorizeId);
    @Select("select * from iot_model_template imt where imt.categorize_two_id=#{categorizeId}")
    List<ModelTemplateDO> selectByCategorizeChildrenId(@Param("categorizeId") Long categorizeId);
}
