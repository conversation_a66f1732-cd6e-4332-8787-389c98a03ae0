package cn.powerchina.bjy.link.iot.controller.admin.deviceeventlog.vo;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


@Schema(description = "管理后台 - 设备事件日志分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DeviceEventLogPageReqVO extends PageParam {

    @Schema(description = "资源空间ID", example = "14286")
    private Long resourceSpaceId;

    @Schema(description = "产品编码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String productCode;

    @Schema(description = "产品编号集合")
    private List<String> productCodes;

    @Schema(description = "设备编号")
    private String deviceCode;

    @Schema(description = "设备唯一标识")
    private String deviceSerial;

    @Schema(description = "设备编号集合")
    private List<String> deviceCodes;

    @Schema(description = "设备名称", example = "")
    private String deviceName;

    @Schema(description = "物模型标识符")
    private String thingIdentity;

    @Schema(description = "物模型名称", example = "")
    private String thingName;

    @Schema(description = "类型", example = "2")
    private Integer eventType;

    @Schema(description = "物模型日志值")
    private String thingValue;

    @Schema(description = "模式(1=影子模式，2=在线模式，3=其他)")
    private Integer deviceMode;

    @Schema(description = "备注", example = "随便")
    private String remark;

    @Schema(description = "触发时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm")
    private LocalDateTime[] triggerTime;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;


}