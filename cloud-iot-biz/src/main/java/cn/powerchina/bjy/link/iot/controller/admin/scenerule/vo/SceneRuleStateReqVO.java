package cn.powerchina.bjy.link.iot.controller.admin.scenerule.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(description = "管理后台 - 场景规则状态切换 Request VO")
@Data
public class SceneRuleStateReqVO {
    @Schema(description = "主键")
    @NotNull(message = "id不能为空")
    private Long id;

    @Schema(description = "切换后的状态值")
    @NotNull(message = "切换后的状态值不能为空")
    private Integer status;
}

