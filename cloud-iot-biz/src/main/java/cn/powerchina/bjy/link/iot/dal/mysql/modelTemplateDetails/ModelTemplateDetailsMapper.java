package cn.powerchina.bjy.link.iot.dal.mysql.modelTemplateDetails;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.iot.controller.admin.modelTemplateDetails.vo.ModelTemplateDetailsPageReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.modelTemplateDetails.ModelTemplateDetailsDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface ModelTemplateDetailsMapper extends BaseMapperX<ModelTemplateDetailsDO> {

    default PageResult<ModelTemplateDetailsDO> selectPage(ModelTemplateDetailsPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ModelTemplateDetailsDO>()
                .likeIfPresent(ModelTemplateDetailsDO::getTemplateDetailsName, reqVO.getTemplateDetailsName())
                .eqIfPresent(ModelTemplateDetailsDO::getTemplateId, reqVO.getTemplateId())
                .eqIfPresent(ModelTemplateDetailsDO::getTemplateType, reqVO.getTemplateType())
                .eqIfPresent(ModelTemplateDetailsDO::getTemplateIdentity, reqVO.getTemplateIdentity())
                .betweenIfPresent(ModelTemplateDetailsDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ModelTemplateDetailsDO::getId));
    }
    default List<ModelTemplateDetailsDO> selectListByTemplateId(Long templateId) {
        return selectList(new LambdaQueryWrapperX<ModelTemplateDetailsDO>()
                .eqIfPresent(ModelTemplateDetailsDO::getTemplateId, templateId));
    }
}
