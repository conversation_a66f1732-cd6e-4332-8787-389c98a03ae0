package cn.powerchina.bjy.link.iot.service.devicepropertylog;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.iot.controller.admin.devicepropertylog.vo.DevicePropertyLogModelRespVO;
import cn.powerchina.bjy.link.iot.controller.admin.devicepropertylog.vo.DevicePropertyLogPageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.devicepropertylog.vo.DevicePropertyLogSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.device.DeviceDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.devicecurrentattribute.DeviceShadowDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.devicepropertylog.DevicePropertyLogDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.productmodel.ProductModelDO;
import cn.powerchina.bjy.link.iot.dal.mysql.devicepropertylog.DevicePropertyLogMapper;
import cn.powerchina.bjy.link.iot.dto.up.EdgeReadPropertyValue;
import cn.powerchina.bjy.link.iot.enums.ThingModeTypeEnum;
import cn.powerchina.bjy.link.iot.model.DeviceTransportModel;
import cn.powerchina.bjy.link.iot.service.device.DeviceService;
import cn.powerchina.bjy.link.iot.service.devicecurrentattribute.DeviceShadowService;
import cn.powerchina.bjy.link.iot.service.productmodel.ProductModelService;
import cn.powerchina.bjy.link.iot.service.transportrule.TransportRuleService;
import cn.powerchina.bjy.link.iot.strategy.ProductModelCheckService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import java.util.*;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.link.iot.enums.ErrorCodeConstants.DEVICE_PROPERTY_LOG_NOT_EXISTS;

/**
 * 设备属性日志 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@Validated
public class DevicePropertyLogServiceImpl implements DevicePropertyLogService {

    @Resource
    private DevicePropertyLogMapper devicePropertyLogMapper;
    @Resource
    private ProductModelService productModelService;
    @Resource
    private DeviceService deviceService;
    @Resource
    private ProductModelCheckService productModelCheckService;
    @Resource
    private TransportRuleService transportRuleService;
    @Resource
    DeviceShadowService deviceShadowService;

    @Override
    public Long createDevicePropertyLog(DevicePropertyLogSaveReqVO createReqVO) {
        // 插入
        DevicePropertyLogDO devicePropertyLog = BeanUtils.toBean(createReqVO, DevicePropertyLogDO.class);
        devicePropertyLogMapper.insert(devicePropertyLog);
        // 返回
        return devicePropertyLog.getId();
    }

    @Override
    public void updateDevicePropertyLog(DevicePropertyLogSaveReqVO updateReqVO) {
        // 校验存在
        validateDevicePropertyLogExists(updateReqVO.getId());
        // 更新
        DevicePropertyLogDO updateObj = BeanUtils.toBean(updateReqVO, DevicePropertyLogDO.class);
        devicePropertyLogMapper.updateById(updateObj);
    }

    @Override
    public void deleteDevicePropertyLog(Long id) {
        // 校验存在
        validateDevicePropertyLogExists(id);
        // 删除
        devicePropertyLogMapper.deleteById(id);
    }

    private void validateDevicePropertyLogExists(Long id) {
        if (devicePropertyLogMapper.selectById(id) == null) {
            throw exception(DEVICE_PROPERTY_LOG_NOT_EXISTS);
        }
    }

    @Override
    public DevicePropertyLogDO getDevicePropertyLog(Long id) {
        return devicePropertyLogMapper.selectById(id);
    }

    @Override
    public PageResult<DevicePropertyLogDO> getDevicePropertyLogPage(DevicePropertyLogPageReqVO pageReqVO) {
        return devicePropertyLogMapper.selectPage(pageReqVO);
    }


    public List<DevicePropertyLogDO> getDevicePropertyLog(List<String> thingIdentityList, String deviceCode) {
        QueryWrapper<DevicePropertyLogDO> wrapper = new QueryWrapper<>();
        wrapper.lambda().in(DevicePropertyLogDO::getThingIdentity, thingIdentityList);
        wrapper.lambda().eq(DevicePropertyLogDO::getDeviceCode, deviceCode);
        List<DevicePropertyLogDO> devicePropertyLogList = devicePropertyLogMapper.selectList(wrapper);
        return devicePropertyLogList.stream().filter(devicePropertyLogDO -> devicePropertyLogList.stream().filter(devicePropertyLogDO1 -> devicePropertyLogDO.getThingIdentity().equals(devicePropertyLogDO1.getThingIdentity())).max(Comparator.comparing(DevicePropertyLogDO::getCreateTime)).get().getCreateTime().equals(devicePropertyLogDO.getCreateTime())).toList();

    }

    @Override
    public PageResult<DevicePropertyLogModelRespVO> getDevicePropertyLogRunningState(String productCode, String deviceCode, String thingName, PageParam pageParam, Integer[] readOrWriteType) {
        //获取产品属性物模型
        PageResult<ProductModelDO> productModelPage = productModelService.getProductModelPropertyPage(productCode, thingName, pageParam, readOrWriteType, ThingModeTypeEnum.PROPERTY.getType());
        List<ProductModelDO> productModelPageList = productModelPage.getList();
        if (productModelPageList.isEmpty()) return new PageResult<>(Collections.emptyList(), 0L);
        //根据  产品模型标识符list 和 设备编码  查询 设备运行状态事件日志
        List<DevicePropertyLogModelRespVO> devicePropertyAndLogDOS = productModelPageList.stream().map(productModel -> {
            DevicePropertyLogModelRespVO devicePropertyLogRespVO = new DevicePropertyLogModelRespVO();
            devicePropertyLogRespVO.setId(productModel.getId());
            devicePropertyLogRespVO.setThingName(productModel.getThingName());
            devicePropertyLogRespVO.setThingIdentity(productModel.getThingIdentity());
            devicePropertyLogRespVO.setReadWriteType(productModel.getReadWriteType());
            devicePropertyLogRespVO.setRemark(productModel.getRemark());
            devicePropertyLogRespVO.setDatatype(productModel.getDatatype());
            devicePropertyLogRespVO.setInputParams(productModel.getInputParams());
            devicePropertyLogRespVO.setOutputParams(productModel.getOutputParams());
            devicePropertyLogRespVO.setExtra(productModel.getExtra());
            //不从设备属性日志表获取属性值，改从设备影子表获取属性值
            //DevicePropertyLogDO propertyLogDO = getDevicePropertyLogLatestByThingIdentity(deviceCode, productModel.getThingIdentity());
            DeviceShadowDO deviceShadowDO = deviceShadowService.getShadowByDeviceCodeAndThingIdentity(deviceCode, productModel.getThingIdentity());

            if (Objects.nonNull(deviceShadowDO)) {
                devicePropertyLogRespVO.setThingValue(deviceShadowDO.getThingValue());
                devicePropertyLogRespVO.setCreateTime(deviceShadowDO.getReportTime());
            }
            return devicePropertyLogRespVO;
        }).toList();
        return new PageResult<>(devicePropertyAndLogDOS, productModelPage.getTotal());
    }

    /**
     * 根据产品物模型标识符获取最新的属性值
     *
     * @param deviceCode
     * @param thingIdentity
     * @return
     */
    private DevicePropertyLogDO getDevicePropertyLogLatestByThingIdentity(String deviceCode, String thingIdentity) {
        return devicePropertyLogMapper.selectOne(new LambdaQueryWrapperX<DevicePropertyLogDO>()
                .eq(DevicePropertyLogDO::getDeviceCode, deviceCode)
                .eq(DevicePropertyLogDO::getThingIdentity, thingIdentity)
                .orderByDesc(DevicePropertyLogDO::getCreateTime)
                .last("limit 1"));
    }

    @Override
    public void savePropertyLog(List<EdgeReadPropertyValue.EdgeDevicePropertyValueDTO> propertyValueList, String edgeCode) {
        List<DeviceTransportModel> transportModelList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(propertyValueList)) {
            propertyValueList.forEach(item -> {
                if (StringUtils.isBlank(item.getThingValue())) {
                    return;
                }
                DeviceDO deviceDO = deviceService.getDevice(item.getDeviceCode());
                if (Objects.isNull(deviceDO)) {
                    if (StringUtils.isNotBlank(item.getSlaveId()) && StringUtils.isNotBlank(edgeCode)) {
                        deviceDO = deviceService.getDeviceBySlaveAndEdgeCode(item.getSlaveId(), edgeCode);
                    }
                    if (Objects.isNull(deviceDO)) {
                        return;
                    }
                }
                ProductModelDO modelDO = productModelService.getProductModel(deviceDO.getProductCode(), item.getThingIdentity());
                if (Objects.isNull(modelDO)) {
                    return;
                }
                boolean lose = productModelCheckService.shouldIgnore(modelDO.getDatatype(), item.getThingValue(), modelDO.getInputParams(), item);
                if (lose) {
                    log.warn("属性值不在规定范围内，丢弃，值：【{}】，规则：【{}】", item.getThingValue(), modelDO.getInputParams());
                    return;
                }
                saveProperty(deviceDO, modelDO, item.getThingValue());
                //进行数据转发
                transportModelList.add(DeviceTransportModel.builder().deviceCode(deviceDO.getDeviceCode())
                        .mcuChannel(deviceDO.getMcuChannel()).thingIdentity(modelDO.getThingIdentity())
                        .thingValue(item.getThingValue()).productCode(deviceDO.getProductCode()).build());
            });
        }
        if (!CollectionUtils.isEmpty(transportModelList)) {
            transportRuleService.transportData(transportModelList);
        }
        log.info("属性上报保存成功.");
    }

    /**
     * 保存属性值
     *
     * @param deviceDO
     * @param modelDO
     * @param value
     */
    private void saveProperty(DeviceDO deviceDO, ProductModelDO modelDO, String value) {
        DevicePropertyLogSaveReqVO saveReqVO = new DevicePropertyLogSaveReqVO();
        saveReqVO.setDeviceCode(deviceDO.getDeviceCode());
        saveReqVO.setDeviceName(deviceDO.getDeviceName());
        saveReqVO.setThingIdentity(modelDO.getThingIdentity());
        saveReqVO.setThingName(modelDO.getThingName());
        saveReqVO.setThingValue(value);
        createDevicePropertyLog(saveReqVO);
    }

}