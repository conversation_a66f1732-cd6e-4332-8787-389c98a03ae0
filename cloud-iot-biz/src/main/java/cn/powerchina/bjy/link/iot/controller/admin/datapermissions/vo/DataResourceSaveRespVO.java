package cn.powerchina.bjy.link.iot.controller.admin.datapermissions.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 数据权限新增/修改 Request VO")
@Data
public class DataResourceSaveRespVO {


    @Schema(description = "资源空间ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "27371")
    @NotNull(message = "资源空间ID不能为空")
    private String resourceSpaceId;

    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "27371")
    @NotNull(message = "产品集合")
    private List<DataProductSaveRespVO> productList;

    @Schema(description = "边缘计算集合", requiredMode = Schema.RequiredMode.REQUIRED, example = "27371")
    @NotNull(message = "边缘计算集合")
    private List<DataGatewaySaveRespVO> edgeList;


}
