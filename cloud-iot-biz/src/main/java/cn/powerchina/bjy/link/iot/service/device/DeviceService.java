package cn.powerchina.bjy.link.iot.service.device;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.link.iot.controller.admin.device.vo.DeviceAndProductVO;
import cn.powerchina.bjy.link.iot.controller.admin.device.vo.DeviceCountReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.device.vo.DevicePageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.device.vo.DeviceSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.device.DeviceDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.product.ProductDO;
import cn.powerchina.bjy.link.iot.dto.up.EdgeCheckOnlineStatus;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.springframework.web.multipart.MultipartFile;

import java.util.Collection;
import java.util.List;
import java.util.Map;


/**
 * 设备 Service 接口
 *
 * <AUTHOR>
 */
public interface DeviceService {

    /**
     * 创建设备
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createDevice(@Valid DeviceSaveReqVO createReqVO);

    /**
     * 更新设备
     *
     * @param updateReqVO 更新信息
     */
    void updateDevice(@Valid DeviceSaveReqVO updateReqVO);

    /**
     * 根据deviceCode更新注册状态
     *
     * @param deviceCode 设备编码
     * @param state      注册状态
     */
    void updateDeviceRegisterState(String deviceCode, Integer state);

    void updateLinkState(Long id, Integer linkState);

    /**
     * 删除设备
     *
     * @param id 编号
     */
    void deleteDevice(Long id);

    /**
     * 获得设备
     *
     * @param id 编号
     * @return 设备
     */
    DeviceDO getDevice(Long id);

    /**
     * 获得设备
     *
     * @param ids 设备id
     * @return 设备
     */
    Map<Long, DeviceDO> getDevices(Collection<Long> ids);


    /**
     * 根据设备号获取设备
     *
     * @param deviceCode
     * @return
     */
    DeviceDO getDevice(String deviceCode);

    /**
     * 获得设备分页
     *
     * @param pageReqVO 分页查询
     * @return 设备分页
     */
    PageResult<DeviceAndProductVO> getDevicePage(DevicePageReqVO pageReqVO);

    /**
     * 获得边缘网关下的设备分页
     *
     * @param pageReqVO 分页查询
     * @return 设备分页
     */
    PageResult<DeviceAndProductVO> getGatewayDevicePage(DevicePageReqVO pageReqVO);

    /**
     * 获得设备分页
     *
     * @param pageReqVO 分页查询
     * @return 设备分页
     */
    PageResult<DeviceAndProductVO> getAllDevicePage(DevicePageReqVO pageReqVO);

    /**
     * 统计产品关联的设备数量
     *
     * @param productCode 产品编码
     * @return
     */
    Long getDeviceCountByProductCode(String productCode);

    /**
     * 获取设备各状态的数量
     *
     * @return
     */
    Map<String, Object> getDeviceCountByISOnline(DevicePageReqVO pageReqVO);

    /**
     * 根据设备编码获取设备信息
     *
     * @param deviceCode
     * @return
     */
    DeviceDO getDeviceByCode(String deviceCode);

    /**
     * 根据设备编码集合获取设备
     *
     * @param deviceCodeList
     * @return
     */
    List<DeviceDO> getDeviceByCodeList(List<String> deviceCodeList);

    void edgeOnlineCheck(EdgeCheckOnlineStatus edgeCheckOnlineStatus);

    /**
     * 通过从站号和网关实例编码获取设备
     *
     * @param slaveId  从站号
     * @param edgeCode 网关实例编码
     * @return
     */
    DeviceDO getDeviceBySlaveAndEdgeCode(String slaveId, String edgeCode);

    /**
     * 通过网关设备code和通道号获取设备
     *
     * @param parentCode
     * @param mcuChannel
     * @return
     */
    DeviceDO getDeviceByParentCodeAndMcuChannel(String parentCode, String mcuChannel);

    /**
     * 查找网关子设备
     *
     * @param parentCodes
     * @return
     */
    List<DeviceDO> findDeviceByParentCode(Collection<String> parentCodes);

    /**
     * 获取边缘实例下所有在线状态的设备
     * @param edgeCode 边缘实例编码
     * @return 在线状态的设备
     */
    List<DeviceDO> listOnlineDevicesByEdgeCode(String edgeCode);


    /**
     * 统计设备数量
     *
     * @param reqVO
     * @return
     */
    Long countDevice(DeviceCountReqVO reqVO);

    /**
     * 获得设备分页
     *
     * @param pageReqVO 分页查询
     * @return 设备分页
     */
    PageResult<DeviceAndProductVO> getGroupDevicePage(DevicePageReqVO pageReqVO);

    /**
     * 设备更换
     *
     * @param updateReqVO 更新信息
     */
    void replaceDevice(@Valid DeviceSaveReqVO updateReqVO);

    /**
     * 新增设备拓扑
     * @param deviceCodes 设备编码
     */
    void addChildDevice(String deviceCode, List<String> deviceCodes);

    /**
     * 删除设备拓扑
     * @param deviceCodes 设备编码
     */
    void delChildDevice(List<String> deviceCodes);

    /**
     * 设备批量注册
     * @param deviceList
     */
    void deviceBatchRegist(ProductDO productDO, List<DeviceDO> deviceList);

}