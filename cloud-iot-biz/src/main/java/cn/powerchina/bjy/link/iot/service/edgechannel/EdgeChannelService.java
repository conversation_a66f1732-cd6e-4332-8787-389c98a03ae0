package cn.powerchina.bjy.link.iot.service.edgechannel;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.link.iot.controller.admin.edgechannel.vo.EdgeChannelPageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.edgechannel.vo.EdgeChannelSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.edgechannel.EdgeChannelDO;
import jakarta.validation.Valid;

/**
 * 通道 Service 接口
 *
 * <AUTHOR>
 */
public interface EdgeChannelService {

    /**
     * 创建通道
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createEdgeChannel(@Valid EdgeChannelSaveReqVO createReqVO);

    /**
     * 更新通道
     *
     * @param updateReqVO 更新信息
     */
    void updateEdgeChannel(@Valid EdgeChannelSaveReqVO updateReqVO);

    /**
     * 删除通道
     *
     * @param id 编号
     */
    void deleteEdgeChannel(Long id);

    /**
     * 获得通道
     *
     * @param id 编号
     * @return 通道
     */
    EdgeChannelDO getEdgeChannel(Long id);

    /**
     * 获得通道分页
     *
     * @param pageReqVO 分页查询
     * @return 通道分页
     */
    PageResult<EdgeChannelDO> getEdgeChannelPage(EdgeChannelPageReqVO pageReqVO);

    /**
     * 根据通道编号获取通道
     * @param channelCode
     * @return
     */
    EdgeChannelDO getEdgeChannelByCode(String channelCode);
}