package cn.powerchina.bjy.link.iot.service.notificationmethod;


import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.link.iot.controller.admin.notificationmethod.vo.NotificationMethodPageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.notificationmethod.vo.NotificationMethodSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.notificationmethod.NotificationMethodDO;
import jakarta.validation.*;

import java.util.List;

/**
 * 通知方式 Service 接口
 *
 * <AUTHOR>
 */
public interface NotificationMethodService {

    void createAndUpdateNotificationMethod(List<NotificationMethodSaveReqVO> saveOrUpdateList, Long alarmTemplateId);

    List<NotificationMethodDO> getNotificationMethodByAlarmTemplateId(Long alarmTemplateId);

    /**
     * 创建通知方式
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createNotificationMethod(@Valid NotificationMethodSaveReqVO createReqVO);

    /**
     * 更新通知方式
     *
     * @param updateReqVO 更新信息
     */
    void updateNotificationMethod(@Valid NotificationMethodSaveReqVO updateReqVO);

    /**
     * 删除通知方式
     *
     * @param id 编号
     */
    void deleteNotificationMethod(Long id);

    /**
     * 获得通知方式
     *
     * @param id 编号
     * @return 通知方式
     */
    NotificationMethodDO getNotificationMethod(Long id);

    /**
     * 获得通知方式分页
     *
     * @param pageReqVO 分页查询
     * @return 通知方式分页
     */
    PageResult<NotificationMethodDO> getNotificationMethodPage(NotificationMethodPageReqVO pageReqVO);

}
