package cn.powerchina.bjy.link.iot.dal.mysql.edgechannel;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.iot.controller.admin.edgechannel.vo.EdgeChannelPageReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.edgechannel.EdgeChannelDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 通道 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface EdgeChannelMapper extends BaseMapperX<EdgeChannelDO> {

    default PageResult<EdgeChannelDO> selectPage(EdgeChannelPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<EdgeChannelDO>()
                .eqIfPresent(EdgeChannelDO::getDriverCode, reqVO.getDriverCode())
                .eqIfPresent(EdgeChannelDO::getEdgeCode, reqVO.getEdgeCode())
                .eqIfPresent(EdgeChannelDO::getConnectType, reqVO.getConnectType())
                .eqIfPresent(EdgeChannelDO::getChannelCode, reqVO.getChannelCode())
                .likeIfPresent(EdgeChannelDO::getChannelName, reqVO.getChannelName())
                .eqIfPresent(EdgeChannelDO::getExtra, reqVO.getExtra())
                .betweenIfPresent(EdgeChannelDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(EdgeChannelDO::getId));
    }

    default Long selectCountByChannelName(String channelName) {
        return selectCount(EdgeChannelDO::getChannelName, channelName);
    }

    @Select("select * from iot_edge_channel where channel_code = #{channelCode} and deleted = 0")
    EdgeChannelDO selectByCode(@Param("channelCode") String channelCode);

}