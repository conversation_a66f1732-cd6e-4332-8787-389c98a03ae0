package cn.powerchina.bjy.link.iot.controller.admin.modelTemplate.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

@Schema(description = "管理后台 - 物模板新增/修改 Request VO")
@Data
public class ModelTemplateSaveReqVO {

    @Schema(description = "主键", example = "3695")
    private Long id;

    @Schema(description = "物模板ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "物模板ID不能为空")
    private String categorizeId;

    @Schema(description = "物模板名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String templateName;

    @Schema(description = "描述", example = "")
    private String remark;
}
