package cn.powerchina.bjy.link.iot.dal.dataobject.messagestatisticday;

import cn.powerchina.bjy.cloud.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.util.Date;

/**
 * 设备消息数按日统计 DO
 *
 * <AUTHOR>
 */
@TableName("iot_message_statistic_day")
@KeySequence("iot_message_statistic_day_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MessageStatisticDayDO extends BaseDO {

    /**
     * 主键id
     */
    @TableId
    private Long id;

    /**
     * 统计类型，1：设备消息数，2：消息转发数
     */
    private Integer statisticType;
    /**
     * 统计日期
     */
    private Date statisticDay;
    /**
     * 统计数量
     */
    private Long statisticCount;

}
