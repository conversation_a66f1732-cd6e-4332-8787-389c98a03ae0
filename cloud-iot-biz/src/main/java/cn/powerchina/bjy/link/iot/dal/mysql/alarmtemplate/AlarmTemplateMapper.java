package cn.powerchina.bjy.link.iot.dal.mysql.alarmtemplate;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.iot.controller.admin.alarmtemplate.vo.AlarmTemplatePageReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.alarmtemplate.AlarmTemplateDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 告警模板 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface AlarmTemplateMapper extends BaseMapperX<AlarmTemplateDO> {

    default PageResult<AlarmTemplateDO> selectPage(AlarmTemplatePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AlarmTemplateDO>()
                .eqIfPresent(AlarmTemplateDO::getRuleId, reqVO.getRuleId())
                .likeIfPresent(AlarmTemplateDO::getAlarmName, reqVO.getAlarmName())
                .eqIfPresent(AlarmTemplateDO::getAlarmLevel, reqVO.getAlarmLevel())
                .eqIfPresent(AlarmTemplateDO::getAlarmContent, reqVO.getAlarmContent())
                .eqIfPresent(AlarmTemplateDO::getSort, reqVO.getSort())
                .betweenIfPresent(AlarmTemplateDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(AlarmTemplateDO::getId));
    }

}
