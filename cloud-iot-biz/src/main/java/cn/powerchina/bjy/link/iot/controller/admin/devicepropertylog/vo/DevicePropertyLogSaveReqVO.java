package cn.powerchina.bjy.link.iot.controller.admin.devicepropertylog.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

@Schema(description = "管理后台 - 设备属性日志新增/修改 Request VO")
@Data
public class DevicePropertyLogSaveReqVO {

    @Schema(description = "主键")
    private Long id;

    @Schema(description = "设备编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "设备编号不能为空")
    private String deviceCode;

    @Schema(description = "设备名称")
    private String deviceName;

    @Schema(description = "物模型标识符", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "物模型标识符不能为空")
    private String thingIdentity;

    @Schema(description = "物模型名称")
    private String thingName;

    @Schema(description = "物模型日志值", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "物模型日志值不能为空")
    private String thingValue;

    @Schema(description = "模式(1=影子模式，2=在线模式，3=其他)")
    private Integer deviceMode;

    @Schema(description = "备注")
    private String remark;

}