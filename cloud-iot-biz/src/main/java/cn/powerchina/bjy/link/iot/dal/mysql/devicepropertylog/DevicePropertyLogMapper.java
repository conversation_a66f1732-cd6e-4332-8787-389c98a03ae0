package cn.powerchina.bjy.link.iot.dal.mysql.devicepropertylog;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.iot.controller.admin.devicepropertylog.vo.DevicePropertyLogPageReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.devicepropertylog.DevicePropertyLogDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 设备属性日志 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface DevicePropertyLogMapper extends BaseMapperX<DevicePropertyLogDO> {

    default PageResult<DevicePropertyLogDO> selectPage(DevicePropertyLogPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<DevicePropertyLogDO>()
                .eqIfPresent(DevicePropertyLogDO::getDeviceCode, reqVO.getDeviceCode())
                .likeIfPresent(DevicePropertyLogDO::getDeviceName, reqVO.getDeviceName())
                .eqIfPresent(DevicePropertyLogDO::getThingIdentity, reqVO.getThingIdentity())
                .likeIfPresent(DevicePropertyLogDO::getThingName, reqVO.getThingName())
                .eqIfPresent(DevicePropertyLogDO::getThingValue, reqVO.getThingValue())
                .eqIfPresent(DevicePropertyLogDO::getDeviceMode, reqVO.getDeviceMode())
                .eqIfPresent(DevicePropertyLogDO::getRemark, reqVO.getRemark())
                .betweenIfPresent(DevicePropertyLogDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(DevicePropertyLogDO::getId));
    }

}