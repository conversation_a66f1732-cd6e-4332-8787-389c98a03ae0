package cn.powerchina.bjy.link.iot.dal.mysql.sceneruletimetrigger;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.iot.controller.admin.sceneruletimetrigger.vo.SceneRuleTimeTriggerPageReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.sceneruletimetrigger.SceneRuleTimeTriggerDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 定时触发 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface SceneRuleTimeTriggerMapper extends BaseMapperX<SceneRuleTimeTriggerDO> {

    default PageResult<SceneRuleTimeTriggerDO> selectPage(SceneRuleTimeTriggerPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<SceneRuleTimeTriggerDO>()
                .eqIfPresent(SceneRuleTimeTriggerDO::getRuleId, reqVO.getRuleId())
                .betweenIfPresent(SceneRuleTimeTriggerDO::getExecutionTime, reqVO.getExecutionTime())
                .eqIfPresent(SceneRuleTimeTriggerDO::getRepeatType, reqVO.getRepeatType())
                .betweenIfPresent(SceneRuleTimeTriggerDO::getRepeatStartDate, reqVO.getRepeatStartDate())
                .betweenIfPresent(SceneRuleTimeTriggerDO::getRepeatEndDate, reqVO.getRepeatEndDate())
                .eqIfPresent(SceneRuleTimeTriggerDO::getRepeatWeekDays, reqVO.getRepeatWeekDays())
                .eqIfPresent(SceneRuleTimeTriggerDO::getCronExpression, reqVO.getCronExpression())
                .eqIfPresent(SceneRuleTimeTriggerDO::getSort, reqVO.getSort())
                .betweenIfPresent(SceneRuleTimeTriggerDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(SceneRuleTimeTriggerDO::getId));
    }

}
