package cn.powerchina.bjy.link.iot.controller.admin.instructiondownlog.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(description = "管理后台 - 指令下发操作记录新增/修改 Request VO")
@Data
public class InstructionDownLogSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "12779")
    private Long id;

    @Schema(description = "设备编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "设备编号不能为空")
    private String deviceCode;

    @Schema(description = "物模型类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "物模型类型不能为空")
    private Integer thingType;

    @Schema(description = "物模型标识符", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "物模型标识符不能为空")
    private String thingIdentity;

    @Schema(description = "物模型名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "")
    @NotEmpty(message = "物模型名称不能为空")
    private String thingName;

    @Schema(description = "控制源（0-web；1-ios；2-android）", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "控制源（0-web；1-ios；2-android）不能为空")
    private Integer operatorSource;

    @Schema(description = "messageid", requiredMode = Schema.RequiredMode.REQUIRED, example = "7321")
    private String messageId;

    @Schema(description = "输入参数", requiredMode = Schema.RequiredMode.REQUIRED)
    private String inputParams;

    @Schema(description = "输出参数")
    private String outputParams;

    @Schema(description = "上行消耗时间(ms)")
    private Integer upConsumeTime;

    @Schema(description = "下行消耗时间(ms)")
    private Integer downConsumeTime;

}