package cn.powerchina.bjy.link.iot.service.alarmtemplate;

import cn.hutool.core.collection.CollectionUtil;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.iot.controller.admin.alarmtemplate.vo.AlarmMsgRespVO;
import cn.powerchina.bjy.link.iot.controller.admin.alarmtemplate.vo.AlarmTemplatePageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.alarmtemplate.vo.AlarmTemplateSaveReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.notificationmethod.vo.NotificationMethodSaveReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.notificationrecord.vo.NotificationRecordSaveReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.scenealarmrecord.vo.SceneAlarmRecordReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.scenealarmrecord.vo.SceneAlarmRecordSaveReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.scenealarmrecorddetail.vo.SceneAlarmRecordDetailSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.alarmtemplate.AlarmTemplateDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.notificationconfig.NotificationConfigDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.notificationmethod.NotificationMethodDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.productmodel.ProductModelDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.scenealarmrecord.SceneAlarmRecordDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.scenerule.SceneRuleDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.sceneruletrigger.SceneRuleTriggerDO;
import cn.powerchina.bjy.link.iot.dal.mysql.alarmtemplate.AlarmTemplateMapper;
import cn.powerchina.bjy.link.iot.dal.mysql.sceneruletrigger.SceneRuleTriggerMapper;
import cn.powerchina.bjy.link.iot.enums.AlarmLevelEnum;
import cn.powerchina.bjy.link.iot.enums.AlarmStatusEnum;
import cn.powerchina.bjy.link.iot.enums.DeviceTriggerTypeEnum;
import cn.powerchina.bjy.link.iot.enums.TriggerTypeEnum;
import cn.powerchina.bjy.link.iot.service.device.DeviceService;
import cn.powerchina.bjy.link.iot.service.notificationconfig.NotificationConfigService;
import cn.powerchina.bjy.link.iot.service.notificationmethod.NotificationMethodService;
import cn.powerchina.bjy.link.iot.service.notificationrecord.NotificationRecordService;
import cn.powerchina.bjy.link.iot.service.product.ProductService;
import cn.powerchina.bjy.link.iot.service.productmodel.ProductModelService;
import cn.powerchina.bjy.link.iot.service.scenealarmrecord.SceneAlarmRecordService;
import cn.powerchina.bjy.link.iot.service.scenealarmrecorddetail.SceneAlarmRecordDetailService;
import cn.powerchina.bjy.link.iot.service.scenerule.SceneRuleService;
import cn.powerchina.bjy.link.iot.thingmodel.IotDataSpecsDataTypeEnum;
import cn.powerchina.bjy.link.iot.util.DingTalkUtils;
import cn.powerchina.bjy.link.iot.util.EmailSendUtils;
import com.alibaba.nacos.common.utils.StringUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.link.iot.enums.ErrorCodeConstants.ALARM_TEMPLATE_NOT_EXISTS;

/**
 * 告警模板 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class AlarmTemplateServiceImpl implements AlarmTemplateService {

    @Resource
    private AlarmTemplateMapper alarmTemplateMapper;

    @Resource
    private SceneRuleTriggerMapper sceneRuleTriggerMapper;

    @Resource
    private NotificationMethodService notificationMethodService;

    @Autowired
    @Lazy
    private SceneRuleService sceneRuleService;

    @Resource
    private SceneAlarmRecordService sceneAlarmRecordService;
    @Resource
    private DeviceService deviceService;
    @Resource
    private ProductService productService;
    @Resource
    private SceneAlarmRecordDetailService sceneAlarmRecordDetailService;
    @Resource
    private ProductModelService productModelService;

    @Resource
    private NotificationConfigService notificationConfigService;

    @Resource
    private NotificationRecordService notificationRecordService;

    @Resource
    private RedissonClient redissonClient;

    private static final ObjectMapper objectMapper = new ObjectMapper();


    @Override
    public void createAndUpdateAlarmTemplate(AlarmTemplateSaveReqVO alarmTemplateSaveReqVO, Long ruleId, Long actionId) {
        alarmTemplateSaveReqVO.setRuleId(ruleId);
        alarmTemplateSaveReqVO.setActionId(actionId);

        Long alarmTemplateId = alarmTemplateSaveReqVO.getId();
        if (Objects.isNull(alarmTemplateSaveReqVO.getId())) {
            alarmTemplateId = createAlarmTemplate(alarmTemplateSaveReqVO);
        } else {
            updateAlarmTemplate(alarmTemplateSaveReqVO);
        }
        notificationMethodService.createAndUpdateNotificationMethod(alarmTemplateSaveReqVO.getNotificationMethodSaveReqVOList(), alarmTemplateId);
    }

    @Override
    public List<AlarmTemplateDO> getAlarmTemplateByRuleId(Long ruleId) {
        return alarmTemplateMapper.selectList(new LambdaQueryWrapperX<AlarmTemplateDO>().
                eq(AlarmTemplateDO::getRuleId, ruleId)
                .orderByAsc(AlarmTemplateDO::getSort));
    }

    @Override
    public AlarmTemplateDO getAlarmTemplateByActionId(Long actionId) {
        return alarmTemplateMapper.selectOne(new LambdaQueryWrapperX<AlarmTemplateDO>().
                eq(AlarmTemplateDO::getActionId, actionId));
    }

    @Override
    public AlarmTemplateSaveReqVO getTemplateAndNotificationByActionId(Long actionId) {
        AlarmTemplateDO alarmTemplateDO = getAlarmTemplateByActionId(actionId);
        AlarmTemplateSaveReqVO alarmTemplateSaveReqVO = BeanUtils.toBean(alarmTemplateDO, AlarmTemplateSaveReqVO.class);
        List<NotificationMethodDO> notificationMethodDOList = notificationMethodService.getNotificationMethodByAlarmTemplateId(alarmTemplateDO.getId());
        alarmTemplateSaveReqVO.setNotificationMethodSaveReqVOList(BeanUtils.toBean(notificationMethodDOList, NotificationMethodSaveReqVO.class));
        return alarmTemplateSaveReqVO;
    }

    @Override
    public void sendAlarm(Long actionId, String deviceCode) {
        AlarmTemplateDO alarmTemplateDO = getAlarmTemplateByActionId(actionId);
        if (null != alarmTemplateDO) {
            AlarmMsgRespVO alarmMsgRespVO = buildAlarmMsg(alarmTemplateDO, deviceCode);
            List<NotificationMethodDO> notificationMethodDOList = notificationMethodService.getNotificationMethodByAlarmTemplateId(alarmTemplateDO.getId());
            List<NotificationRecordSaveReqVO> allMsgRecordList = new ArrayList<>();
            //获取消息配置
            NotificationConfigDO notificationConfig = notificationConfigService.getNotificationConfigList().get(0);
            if (CollectionUtil.isNotEmpty(notificationMethodDOList)) {
                notificationMethodDOList.forEach(item -> {
                    switch (item.getNotificationMethod()) {
                        case 1:
                            DingTalkUtils.send(item, alarmMsgRespVO);
                            break;
                        case 2:
                            allMsgRecordList.addAll(EmailSendUtils.send(item, notificationConfig, alarmMsgRespVO));
                            break;
                        case 3:
                            //短信
                            break;
                        default:
                    }
                });
            }
            //生成告警记录
            createAlarmRecord(alarmTemplateDO, deviceCode, allMsgRecordList);
        }
    }

    @Override
    public List<AlarmTemplateSaveReqVO> getAlarmTemplateAndNotificationByRuleId(Long ruleId) {
        List<AlarmTemplateSaveReqVO> alarmTemplateSaveReqVOList = new ArrayList<>();
        List<AlarmTemplateDO> templateList = getAlarmTemplateByRuleId(ruleId);
        if (!CollectionUtils.isEmpty(templateList)) {
            templateList.forEach(item -> {
                AlarmTemplateSaveReqVO alarmTemplateSaveReqVO = BeanUtils.toBean(item, AlarmTemplateSaveReqVO.class);
                List<NotificationMethodDO> notificationMethodDOList = notificationMethodService.getNotificationMethodByAlarmTemplateId(item.getId());
                if (!CollectionUtils.isEmpty(notificationMethodDOList)) {
                    alarmTemplateSaveReqVO.setNotificationMethodSaveReqVOList(BeanUtils.toBean(notificationMethodDOList, NotificationMethodSaveReqVO.class));
                }
                alarmTemplateSaveReqVOList.add(alarmTemplateSaveReqVO);
            });
        }
        return alarmTemplateSaveReqVOList;
    }

    @Override
    public Long createAlarmTemplate(AlarmTemplateSaveReqVO createReqVO) {
        // 插入
        AlarmTemplateDO alarmTemplate = BeanUtils.toBean(createReqVO, AlarmTemplateDO.class);
        alarmTemplateMapper.insert(alarmTemplate);
        // 返回
        return alarmTemplate.getId();
    }

    @Override
    public void updateAlarmTemplate(AlarmTemplateSaveReqVO updateReqVO) {
        // 校验存在
        validateAlarmTemplateExists(updateReqVO.getId());
        // 更新
        AlarmTemplateDO updateObj = BeanUtils.toBean(updateReqVO, AlarmTemplateDO.class);
        alarmTemplateMapper.updateById(updateObj);
    }

    @Override
    public void deleteAlarmTemplate(Long id) {
        // 校验存在
        validateAlarmTemplateExists(id);
        // 删除
        alarmTemplateMapper.deleteById(id);
    }

    private AlarmTemplateDO validateAlarmTemplateExists(Long id) {
        AlarmTemplateDO alarmTemplateDO = alarmTemplateMapper.selectById(id);
        if (alarmTemplateDO == null) {
            throw exception(ALARM_TEMPLATE_NOT_EXISTS);
        }
        return alarmTemplateDO;
    }

    @Override
    public AlarmTemplateDO getAlarmTemplate(Long id) {
        return alarmTemplateMapper.selectById(id);
    }

    @Override
    public PageResult<AlarmTemplateDO> getAlarmTemplatePage(AlarmTemplatePageReqVO pageReqVO) {
        return alarmTemplateMapper.selectPage(pageReqVO);
    }

    @Override
    public void inviedAlarmTemplate(List<Long> ruleActionId) {
        for (Long item : ruleActionId) {
            AlarmTemplateDO alarmTemplateDO = getAlarmTemplateByActionId(item);
            alarmTemplateMapper.deleteById(alarmTemplateDO.getId());
            List<NotificationMethodDO> notificationMethodDOList = notificationMethodService.getNotificationMethodByAlarmTemplateId(alarmTemplateDO.getId());
            for (NotificationMethodDO notificationMethodDO : notificationMethodDOList) {
                notificationMethodService.deleteNotificationMethod(notificationMethodDO.getId());
            }
        }
    }

    private AlarmMsgRespVO buildAlarmMsg(AlarmTemplateDO alarmTemplateDO, String deviceCode) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        SceneRuleDO sceneRuleDO = sceneRuleService.getSceneRule(alarmTemplateDO.getRuleId());
        AlarmMsgRespVO alarmMsgRespVO = new AlarmMsgRespVO();
        alarmMsgRespVO.setTriggerTime(sdf.format(new Date()));//告警时间
        String alarmLeve = AlarmLevelEnum.getByCode(alarmTemplateDO.getAlarmLevel()).getDesc();
        alarmMsgRespVO.setAlarmLevel(alarmLeve);//告警级别
        alarmMsgRespVO.setRuleName(sceneRuleDO.getRuleName());//触发规则
        String context = alarmTemplateDO.getAlarmName() + "：" + alarmTemplateDO.getAlarmContent();
        alarmMsgRespVO.setAlarmName(alarmTemplateDO.getAlarmName());
        alarmMsgRespVO.setAlarmContent(context);//告警内容
        Optional.ofNullable(deviceCode)
                .filter(StringUtils::isNotBlank)
                .map(deviceService::getDeviceByCode)
                .ifPresent(device -> {
                    alarmMsgRespVO.setDeviceName(device.getDeviceName());
                    alarmMsgRespVO.setDeviceCode(device.getDeviceCode());

                    Optional.ofNullable(device.getProductCode())
                            .map(productService::getProductBOByCode)
                            .ifPresent(product ->
                                    alarmMsgRespVO.setProductName(product.getProductName())
                            );
                });
        return alarmMsgRespVO;
    }

    private void createAlarmRecord(AlarmTemplateDO alarmTemplateDO, String deviceCode, List<NotificationRecordSaveReqVO> allMsgRecordList) {

        String lockKey = "alarm:lock:" + alarmTemplateDO.getRuleId() + ":" + deviceCode;
        RLock lock = redissonClient.getFairLock(lockKey);
        log.info("添加告警记录新增的分布式锁： {}", lock);
        try {
            //lock.lock();
            if (lock.tryLock(5, 10, TimeUnit.SECONDS)) {
                SceneRuleDO sceneRuleDO = sceneRuleService.getSceneRule(alarmTemplateDO.getRuleId());

                //相同规则触发状态的告警，不再产生相同告警，待验证告警改为触发，告警次数+1
                SceneAlarmRecordReqVO sceneAlarmRecordReqVO = getSceneAlarmRecordReqVO(alarmTemplateDO, sceneRuleDO);
                List<SceneAlarmRecordDO> doList = sceneAlarmRecordService.getSceneAlarmRecordList(sceneAlarmRecordReqVO);
                Long alarmId;
                Long alarmDetailId;
                SceneAlarmRecordSaveReqVO reqVO = new SceneAlarmRecordSaveReqVO();
                if (CollectionUtil.isNotEmpty(doList)) {
                    SceneAlarmRecordDO sceneAlarmRecordDO = doList.get(0);

                    if (AlarmStatusEnum.CHECKING.getType().equals(sceneAlarmRecordDO.getAlarmStatus())) {
                        reqVO.setAlarmStatus(AlarmStatusEnum.TRIGGER.getType());
                    }
                    reqVO.setId(sceneAlarmRecordDO.getId());
                    reqVO.setRuleId(alarmTemplateDO.getRuleId());
                    reqVO.setLastAlarmTime(LocalDateTime.now());
                    AtomicInteger alarmNum = new AtomicInteger(sceneAlarmRecordDO.getAlarmNum());
                    reqVO.setAlarmNum(alarmNum.incrementAndGet());
                    sceneAlarmRecordService.updateSceneAlarmRecord(reqVO);
                    reqVO.setProductCode(sceneAlarmRecordDO.getProductCode());
                    reqVO.setDeviceCode(sceneAlarmRecordDO.getDeviceCode());
                    alarmId = sceneAlarmRecordDO.getId();
                    log.info("修改告警记录： {}", alarmId);
                } else {
                    reqVO.setResourceSpaceId(sceneRuleDO.getResourceSpaceId());
                    reqVO.setRuleId(alarmTemplateDO.getRuleId());
                    reqVO.setAlarmNum(1);
                    reqVO.setRuleName(sceneRuleDO.getRuleName());
                    reqVO.setAlarmTemplateId(alarmTemplateDO.getId());
                    reqVO.setAlarmName(alarmTemplateDO.getAlarmName());
                    reqVO.setAlarmContent(alarmTemplateDO.getAlarmContent());
                    reqVO.setAlarmLevel(alarmTemplateDO.getAlarmLevel());
                    reqVO.setAlarmStatus(AlarmStatusEnum.TRIGGER.getType());
                    reqVO.setTriggerTime(LocalDateTime.now());
                    reqVO.setLastAlarmTime(LocalDateTime.now());

                    Optional.ofNullable(deviceCode)
                            .filter(StringUtils::isNotBlank)
                            .map(deviceService::getDeviceByCode)
                            .ifPresent(device -> {
                                reqVO.setDeviceCode(deviceCode);
                                Optional.ofNullable(device.getProductCode())
                                        .map(productService::getProductBOByCode)
                                        .ifPresent(product ->
                                                reqVO.setProductCode(product.getProductCode())
                                        );
                            });
                    //生成告警记录
                    Long id = sceneAlarmRecordService.createSceneAlarmRecord(reqVO);
                    reqVO.setId(id);
                    alarmId = id;
                    log.info("添加告警记录： {}", alarmId);
                }
                //生成告警详情
                alarmDetailId = createAlarmRecordDetail(alarmTemplateDO.getRuleId(), reqVO);
                //生成消息记录
                createNotificationRecord(alarmId, alarmDetailId, allMsgRecordList);
            }else{
                log.warn("【告警并发控制】获取锁失败，规则：{}，设备：{}", alarmTemplateDO.getRuleId(), deviceCode);
            }
        } catch (Exception e) {
            Thread.currentThread().interrupt();
            log.error("获取分布式锁被中断", e);
        } finally {
            if (lock != null && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    private static @NotNull SceneAlarmRecordReqVO getSceneAlarmRecordReqVO(AlarmTemplateDO alarmTemplateDO, SceneRuleDO sceneRuleDO) {
        SceneAlarmRecordReqVO sceneAlarmRecordReqVO = new SceneAlarmRecordReqVO();
        sceneAlarmRecordReqVO.setRuleId(alarmTemplateDO.getRuleId());
        sceneAlarmRecordReqVO.setResourceSpaceId(sceneRuleDO.getResourceSpaceId());
        sceneAlarmRecordReqVO.setAlarmStatusList(Arrays.asList(
                AlarmStatusEnum.TRIGGER.getType(),
                AlarmStatusEnum.CHECKING.getType()
        ));
        return sceneAlarmRecordReqVO;
    }

    /**
     * 生成告警详情
     */
    public Long createAlarmRecordDetail(Long ruleId, SceneAlarmRecordSaveReqVO saveReqVO) {

        String productCode = null;
        String deviceCode = null;
        SceneAlarmRecordDetailSaveReqVO detailCreateReqVO = new SceneAlarmRecordDetailSaveReqVO();
        if (saveReqVO != null) {
            detailCreateReqVO.setAlarmId(saveReqVO.getId());
            detailCreateReqVO.setAlarmTime(saveReqVO.getLastAlarmTime());
            productCode = saveReqVO.getProductCode();
            deviceCode = saveReqVO.getDeviceCode();
        }
        detailCreateReqVO.setRuleId(ruleId);
        SceneRuleTriggerDO sceneRuleTriggerDO = null;
        try {
            //查询触发规则
            List<SceneRuleTriggerDO> sceneRuleTriggerList = sceneRuleTriggerMapper.selectList(new LambdaQueryWrapperX<SceneRuleTriggerDO>()
                    .eqIfPresent(SceneRuleTriggerDO::getRuleId, ruleId)
                    .eqIfPresent(SceneRuleTriggerDO::getProductCode, productCode)
                    .eqIfPresent(SceneRuleTriggerDO::getDeviceCode, deviceCode));
            if (CollectionUtil.isEmpty(sceneRuleTriggerList)) {
                List<SceneRuleTriggerDO> allSceneRuleTriggerList = sceneRuleTriggerMapper.selectList(new LambdaQueryWrapperX<SceneRuleTriggerDO>()
                        .eqIfPresent(SceneRuleTriggerDO::getRuleId, ruleId)
                        .eqIfPresent(SceneRuleTriggerDO::getProductCode, productCode)
                        .eqIfPresent(SceneRuleTriggerDO::getDeviceCode, "-1"));
                if (CollectionUtil.isNotEmpty(allSceneRuleTriggerList)) {
                    sceneRuleTriggerDO = allSceneRuleTriggerList.get(0);
                }
            } else {
                sceneRuleTriggerDO = sceneRuleTriggerList.get(0);
            }
        } catch (Exception e) {
            log.error("添加告警详情失败：{}", e.getMessage());
        }

        if (sceneRuleTriggerDO != null && TriggerTypeEnum.DEVICE_TRIGGER.getType().equals(sceneRuleTriggerDO.getTriggerType())) {
            String triggerCondition = null;
            String attributeValue = null;
            ProductModelDO productModelDO = productModelService.getProductModel(productCode, sceneRuleTriggerDO.getAttributeIdentity());
            if (DeviceTriggerTypeEnum.ATTRIBUTE.getType() == sceneRuleTriggerDO.getDeviceTriggerType()) {
                Map<String, String> map = new HashMap<>();
                if (IotDataSpecsDataTypeEnum.ENUM.getDataType().equals(productModelDO.getDatatype())) {
                    try {
                        Map enumMap = objectMapper.readValue(productModelDO.getInputParams(), Map.class);
                        List<Map<String, String>> enumItems = (List<Map<String, String>>) enumMap.get("enumItem");
                        for (Map<String, String> enumItem : enumItems) {
                            map.put(enumItem.get("key"), enumItem.get("value"));
                        }
                    } catch (Exception e) {
                        log.error("属性转换失败：{}", e.getMessage());
                    }
                    attributeValue = map.get(sceneRuleTriggerDO.getAttributeValue());
                }
                if (IotDataSpecsDataTypeEnum.BOOL.getDataType().equals(productModelDO.getDatatype())) {
                    String boolInputParams = productModelDO.getInputParams();
                    Gson gson = new Gson();
                    String[] result = gson.fromJson(boolInputParams, String[].class);
                    if (Boolean.parseBoolean(sceneRuleTriggerDO.getAttributeValue())) {
                        attributeValue = result[1];
                    } else {
                        attributeValue = result[0];
                    }
                }

                triggerCondition = productModelDO.getThingName() + "：" + attributeValue;
            }
            if (DeviceTriggerTypeEnum.EVENT.getType() == sceneRuleTriggerDO.getDeviceTriggerType()) {
                ProductModelDO eventProductModel = productModelService.getProductModel(productCode, sceneRuleTriggerDO.getEventIdentity());
                triggerCondition = eventProductModel.getThingName();
            }
            if (DeviceTriggerTypeEnum.ON_OR_OFF.getType() == sceneRuleTriggerDO.getDeviceTriggerType()) {
                triggerCondition = sceneRuleTriggerDO.getOnlineStatus() == 1 ? "设备由上线变为下线" : "设备由下线变为上线";
            }
            detailCreateReqVO.setDeviceName(deviceService.getDeviceByCode(deviceCode).getDeviceName());
            detailCreateReqVO.setTriggerCondition(triggerCondition);
        }
        if (sceneRuleTriggerDO != null && TriggerTypeEnum.TIME_TRIGGER.getType().equals(sceneRuleTriggerDO.getTriggerType())) {
            detailCreateReqVO.setTriggerCondition(TriggerTypeEnum.TIME_TRIGGER.getDesc());
        }
        return sceneAlarmRecordDetailService.createSceneAlarmRecordDetail(detailCreateReqVO);
    }

    public void createNotificationRecord(Long alarmId, Long alarmDetailId, List<NotificationRecordSaveReqVO> allMsgRecordList) {
        for (NotificationRecordSaveReqVO vo : allMsgRecordList) {
            vo.setAlarmId(alarmId);
            vo.setAlarmDetailId(alarmDetailId);
            notificationRecordService.createNotificationRecord(vo);
        }
    }

}
