package cn.powerchina.bjy.link.iot.controller.admin.scenerule.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 场景规则 Response VO")
@Data
@ExcelIgnoreUnannotated
public class SceneRuleRespVO {

    @Schema(description = "主键id",  example = "16863")
    @ExcelProperty("主键id")
    private Long id;

    @Schema(description = "规则名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    @ExcelProperty("规则名称")
    @NotNull(message = "规则名称不能为空")
    private String ruleName;

    @Schema(description = "所属资源空间ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "15362")
    @ExcelProperty("所属资源空间ID")
    @NotNull(message = "所属资源空间不能为空")
    private Long resourceSpaceId;

    @Schema(description = "状态:0-禁用,1-启用", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("状态:0-禁用,1-启用")
    private Integer status;

    @Schema(description = "是否抑制:0-禁用抑制,1-启用抑制", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("是否抑制:0-禁用抑制,1-启用抑制")
    private Integer inhibition;

    @Schema(description = "生效时段类型:1-全天,2-自定义", example = "2")
    @ExcelProperty("生效时段类型:1-全天,2-自定义")
    @NotNull(message = "生效时段不能为空")
    private Integer effectiveType;

    @Schema(description = "生效开始时间")
    @ExcelProperty("生效开始时间")
    private String effectiveStartTime;

    @Schema(description = "生效结束时间")
    @ExcelProperty("生效结束时间")
    private String effectiveEndTime;

    @Schema(description = "重复类型:1-每天,2-指定日期,3-指定周期,4-自定义", example = "2")
    @ExcelProperty("重复类型:1-每天,2-指定日期,3-指定周期,4-自定义")
    @NotNull(message = "重复类型不能为空")
    private Integer repeatType;

    @Schema(description = "开始日期/指定日期")
    @ExcelProperty("开始日期/指定日期")
    private LocalDate repeatStartDate;

    @Schema(description = "结束日期")
    @ExcelProperty("结束日期")
    private LocalDate repeatEndDate;

    @Schema(description = "每周重复的星期几,如:1,2,3,4,5,6,7")
    @ExcelProperty("每周重复的星期几,如:1,2,3,4,5,6,7")
    private String repeatWeekDays;

    @Schema(description = "规则表达式")
    @ExcelProperty("规则表达式")
    private String ruleExpression;

    @Schema(description = "规则优先级")
    @ExcelProperty("规则优先级")
    private Integer rulePriority;

    @Schema(description = "规则描述")
    @ExcelProperty("规则描述")
    private String ruleDesc;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;
}