package cn.powerchina.bjy.link.iot.controller.admin.deviceeventlog.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(description = "管理后台 - 设备事件日志新增/修改 Request VO")
@Data
public class DeviceEventLogSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "25993")
    private Long id;

    @Schema(description = "设备编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "设备编号不能为空")
    private String deviceCode;

    @Schema(description = "设备名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "")
    @NotEmpty(message = "设备名称不能为空")
    private String deviceName;

    @Schema(description = "物模型标识符", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "物模型标识符不能为空")
    private String thingIdentity;

    @Schema(description = "物模型名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "")
    @NotEmpty(message = "物模型名称不能为空")
    private String thingName;

    @Schema(description = "类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "类型不能为空")
    private Integer eventType;

    @Schema(description = "物模型日志值", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "物模型日志值不能为空")
    private String thingValue;

    @Schema(description = "模式(1=影子模式，2=在线模式，3=其他)", requiredMode = Schema.RequiredMode.REQUIRED)
    //@NotNull(message = "模式(1=影子模式，2=在线模式，3=其他)不能为空")
    private Integer deviceMode;

    @Schema(description = "备注", example = "随便")
    private String remark;

}