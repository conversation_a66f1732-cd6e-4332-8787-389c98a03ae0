package cn.powerchina.bjy.link.iot.controller.admin.messagestatisticday.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 设备消息数按日统计 Response VO")
@Data
@ExcelIgnoreUnannotated
public class MessageStatisticDayRespVO {

    @Schema(description = "主键id")
    @ExcelProperty("主键id")
    private Long id;

    @Schema(description = "统计日期")
    @ExcelProperty("统计日期")
    private LocalDate statisticDay;

    @Schema(description = "统计数量")
    @ExcelProperty("统计数量")
    private Long statisticCount;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
