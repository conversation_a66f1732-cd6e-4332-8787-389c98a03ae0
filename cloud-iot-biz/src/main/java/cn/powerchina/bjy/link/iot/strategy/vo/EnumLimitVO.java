package cn.powerchina.bjy.link.iot.strategy.vo;

import lombok.Data;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 枚举类型 VO
 *
 * <AUTHOR>
 **/
@Data
public class EnumLimitVO {
    private List<EnumItem> enumItem;

    public Set<String> keySet() {
        if (null == enumItem || enumItem.isEmpty()) {
            return new HashSet<>(0);
        }
        return enumItem.stream().map(EnumItem::getKey).collect(Collectors.toSet());
    };
}

@Data
class EnumItem {
    /**
     * 枚举参数值
     */
    private String key;
    /**
     * 描述
     */
    private String value;
}

