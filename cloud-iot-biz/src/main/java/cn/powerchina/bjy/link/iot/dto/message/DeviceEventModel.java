package cn.powerchina.bjy.link.iot.dto.message;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 设备事件
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DeviceEventModel implements Serializable {

    private String thingIdentity;
    /**
     * 设备编码
     */
    private String deviceCode;
    /**
     * 产品编码
     */
    private String productCode;

    private String eventType;
    /**
     * 采集时刻时间
     */
    private Long currentTime;

    private Long reportTime;

    private Object params;

    private String remark;


}
