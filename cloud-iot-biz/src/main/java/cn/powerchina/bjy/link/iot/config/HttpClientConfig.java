package cn.powerchina.bjy.link.iot.config;

import org.apache.hc.client5.http.config.RequestConfig;
import org.apache.hc.client5.http.cookie.BasicCookieStore;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.HttpClients;
import org.apache.hc.core5.util.Timeout;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class HttpClientConfig {

    // 超时时间配置（单位：毫秒）
    private static final int CONNECT_TIMEOUT = 30000;   // 连接超时：30秒
    private static final int RESPONSE_TIMEOUT = 30000;  // 响应超时：30秒

    @Bean
    public CloseableHttpClient closeableHttpClient(BasicCookieStore cookieStore) {
        // 创建超时配置
        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectTimeout(Timeout.ofMilliseconds(CONNECT_TIMEOUT))  // 连接超时
                .setResponseTimeout(Timeout.ofMilliseconds(RESPONSE_TIMEOUT)) // 响应超时
                .build();

        return HttpClients.custom()
                .setDefaultRequestConfig(requestConfig) // 应用超时配置
                .setDefaultCookieStore(cookieStore)     // 设置Cookie存储
                .build();
    }

    @Bean
    public BasicCookieStore basicCookieStore() {
        return new BasicCookieStore();
    }
}