package cn.powerchina.bjy.link.iot.controller.admin.edgegateway.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 边缘网关一键注册 Request VO
 *
 * <AUTHOR>
 **/
@Schema(description = "管理后台 - 边缘网关一键注册 Request VO")
@Data
public class EdgeGatewayRegisterReqVO {

    @Schema(description = "主键")
    private Long id;

    @Schema(description = "类型，全局下发或单条下发，1-全局；0-单条", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "需指明下发范围，全局或单条")
    private Integer scope;

    @Schema(description = "网关实例编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "边缘网关编码不能为空")
    private String edgeCode;

    @Schema(description = "设备编码，单条下发时必填")
    private String deviceCode;

    @Schema(description = "边缘网关服务名", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "边缘网关服务名不能为空")
    private String edgeServiceName;
}
