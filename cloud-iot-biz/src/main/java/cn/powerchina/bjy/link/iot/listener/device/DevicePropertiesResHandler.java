package cn.powerchina.bjy.link.iot.listener.device;

import cn.hutool.core.collection.CollectionUtil;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.link.iot.controller.admin.device.vo.DeviceSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.device.DeviceDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.devicecurrentattribute.DeviceShadowDO;
import cn.powerchina.bjy.link.iot.dal.tdengine.IotDevicePropertyMapper;
import cn.powerchina.bjy.link.iot.dto.message.DevicePropertiesReportModel;
import cn.powerchina.bjy.link.iot.dto.message.DevicePropertiesResponseModel;
import cn.powerchina.bjy.link.iot.enums.IotTopicConstant;
import cn.powerchina.bjy.link.iot.enums.LinkStateEnum;
import cn.powerchina.bjy.link.iot.enums.ShadowTypeEnum;
import cn.powerchina.bjy.link.iot.enums.TransportSourceTypeEnum;
import cn.powerchina.bjy.link.iot.framework.rule.RuleActionService;
import cn.powerchina.bjy.link.iot.model.DeviceDataTransportModel;
import cn.powerchina.bjy.link.iot.model.ServiceProperty;
import cn.powerchina.bjy.link.iot.mq.RocketMQv5Client;
import cn.powerchina.bjy.link.iot.service.device.DeviceService;
import cn.powerchina.bjy.link.iot.service.devicecurrentattribute.DeviceShadowService;
import cn.powerchina.bjy.link.iot.service.transportsource.TransportSourceService;
import com.alibaba.fastjson.JSON;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.jeasy.rules.api.Facts;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description: 监听设备上报的消息
 * @Author: zhaoqiang
 * @CreateDate: 2024/10/16
 */
@Slf4j
@Component
public class DevicePropertiesResHandler {


    @Resource
    private RuleActionService ruleActionService;

    @Resource
    private IotDevicePropertyMapper devicePropertyMapper;

    @Resource
    private DeviceShadowService deviceShadowService;

    @Resource
    private TransportSourceService transportSourceService;

    @Resource
    private DeviceService deviceService;

    @Resource
    private RocketMQv5Client rocketMQv5Client;


    public void handler(DevicePropertiesResponseModel devicePropertiesResponseModel, DeviceDO device) {

        DevicePropertiesReportModel messageModel = JSON.parseObject(JSON.toJSONString(devicePropertiesResponseModel), DevicePropertiesReportModel.class);

        //变化速率：上一次最新的值
        Map<String, Object> lastValueMap = new HashMap<>();
        List<ServiceProperty> services = messageModel.getServices();
        Map<String, Object> properties = new HashMap<>();
        Long reportTime = System.currentTimeMillis();
        for (ServiceProperty serviceProperty : services) {
            if ("default".equals(serviceProperty.getServiceId())) {
                properties = serviceProperty.getProperties();
                reportTime = serviceProperty.getReportTime();
            }
        }
        insertReportData(messageModel, properties, reportTime, lastValueMap);
        try {
            Facts facts = new Facts();
            facts.put("productCode", messageModel.getProductCode());
            facts.put("deviceCode", messageModel.getDeviceCode());
            if (CollectionUtil.isNotEmpty(properties)) {
                properties.forEach(facts::put);
            }
            if (CollectionUtil.isNotEmpty(lastValueMap)) {
                lastValueMap.forEach(facts::put);
            }
            ruleActionService.matchRuleAndAction(facts);
        } catch (Exception e) {
            log.error("根据上报的属性，匹配规则异常：{}", e.getMessage());
        }
        //如果接收到消息，说明设备上线。变更设备状态
        if (LinkStateEnum.OFF_LINE.getType().equals(device.getLinkState())) {
            device.setLinkState(LinkStateEnum.ON_LINE.getType());
            device.setLastUpTime(LocalDateTime.now());
            deviceService.updateDevice(BeanUtils.toBean(device, DeviceSaveReqVO.class));
        }
        // 设备属性上报
        messageModel.setThingIdentity("attribute");
        transportSourceService.dataForwarding(TransportSourceTypeEnum.DEVICE_ATTRIBUTE_REPORT, messageModel);


        List<DeviceDataTransportModel.DeviceData> deviceDataList = new ArrayList<>();

        devicePropertiesResponseModel.getServices().forEach(item -> {
            Map<String, Object> propertyMap = item.getProperties();
            propertyMap.forEach((key, value) -> {
                DeviceDataTransportModel.DeviceData deviceData = new DeviceDataTransportModel.DeviceData();
                deviceData.setThingIdentity(key);
                deviceData.setThingValue((String) value);
                deviceDataList.add(deviceData);
            });
        });

        DeviceDataTransportModel transportModel = new DeviceDataTransportModel();
        transportModel.setDeviceCode(device.getDeviceCode());
        transportModel.setMcuChannel(device.getMcuChannel());
        transportModel.setCurrentTime(devicePropertiesResponseModel.getServices().get(0).getReportTime());
        transportModel.setDeviceDataList(deviceDataList);

        rocketMQv5Client.syncSendFifoMessage(IotTopicConstant.TOPIC_DEVICE_COLLECT_DATA, transportModel, IotTopicConstant.GROUP_DEVICE_COLLECT_DATA);
    }

    /**
     * 时序数据库保存属性
     * mysql更新最新的属性值（影子）
     *
     * @param messageModel
     * @param lastValueMap
     */
    private void insertReportData(DevicePropertiesReportModel messageModel, Map<String, Object> properties, Long currentTime, Map<String, Object> lastValueMap) {
        try {
            devicePropertyMapper.insert(messageModel.getProductCode(), messageModel.getDeviceCode(), properties, currentTime);
        } catch (Exception e) {
            log.error("时序数据库持久化属性异常 {}", e.getMessage());
            // throw exception(PRODUCT_MODEL_TD_INSERT_DATA);
        }
        try {
            List<DeviceShadowDO> currentDBList = deviceShadowService.getShadowListByDeviceCode(messageModel.getDeviceCode());
            Map<String, DeviceShadowDO> identityMap = new HashMap<>();
            if (CollectionUtil.isNotEmpty(currentDBList)) {
                identityMap = currentDBList.stream().collect(Collectors.toMap(DeviceShadowDO::getThingIdentity, Function.identity(), (oldValue, newValue) -> newValue));
            }
            List<DeviceShadowDO> newAttributeList = new ArrayList<>();
            List<DeviceShadowDO> updateAttributeList = new ArrayList<>();
            Map<String, DeviceShadowDO> finalIdentityMap = identityMap;
            LocalDateTime reportTime = Instant.ofEpochMilli(currentTime)
                    .atZone(ZoneId.systemDefault())
                    .toLocalDateTime();
            properties.forEach((key, value) -> {
                if (value != null) {
                    if (finalIdentityMap.containsKey(key)) {
                        DeviceShadowDO currentAttributeDO = finalIdentityMap.get(key);
                        lastValueMap.put("last_" + key, currentAttributeDO.getThingValue());
                        currentAttributeDO.setReportTime(reportTime);
                        currentAttributeDO.setThingIdentity(key);
                        currentAttributeDO.setThingValue(String.valueOf(value));
                        currentAttributeDO.setUpdateTime(LocalDateTime.now());
                        updateAttributeList.add(currentAttributeDO);
                    } else {
                        DeviceShadowDO currentAttributeDO = new DeviceShadowDO();
                        currentAttributeDO.setDeviceCode(messageModel.getDeviceCode());
                        currentAttributeDO.setProductCode(messageModel.getProductCode());
                        currentAttributeDO.setReportTime(reportTime);
                        currentAttributeDO.setThingIdentity(key);
                        currentAttributeDO.setThingValue(String.valueOf(value));
                        currentAttributeDO.setShadowType(ShadowTypeEnum.ATTRIBUTE.getType());
                        newAttributeList.add(currentAttributeDO);
                    }
                }
            });
            deviceShadowService.createShadow(newAttributeList);
            deviceShadowService.updateShadow(updateAttributeList);
        } catch (Exception e) {
            log.error("持久化最新属性消息 {} 异常 {}", messageModel.getId(), e.getMessage());
            //throw exception(DEVICE_SHADOW_UPDATE);
        }
    }

}
