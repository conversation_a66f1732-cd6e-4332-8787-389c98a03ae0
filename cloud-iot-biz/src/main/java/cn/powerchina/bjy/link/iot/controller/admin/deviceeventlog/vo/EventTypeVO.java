package cn.powerchina.bjy.link.iot.controller.admin.deviceeventlog.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class EventTypeVO {
    @Schema(description = "事件类型值", example = "1")
    private Integer eventType;
    @Schema(description = "事件类型名称", example = "信息")
    private String eventTypeName;
}