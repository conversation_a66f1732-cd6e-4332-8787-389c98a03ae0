package cn.powerchina.bjy.link.iot.controller.admin.edgegateway.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

@Data
public class ConvertVideoReqVO {
    @Schema(description = "设备ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "camera_001")
    private String deviceId;

    @Schema(description = "设备唯一标识", requiredMode = Schema.RequiredMode.REQUIRED, example = "camera_001")
    private String deviceSerial;
}
