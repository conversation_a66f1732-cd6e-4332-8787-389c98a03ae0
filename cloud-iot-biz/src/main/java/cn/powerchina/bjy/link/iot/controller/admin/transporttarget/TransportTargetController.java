package cn.powerchina.bjy.link.iot.controller.admin.transporttarget;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.link.iot.controller.admin.transporttarget.vo.TransportTargetPageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.transporttarget.vo.TransportTargetRespVO;
import cn.powerchina.bjy.link.iot.controller.admin.transporttarget.vo.TransportTargetSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.transporttarget.TransportTargetDO;
import cn.powerchina.bjy.link.iot.service.transporttarget.TransportTargetService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 转发规则-转发目标")
@RestController
@RequestMapping("/iot/transport-target")
@Validated
public class TransportTargetController {

    @Resource
    private TransportTargetService transportTargetService;

    @PostMapping("/create")
    @Operation(summary = "创建转发规则-转发目标")
    @PreAuthorize("@ss.hasPermission('iot:transport-target:create')")
    public CommonResult<Long> createTransportTarget(@Valid @RequestBody TransportTargetSaveReqVO createReqVO) {
        return success(transportTargetService.createTransportTarget(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新转发规则-转发目标")
    @PreAuthorize("@ss.hasPermission('iot:transport-target:update')")
    public CommonResult<Boolean> updateTransportTarget(@Valid @RequestBody TransportTargetSaveReqVO updateReqVO) {
        transportTargetService.updateTransportTarget(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除转发规则-转发目标")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('iot:transport-target:delete')")
    public CommonResult<Boolean> deleteTransportTarget(@RequestParam("id") Long id) {
        transportTargetService.deleteTransportTarget(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得转发规则-转发目标")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('iot:transport-target:query')")
    public CommonResult<TransportTargetRespVO> getTransportTarget(@RequestParam("id") Long id) {
        TransportTargetDO transportTarget = transportTargetService.getTransportTarget(id);
        return success(BeanUtils.toBean(transportTarget, TransportTargetRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得转发规则-转发目标分页")
    @PreAuthorize("@ss.hasPermission('iot:transport-target:query')")
    public CommonResult<PageResult<TransportTargetRespVO>> getTransportTargetPage(@Valid TransportTargetPageReqVO pageReqVO) {
        PageResult<TransportTargetRespVO> pageResult = transportTargetService.getTransportTargetPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, TransportTargetRespVO.class));
    }

}