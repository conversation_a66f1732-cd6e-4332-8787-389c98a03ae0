package cn.powerchina.bjy.link.iot.dal.mysql.edgegateway;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.MPJLambdaWrapperX;
import cn.powerchina.bjy.link.iot.controller.admin.edgegateway.vo.EdgeGatewayPageReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.device.DeviceDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.edgegateway.EdgeGatewayDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.edgegateway.EdgeGatewayDeviceDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;
import org.springframework.util.CollectionUtils;

import java.util.Date;

/**
 * 边缘网关 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface EdgeGatewayMapper extends BaseMapperX<EdgeGatewayDO> {

    default PageResult<EdgeGatewayDO> selectPage(EdgeGatewayPageReqVO reqVO) {
        PageResult<EdgeGatewayDO> result = null;
        if (CollectionUtils.isEmpty(reqVO.getCodes())) {
            result = selectPage(reqVO, new LambdaQueryWrapperX<EdgeGatewayDO>()
                    .eqIfPresent(EdgeGatewayDO::getEdgeCode, reqVO.getEdgeCode())
                    .eqIfPresent(EdgeGatewayDO::getResourceSpaceId, reqVO.getResourceSpaceId())
                    .likeIfPresent(EdgeGatewayDO::getEdgeName, reqVO.getEdgeName())
                    .likeIfPresent(EdgeGatewayDO::getEdgeServiceName, reqVO.getEdgeServiceName())
                    .eqIfPresent(EdgeGatewayDO::getEdgeHost, reqVO.getEdgeHost())
                    .eqIfPresent(EdgeGatewayDO::getEdgePort, reqVO.getEdgePort())
                    .eqIfPresent(EdgeGatewayDO::getDescription, reqVO.getDescription())
                    .betweenIfPresent(EdgeGatewayDO::getCreateTime, reqVO.getCreateTime())
                    .orderByDesc(EdgeGatewayDO::getId));
        } else {
            result = selectPage(reqVO, new LambdaQueryWrapperX<EdgeGatewayDO>()
                    .eqIfPresent(EdgeGatewayDO::getEdgeCode, reqVO.getEdgeCode())
                    .eqIfPresent(EdgeGatewayDO::getResourceSpaceId, reqVO.getResourceSpaceId())
                    .likeIfPresent(EdgeGatewayDO::getEdgeName, reqVO.getEdgeName())
                    .in(EdgeGatewayDO::getId, reqVO.getCodes())
                    .likeIfPresent(EdgeGatewayDO::getEdgeServiceName, reqVO.getEdgeServiceName())
                    .eqIfPresent(EdgeGatewayDO::getEdgeHost, reqVO.getEdgeHost())
                    .eqIfPresent(EdgeGatewayDO::getEdgePort, reqVO.getEdgePort())
                    .eqIfPresent(EdgeGatewayDO::getDescription, reqVO.getDescription())
                    .betweenIfPresent(EdgeGatewayDO::getCreateTime, reqVO.getCreateTime())
                    .orderByDesc(EdgeGatewayDO::getId));
        }
        return result;
    }

    default PageResult<EdgeGatewayDeviceDO> selectJoinPage(EdgeGatewayPageReqVO reqVO) {
        MPJLambdaWrapperX<EdgeGatewayDO> wrapper = (MPJLambdaWrapperX<EdgeGatewayDO>) new MPJLambdaWrapperX<EdgeGatewayDO>()
                .selectAll(EdgeGatewayDO.class)
                .selectAs(DeviceDO::getDeviceName, EdgeGatewayDeviceDO::getDeviceName)
                .eqIfPresent(EdgeGatewayDO::getEdgeCode, reqVO.getEdgeCode())
                .eqIfPresent(EdgeGatewayDO::getResourceSpaceId, reqVO.getResourceSpaceId())
                .likeIfExists(DeviceDO::getDeviceName, reqVO.getDeviceName())
                .leftJoin(DeviceDO.class, "d", on -> on.eq(DeviceDO::getEdgeCode, EdgeGatewayDO::getEdgeCode));
        return selectJoinPage(reqVO, EdgeGatewayDeviceDO.class, wrapper);
    }

    default EdgeGatewayDO selectByEdgeCode(String edgeCode) {
        return selectOne(EdgeGatewayDO::getEdgeCode, edgeCode);
    }

    /**
     * 修改网关实例在线状态
     *
     * @param edgeCode
     * @param edgeStatus
     * @param changeTime
     * @return
     */
    @Update("update iot_edge_gateway set edge_status = #{edgeStatus} where edge_code = #{edgeCode} and online_time < #{changeTime}")
    Integer updateEdgeGateWayEdgeStatus(@Param("edgeCode") String edgeCode, @Param("edgeStatus") Integer edgeStatus, @Param("changeTime") Date changeTime);
}