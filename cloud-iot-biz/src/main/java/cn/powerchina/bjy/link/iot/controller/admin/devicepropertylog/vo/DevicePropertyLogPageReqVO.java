package cn.powerchina.bjy.link.iot.controller.admin.devicepropertylog.vo;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 设备属性日志分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DevicePropertyLogPageReqVO extends PageParam {

    @Schema(description = "设备编号")
    private String deviceCode;

    @Schema(description = "设备名称",example = "")
    private String deviceName;

    @Schema(description = "物模型标识符")
    private String thingIdentity;

    @Schema(description = "物模型名称",example = "")
    private String thingName;

    @Schema(description = "物模型日志值")
    private String thingValue;

    @Schema(description = "模式(1=影子模式，2=在线模式，3=其他)")
    private Integer deviceMode;

    @Schema(description = "备注", example = "你说的对")
    private String remark;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}