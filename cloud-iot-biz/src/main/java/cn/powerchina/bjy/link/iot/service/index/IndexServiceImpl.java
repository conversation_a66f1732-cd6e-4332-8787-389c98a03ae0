package cn.powerchina.bjy.link.iot.service.index;

import cn.powerchina.bjy.link.iot.controller.admin.device.vo.DeviceCountReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.index.bo.IndexTotalBO;
import cn.powerchina.bjy.link.iot.controller.admin.index.bo.StatisticDayBO;
import cn.powerchina.bjy.link.iot.controller.admin.index.bo.StatisticDayIndexBO;
import cn.powerchina.bjy.link.iot.controller.admin.index.bo.StatisticHourBO;
import cn.powerchina.bjy.link.iot.controller.admin.index.vo.StatisticDayImageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.product.vo.ProductCountReqVO;
import cn.powerchina.bjy.link.iot.enums.*;
import cn.powerchina.bjy.link.iot.service.device.DeviceService;
import cn.powerchina.bjy.link.iot.service.messagestatisticday.MessageStatisticDayService;
import cn.powerchina.bjy.link.iot.service.product.ProductService;
import cn.powerchina.bjy.link.iot.util.MyCalculateUtils;
import cn.powerchina.bjy.link.iot.util.MyDateUtils;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;

/**
 * @Description: 描述
 * @Author: yhx
 * @CreateDate: 2024/8/19
 */
@Service
public class IndexServiceImpl implements IndexService {

    @Autowired
    private ProductService productService;

    @Autowired
    private DeviceService deviceService;

    @Autowired
    private MessageStatisticDayService messageStatisticDayService;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Override
    public IndexTotalBO findIndexTotal() {
        IndexTotalBO totalBO = new IndexTotalBO();
        Date currentDate = MyDateUtils.getParseDate(MyDateUtils.getFormatDate(new Date(), "yyyy-MM-dd"), "yyyy-MM-dd");
        totalBO.setProductTotal(productService.countProduct(ProductCountReqVO.builder().build()));
        totalBO.setProductToday(productService.countProduct(ProductCountReqVO.builder().startDate(currentDate).build()));
        totalBO.setDeviceTotal(deviceService.countDevice(DeviceCountReqVO.builder().build()));
        totalBO.setDeviceOnlineTotal(deviceService.countDevice(DeviceCountReqVO.builder().linkState(LinkStateEnum.ON_LINE.getType()).build()));
        totalBO.setDeviceOfflineTotal(deviceService.countDevice(DeviceCountReqVO.builder().linkState(LinkStateEnum.OFF_LINE.getType()).build()));
        totalBO.setDeviceToday(deviceService.countDevice(DeviceCountReqVO.builder().startDate(currentDate).build()));
        totalBO.setDeviceEdgeTotal(deviceService.countDevice(DeviceCountReqVO.builder().nodeType(NodeTypeEnum.EDGE.getType()).build()));
        totalBO.setDeviceSubTotal(deviceService.countDevice(DeviceCountReqVO.builder().nodeType(NodeTypeEnum.EDGE_SUB.getType()).build()));
        totalBO.setDeviceDirectTotal(totalBO.getDeviceTotal() - totalBO.getDeviceEdgeTotal() - totalBO.getDeviceSubTotal());
        totalBO.setPercentDeviceEdge(MyCalculateUtils.calculate(totalBO.getDeviceEdgeTotal(), totalBO.getDeviceTotal(), 2));
        totalBO.setPercentDeviceSub(MyCalculateUtils.calculate(totalBO.getDeviceSubTotal(), totalBO.getDeviceTotal(), 2));
        totalBO.setPercentDeviceDirect(MyCalculateUtils.calculate(totalBO.getDeviceDirectTotal(), totalBO.getDeviceTotal(), 2));
        totalBO.setMessageTotal(messageStatisticDayService.countMessage(null, StatisticImageTypeEnum.MESSAGE.getType()));
        totalBO.setMessageToday(messageStatisticDayService.countMessage(MyDateUtils.getParseDate(MyDateUtils.getFormatDate(new Date(), "yyyy-MM-dd"), "yyyy-MM-dd"), StatisticImageTypeEnum.MESSAGE.getType()));
        totalBO.setMessageTransportTotal(messageStatisticDayService.countMessage(null, StatisticImageTypeEnum.TRANSPORT.getType()));
        totalBO.setMessageTransportToday(messageStatisticDayService.countMessage(MyDateUtils.getParseDate(MyDateUtils.getFormatDate(new Date(), "yyyy-MM-dd"), "yyyy-MM-dd"), StatisticImageTypeEnum.TRANSPORT.getType()));
        return totalBO;
    }

    @Override
    public StatisticDayIndexBO getStatisticDayList(StatisticDayImageReqVO reqVO) {
        StatisticDayIndexBO dayBO = new StatisticDayIndexBO();
        Date startTime = null, endTime = null, today = MyDateUtils.getParseDate(MyDateUtils.getFormatDate(new Date(), "yyyy-MM-dd"), "yyyy-MM-dd");
        //统计类型不为空
        if (Objects.nonNull(reqVO.getStatisticType())) {
            //统计类型为近7日或30日
            if (Objects.equals(reqVO.getStatisticType(), StatisticTypeEnum.SEVEN.getType()) || Objects.equals(reqVO.getStatisticType(), StatisticTypeEnum.THIRTY.getType())) {
                startTime = DateUtils.addDays(today, 1 - reqVO.getStatisticType());
                endTime = today;
            } else {
                //统计类型为当日，查询缓存
                String key = String.format(IotRedisConstant.STATISTIC_MESSAGE_DAY_KEY, reqVO.getStatisticImageType(), MyDateUtils.getFormatDate(new Date(), "yyyyMMdd"));
                Map<Object, Object> statisticMap = redisTemplate.opsForHash().entries(key);
                List<StatisticHourBO> boList = new ArrayList<>();
                if (!CollectionUtils.isEmpty(statisticMap)) {
                    boList.addAll(statisticMap.entrySet().stream().map(item -> StatisticHourBO.builder()
                            .statisticTime(Integer.valueOf(item.getKey() + "")).statisticCount(Long.valueOf(item.getValue() + "")).build()).toList());
                }
                List<Integer> hourList = statisticMap.keySet().stream().map(item -> Integer.valueOf(item + "")).toList();
                for (int i = 0; i <= LocalDateTime.now().getHour(); i++) {
                    if (!hourList.contains(Integer.valueOf(i + ""))) {
                        boList.add(StatisticHourBO.builder().statisticTime(i).statisticCount(0L).build());
                    }
                }
                boList = boList.stream().sorted(Comparator.comparing(StatisticHourBO::getStatisticTime)).collect(Collectors.toList());
                dayBO.setStatisticTime(boList.stream().map(item -> item.getStatisticTime() + " :00").collect(Collectors.toList()));
                dayBO.setStatisticCount(boList.stream().map(StatisticHourBO::getStatisticCount).collect(Collectors.toList()));
                return dayBO;
            }
            //自定义区间查询
        } else if (Objects.nonNull(reqVO.getStatisticTime()) && reqVO.getStatisticTime().length > 0) {//自定义日期不为空
            startTime = MyDateUtils.transportLocalDateTimeToDate(reqVO.getStatisticTime()[0], "yyyy-MM-dd");
            endTime = MyDateUtils.transportLocalDateTimeToDate(reqVO.getStatisticTime()[1], "yyyy-MM-dd");
            if (DateUtils.addDays(startTime, IotConstant.STATISTIC_MESSAGE_INTERVAL_DAYS).compareTo(endTime) < 0) {
                throw exception(ErrorCodeConstants.MESSAGE_STATISTIC_DAY_INTERVAL_ERROR, IotConstant.STATISTIC_MESSAGE_INTERVAL_DAYS,
                        IotConstant.STATISTIC_MESSAGE_INTERVAL_DAYS);
            }
        }
        if (Objects.nonNull(startTime)) {
            List<StatisticDayBO> dayBOList = messageStatisticDayService.selectListByStatisticDay(startTime, endTime, reqVO.getStatisticImageType());
            if (!CollectionUtils.isEmpty(dayBOList)) {
                dayBO.setStatisticTime(dayBOList.stream().map(item -> MyDateUtils.getFormatDate(item.getStatisticDay(), "MM-dd")).collect(Collectors.toList()));
                dayBO.setStatisticCount(dayBOList.stream().map(StatisticDayBO::getStatisticCount).collect(Collectors.toList()));
            }
        }
        return dayBO;
    }
}
