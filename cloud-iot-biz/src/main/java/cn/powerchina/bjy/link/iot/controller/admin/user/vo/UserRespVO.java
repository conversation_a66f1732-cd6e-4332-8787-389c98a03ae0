package cn.powerchina.bjy.link.iot.controller.admin.user.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 用户信息 Response VO")
@Data
@ExcelIgnoreUnannotated
public class UserRespVO {

    @Schema(description = "主键id，system_user关联用户id", requiredMode = Schema.RequiredMode.REQUIRED, example = "13440")
    @ExcelProperty("主键id，system_user关联用户id")
    private Long id;

    @Schema(description = "用户账号", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @ExcelProperty("用户账号")
    private String username;

    @Schema(description = "姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @ExcelProperty("姓名")
    private String name;

    @Schema(description = "手机号码")
    @ExcelProperty("手机号码")
    private String mobile;

    @Schema(description = "部门名称", example = "张三")
    @ExcelProperty("部门名称")
    private String deptName;

    @Schema(description = "职务", example = "李四")
    @ExcelProperty("职务")
    private String postName;

    @Schema(description = "角色名称", example = "角色名称")
    @ExcelProperty("角色名称")
    private String roleName;

    @Schema(description = "用户邮箱")
    @ExcelProperty("用户邮箱")
    private String email;

    @Schema(description = "启用状态（0正常 1停用）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("启用状态（0正常 1停用）")
    private Integer status;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}