package cn.powerchina.bjy.link.iot.controller.admin.devicepropertylog;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.link.iot.controller.admin.devicepropertylog.vo.DevicePropertyLogModelRespVO;
import cn.powerchina.bjy.link.iot.controller.admin.devicepropertylog.vo.DevicePropertyLogPageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.devicepropertylog.vo.DevicePropertyLogRespVO;
import cn.powerchina.bjy.link.iot.controller.admin.devicepropertylog.vo.DevicePropertyLogSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.devicepropertylog.DevicePropertyLogDO;
import cn.powerchina.bjy.link.iot.enums.ReadTypeEnum;
import cn.powerchina.bjy.link.iot.service.devicepropertylog.DevicePropertyLogService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 设备属性日志")
@RestController
@RequestMapping("/iot/device-property-log")
@Validated
public class DevicePropertyLogController {

    @Resource
    private DevicePropertyLogService devicePropertyLogService;

    @PostMapping("/create")
    @Operation(summary = "创建设备属性日志")
//    @PreAuthorize("@ss.hasPermission('iot:device-property-log:create')")
    public CommonResult<Long> createDevicePropertyLog(@Valid @RequestBody DevicePropertyLogSaveReqVO createReqVO) {
        return success(devicePropertyLogService.createDevicePropertyLog(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新设备属性日志")
//    @PreAuthorize("@ss.hasPermission('iot:device-property-log:update')")
    public CommonResult<Boolean> updateDevicePropertyLog(@Valid @RequestBody DevicePropertyLogSaveReqVO updateReqVO) {
        devicePropertyLogService.updateDevicePropertyLog(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除设备属性日志")
    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('iot:device-property-log:delete')")
    public CommonResult<Boolean> deleteDevicePropertyLog(@RequestParam("id") Long id) {
        devicePropertyLogService.deleteDevicePropertyLog(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得设备属性日志")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('iot:device-property-log:query')")
    public CommonResult<DevicePropertyLogRespVO> getDevicePropertyLog(@RequestParam("id") Long id) {
        DevicePropertyLogDO devicePropertyLog = devicePropertyLogService.getDevicePropertyLog(id);
        return success(BeanUtils.toBean(devicePropertyLog, DevicePropertyLogRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得设备属性日志分页")
//    @PreAuthorize("@ss.hasPermission('iot:device-property-log:query')")
    public CommonResult<PageResult<DevicePropertyLogRespVO>> getDevicePropertyLogPage(@Valid DevicePropertyLogPageReqVO pageReqVO) {
        PageResult<DevicePropertyLogDO> pageResult = devicePropertyLogService.getDevicePropertyLogPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, DevicePropertyLogRespVO.class));
    }

    @GetMapping("/runningState")
    @Operation(summary = "获得设备属性日志分页(运行状态)")
//    @PreAuthorize("@ss.hasPermission('iot:device-property-log:query')")
    public CommonResult<PageResult<DevicePropertyLogModelRespVO>> getDevicePropertyLogRunningState(@RequestParam("productCode") String productCode,
                                                                                                   @RequestParam("deviceCode") String deviceCode,
                                                                                                   @RequestParam(value = "thingName", required = false) String thingName,
                                                                                                   @Valid PageParam pageParam) {
        Integer[] readWriteType = new Integer[]{ReadTypeEnum.READ.getType(), ReadTypeEnum.READ_WRITE.getType(), ReadTypeEnum.WRITE.getType()};//读写类型
        PageResult<DevicePropertyLogModelRespVO> devicePropertyLogRunningState = devicePropertyLogService.getDevicePropertyLogRunningState(productCode, deviceCode, thingName, pageParam, readWriteType);
        return success(devicePropertyLogRunningState);
    }

    @GetMapping("/propertyState")
    @Operation(summary = "获得设备属性日志分页")
//    @PreAuthorize("@ss.hasPermission('iot:device-property-log:query')")
    public CommonResult<PageResult<DevicePropertyLogModelRespVO>> getDevicePropertyLogPropertyState(@RequestParam("productCode") String productCode,
                                                                                                    @RequestParam("deviceCode") String deviceCode,
                                                                                                    @RequestParam(value = "thingName", required = false) String thingName,
                                                                                                    @Valid PageParam pageParam) {
        Integer[] readWriteType = new Integer[]{};//所有读写类型
        PageResult<DevicePropertyLogModelRespVO> devicePropertyLogRunningState = devicePropertyLogService.getDevicePropertyLogRunningState(productCode, deviceCode, thingName, pageParam, readWriteType);
        return success(devicePropertyLogRunningState);
    }

}