package cn.powerchina.bjy.link.iot.controller.admin.index;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.link.iot.controller.admin.index.bo.IndexTotalBO;
import cn.powerchina.bjy.link.iot.controller.admin.index.bo.StatisticDayIndexBO;
import cn.powerchina.bjy.link.iot.controller.admin.index.vo.IndexTotalRespVO;
import cn.powerchina.bjy.link.iot.controller.admin.index.vo.StatisticDayImageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.index.vo.StatisticDayImageRespVO;
import cn.powerchina.bjy.link.iot.service.index.IndexService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

/**
 * @Description: 首页信息
 * @Author: yhx
 * @CreateDate: 2024/8/20
 */
@Tag(name = "管理后台 - 首页")
@RestController
@RequestMapping("/iot/index")
@Validated
public class IndexController {

    @Autowired
    private IndexService indexService;

    @GetMapping("/total/get")
    @Operation(summary = "获得首页统计数量")
    public CommonResult<IndexTotalRespVO> getIndexTotal() {
        IndexTotalBO indexTotalBO = indexService.findIndexTotal();
        return success(BeanUtils.toBean(indexTotalBO, IndexTotalRespVO.class));
    }

    @GetMapping("/data/list")
    @Operation(summary = "获得设备消息数按日统计")
    // @PreAuthorize("@ss.hasPermission('iot:message-statistic-day:query')")
    public CommonResult<StatisticDayImageRespVO> getMessageStatisticDayList(@Valid StatisticDayImageReqVO reqVO) {
        StatisticDayIndexBO result = indexService.getStatisticDayList(reqVO);
        return success(BeanUtils.toBean(result, StatisticDayImageRespVO.class));
    }

}
