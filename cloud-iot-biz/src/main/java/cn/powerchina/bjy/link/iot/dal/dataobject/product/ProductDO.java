package cn.powerchina.bjy.link.iot.dal.dataobject.product;

import cn.powerchina.bjy.cloud.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 产品 DO
 *
 * <AUTHOR>
 */
@TableName("iot_product")
@KeySequence("iot_product_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 产品名称
     */
    private String productName;
    /**
     * 产品编码
     */
    private String productCode;
    /**
     * 产品型号
     */
    private String productModel;
    /**
     * 厂商
     */
    private String firmName;
    /**
     * 产品描述
     */
    private String description;
    /**
     * 节点类型（0直连，1网关，2网关子设备）
     */
    private Integer nodeType;
    /**
     * 协议编号
     */
    private String protocolCode;
    /**
     * 联网方式
     */
    private String networkMethod;
    /**
     * 数据格式
     */
    private String dataFormat;
    /**
     * 产品启用状态（0未启用，1启用，默认1）
     */
    private Integer productState;
    /**
     * 秘钥
     */
    private String productSecret;

    /**
     * 资源空间id
     */
    private Long resourceSpaceId;

    /**
     * 资源空间id
     */
    private Long templateId;

    /**
     * 是否勾选所有设备按钮
     */
    private Boolean isAllDevice;

    /**
     * 设备限额
     */
    private Long deviceLimit;

    /**
     * 已使用额度
     */
    private Long usedQuota;

    /**
     * 动态注册(0:否; 1:是)
     */
    private Integer dynamicRegister;

    /**
     * 预注册(0:否; 1:是)
     */
    private Integer preRegister;

    /**
     * 认证方式(1:设备密钥)
     */
    private Integer authMethod;

}