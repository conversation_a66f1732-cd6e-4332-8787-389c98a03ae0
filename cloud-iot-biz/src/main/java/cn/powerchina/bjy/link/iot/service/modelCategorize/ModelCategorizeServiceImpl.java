package cn.powerchina.bjy.link.iot.service.modelCategorize;

import cn.hutool.core.util.ObjectUtil;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.collection.CollectionUtils;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.link.iot.controller.admin.modelCategorize.bo.ModelCategorizeBO;
import cn.powerchina.bjy.link.iot.controller.admin.modelCategorize.vo.ModelCategorizeListReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.modelCategorize.vo.ModelCategorizePageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.modelCategorize.vo.ModelCategorizeRespVO;
import cn.powerchina.bjy.link.iot.controller.admin.modelCategorize.vo.ModelCategorizeSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.modelCategorize.ModelCategorizeDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.modelTemplate.ModelTemplateDO;
import cn.powerchina.bjy.link.iot.dal.mysql.modelCategorize.ModelCategorizeMapper;
import cn.powerchina.bjy.link.iot.dal.mysql.modelTemplate.ModelTemplateMapper;
import cn.powerchina.bjy.link.iot.enums.ErrorCodeConstants;
import cn.powerchina.bjy.link.iot.model.TreeSelect;
import cn.powerchina.bjy.link.iot.util.SnowFlakeUtil;
import com.google.common.annotations.VisibleForTesting;
import jakarta.annotation.Resource;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;


@Service
@ToString
@Slf4j
public class ModelCategorizeServiceImpl implements ModelCategorizeService {

    @Resource
    private ModelCategorizeMapper modelCategorizeMapper;

    @Resource
    private ModelTemplateMapper modelTemplateMapper;

    @Autowired
    private SnowFlakeUtil snowFlakeUtil;

    @Override
    public PageResult<ModelCategorizeBO> getModelCategorizePage(ModelCategorizePageReqVO pageReqVO) {
        PageResult<ModelCategorizeDO> doPageResult = modelCategorizeMapper.selectPage(pageReqVO);
        PageResult<ModelCategorizeBO> bOPageResult = BeanUtils.toBean(doPageResult, ModelCategorizeBO.class);
        return bOPageResult;
    }

    @Override
    public List<ModelCategorizeDO> getAllList(ModelCategorizeListReqVO pageReqVO) {
//        List<ModelCategorizeDO> list = modelCategorizeMapper.selectList(pageReqVO);
//        List<ModelCategorizeDO> modelCategorizeDOList=new ArrayList<>();
//        if(!CollectionUtils.isAnyEmpty(list))
//        {
//            list.stream().forEach(item -> {
//                modelCategorizeDOList.add(item);
//                if(2==item.getLevel()){
//                    List<ModelTemplateDO> modelTemplateDOList= modelTemplateMapper.selectByCategorizeChildrenId(item.getId());
//                    if(!CollectionUtils.isAnyEmpty(modelTemplateDOList)){
//                        modelTemplateDOList.stream().forEach(model -> {
//                            ModelCategorizeDO categorizeDO=new ModelCategorizeDO();
//                            categorizeDO.setId(model.getId());
//                            categorizeDO.setCategorizeName(model.getTemplateName());
//                            categorizeDO.setLevel(3);
//                            categorizeDO.setParentId(item.getId());
//                            modelCategorizeDOList.add(categorizeDO);
//                        });
//                    }
//                }
//            });
//        }
        return modelCategorizeMapper.selectList(pageReqVO);
    }
    @Override
    public List<ModelCategorizeDO> getAllLists(ModelCategorizeListReqVO pageReqVO) {
        List<ModelCategorizeDO> list = modelCategorizeMapper.selectList(pageReqVO);
        List<ModelCategorizeDO> modelCategorizeDOList=new ArrayList<>();
        if(!CollectionUtils.isAnyEmpty(list))
        {
            list.stream().forEach(item -> {
                modelCategorizeDOList.add(item);
                if(2==item.getLevel()){
                    List<ModelTemplateDO> modelTemplateDOList= modelTemplateMapper.selectByCategorizeChildrenId(item.getId());
                    if(!CollectionUtils.isAnyEmpty(modelTemplateDOList)){
                        modelTemplateDOList.stream().forEach(model -> {
                            ModelCategorizeDO categorizeDO=new ModelCategorizeDO();
                            categorizeDO.setId(model.getId());
                            categorizeDO.setCategorizeName(model.getTemplateName());
                            categorizeDO.setLevel(3);
                            categorizeDO.setParentId(item.getId());
                            modelCategorizeDOList.add(categorizeDO);
                        });
                    }
                }
            });
        }
        return modelCategorizeDOList;
    }
    /**
     * 构建前端所需要下拉树结构
     *
     * @param list 分类列表
     * @return 下拉树结构列表
     */
    @Override
    public List<TreeSelect> buildMenuTreeSelect(List<ModelCategorizeRespVO> list)
    {
        List<ModelCategorizeRespVO> menuTrees = buildMenuTree(list);
        return menuTrees.stream().map(TreeSelect::new).collect(Collectors.toList());
    }
    /**
     * 构建前端所需要树结构
     *
     * @param list 分类列表
     * @return 树结构列表
     */
    @Override
    public List<ModelCategorizeRespVO> buildMenuTree(List<ModelCategorizeRespVO> list)
    {
        List<ModelCategorizeRespVO> returnList = new ArrayList<ModelCategorizeRespVO>();
        List<Long> tempList = list.stream().map(ModelCategorizeRespVO::getId).collect(Collectors.toList());
        for (Iterator<ModelCategorizeRespVO> iterator = list.iterator(); iterator.hasNext();)
        {
            ModelCategorizeRespVO resp = iterator.next();
            // 如果是顶级节点, 遍历该父节点的所有子节点
            if (!tempList.contains(resp.getParentId()))
            {
                recursionFn(list, resp);
                returnList.add(resp);
            }
        }
        if (returnList.isEmpty())
        {
            returnList = list;
        }
        return returnList;
    }
    /**
     * 递归列表
     *
     * @param list 分类表
     * @param t 子节点
     */
    private void recursionFn(List<ModelCategorizeRespVO> list, ModelCategorizeRespVO t)
    {
        // 得到子节点列表
        List<ModelCategorizeRespVO> childList = getChildList(list, t);
        t.setChildren(childList);
        for (ModelCategorizeRespVO tChild : childList)
        {
            if (hasChild(list, tChild))
            {
                recursionFn(list, tChild);
            }
        }
    }
    /**
     * 得到子节点列表
     */
    private List<ModelCategorizeRespVO> getChildList(List<ModelCategorizeRespVO> list, ModelCategorizeRespVO t)
    {
        List<ModelCategorizeRespVO> tlist = new ArrayList<ModelCategorizeRespVO>();
        Iterator<ModelCategorizeRespVO> it = list.iterator();
        while (it.hasNext())
        {
            ModelCategorizeRespVO n = it.next();
            if (n.getParentId().equals(t.getId()))
            {
                tlist.add(n);
            }
        }
        return tlist;
    }

    /**
     * 判断是否有子节点
     */
    private boolean hasChild(List<ModelCategorizeRespVO> list, ModelCategorizeRespVO t)
    {
        return getChildList(list, t).size() > 0;
    }

    @Override
    public List<ModelCategorizeDO> getListByCategorizeId(Long id) {
        List<ModelCategorizeDO> list = modelCategorizeMapper.selectListByCategorizeId(id);
        list.sort(Comparator.comparing(ModelCategorizeDO::getSort));
        return list;
    }

    @Override
    public ModelCategorizeDO getModelCategorize(Long id) {
        return Objects.isNull(id) ? null : modelCategorizeMapper.selectById(id);
    }

    @Override
    public ModelCategorizeBO getModelCategorizeBO(Long id) {
        ModelCategorizeDO modelCategorizeDO = getModelCategorize(id);
        if (Objects.nonNull(modelCategorizeDO)) {
            ModelCategorizeBO modelCategorizeBO = BeanUtils.toBean(modelCategorizeDO, ModelCategorizeBO.class);
            modelCategorizeBO.setLevel(modelCategorizeDO.getLevel());
            modelCategorizeBO.setSort(modelCategorizeDO.getSort());
            modelCategorizeBO.setState(modelCategorizeDO.getState());
            return modelCategorizeBO;
        }
        return null;
    }
    @Override
    public Long createModelCategorize(ModelCategorizeSaveReqVO createReqVO) {
        if (createReqVO.getParentId() == null) {
            createReqVO.setParentId(ModelCategorizeDO.PARENT_ID_ROOT);
        }
        // 校验父分类的有效性
        validateParentModelCategorize(null, createReqVO.getParentId());
        // 校验分类名称的唯一性
        validateCategorizeNameUnique(null, createReqVO.getParentId(), createReqVO.getCategorizeName());

        // 插入分类
        ModelCategorizeDO modelCategorizeDO = BeanUtils.toBean(createReqVO, ModelCategorizeDO.class);
        modelCategorizeDO.setId(snowFlakeUtil.snowflakeId());
        modelCategorizeMapper.insert(modelCategorizeDO);
        return modelCategorizeDO.getId();
    }

    @Override
    public void updateModelCategorize(ModelCategorizeSaveReqVO updateReqVO) {
        if (updateReqVO.getParentId() == null) {
            updateReqVO.setParentId(ModelCategorizeDO.PARENT_ID_ROOT);
        }
        // 校验自己存在
        validateModelCategorizeExists(updateReqVO.getId());
        // 校验父分类的有效性
        validateParentModelCategorize(updateReqVO.getId(), updateReqVO.getParentId());
        // 校验分类名称的唯一性
        validateCategorizeNameUnique(updateReqVO.getId(), updateReqVO.getParentId(), updateReqVO.getCategorizeName());
        // 更新分类
        ModelCategorizeDO updateObj = BeanUtils.toBean(updateReqVO, ModelCategorizeDO.class);
        modelCategorizeMapper.updateById(updateObj);
    }

    @Override
    public void updateModelCategorize(ModelCategorizeDO updateModelCategorizeDO) {
        modelCategorizeMapper.updateById(updateModelCategorizeDO);
    }

    @Override
    public void deleteModelCategorize(Long id) {
        // 校验是否存在
        validateModelCategorizeExists(id);
        // 校验是否有子分类
        if (modelCategorizeMapper.selectCountByParentId(id) > 0) {
            throw exception(ErrorCodeConstants.CATEGORIZE_EXITS_CHILDREN);
        }
        // 校验是否存在物料
        if (modelTemplateMapper.selectCountByCategorizeId(id) > 0) {
            throw exception(ErrorCodeConstants.CATEGORIZE_EXITS_MODEL);
        }
        // 删除分类
        modelCategorizeMapper.deleteById(id);
    }
    @VisibleForTesting
    void validateModelCategorizeExists(Long id) {
        if (id == null) {
            return;
        }
        ModelCategorizeDO modelCategorizeDO = modelCategorizeMapper.selectById(id);
        if (modelCategorizeDO == null) {
            throw exception(ErrorCodeConstants.CATEGORIZE_NOT_FOUND);
        }
    }

    @VisibleForTesting
    void validateParentModelCategorize(Long id, Long parentId) {
        if (parentId == null || ModelCategorizeDO.PARENT_ID_ROOT.equals(parentId)) {
            return;
        }
        // 1. 不能设置自己为父分类
        if (Objects.equals(id, parentId)) {
            throw exception(ErrorCodeConstants.CATEGORIZE_PARENT_ERROR);
        }
        // 2. 父分类不存在
        ModelCategorizeDO parentCategorize = modelCategorizeMapper.selectById(parentId);
        if (parentCategorize == null) {
            throw exception(ErrorCodeConstants.CATEGORIZE_PARENT_NOT_EXITS);
        }
        // 3. 递归校验父分类，如果父分类是自己的子分类，则报错，避免形成环路
        if (id == null) { // id 为空，说明新增，不需要考虑环路
            return;
        }
        for (int i = 0; i < Short.MAX_VALUE; i++) {
            // 3.1 校验环路
            parentId = parentCategorize.getParentId();
            if (Objects.equals(id, parentId)) {
                throw exception(ErrorCodeConstants.CATEGORIZE_PARENT_IS_CHILD);
            }
            // 3.2 继续递归下一级父分类
            if (parentId == null || ModelCategorizeDO.PARENT_ID_ROOT.equals(parentId)) {
                break;
            }
            parentCategorize = modelCategorizeMapper.selectById(parentId);
            if (parentCategorize == null) {
                break;
            }
        }
    }

    @VisibleForTesting
    void validateCategorizeNameUnique(Long id, Long parentId, String name) {
        ModelCategorizeDO modelCategorize = modelCategorizeMapper.selectByParentIdAndName(parentId, name);
        if (modelCategorize == null) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同 id 的分类
        if (id == null) {
            throw exception(ErrorCodeConstants.CATEGORIZE_NAME_DUPLICATE);
        }
        if (ObjectUtil.notEqual(modelCategorize.getId(), id)) {
            throw exception(ErrorCodeConstants.CATEGORIZE_NAME_DUPLICATE);
        }
    }
}