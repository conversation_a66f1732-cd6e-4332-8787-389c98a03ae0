package cn.powerchina.bjy.link.iot.service.devicelog;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.link.iot.controller.admin.devicelog.vo.DeviceLogPageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.devicelog.vo.DeviceLogSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.devicelog.DeviceLogDO;
import cn.powerchina.bjy.link.iot.dal.mysql.devicelog.DeviceLogMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import org.springframework.validation.annotation.Validated;

import static cn.powerchina.bjy.link.iot.enums.ErrorCodeConstants.DEVICE_LOG_NOT_EXISTS;


/**
 * 设备日志 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class DeviceLogServiceImpl implements DeviceLogService {

    @Resource
    private DeviceLogMapper deviceLogMapper;

    @Override
    public Long createDeviceLog(DeviceLogSaveReqVO createReqVO) {
        // 插入
        DeviceLogDO deviceLog = BeanUtils.toBean(createReqVO, DeviceLogDO.class);
        deviceLogMapper.insert(deviceLog);
        // 返回
        return deviceLog.getId();
    }

    @Override
    public void updateDeviceLog(DeviceLogSaveReqVO updateReqVO) {
        // 校验存在
        validateDeviceLogExists(updateReqVO.getId());
        // 更新
        DeviceLogDO updateObj = BeanUtils.toBean(updateReqVO, DeviceLogDO.class);
        deviceLogMapper.updateById(updateObj);
    }

    @Override
    public void deleteDeviceLog(Long id) {
        // 校验存在
        validateDeviceLogExists(id);
        // 删除
        deviceLogMapper.deleteById(id);
    }

    private void validateDeviceLogExists(Long id) {
        if (deviceLogMapper.selectById(id) == null) {
            throw exception(DEVICE_LOG_NOT_EXISTS);
        }
    }

    @Override
    public DeviceLogDO getDeviceLog(Long id) {
        return deviceLogMapper.selectById(id);
    }

    @Override
    public PageResult<DeviceLogDO> getDeviceLogPage(DeviceLogPageReqVO pageReqVO) {
        return deviceLogMapper.selectPage(pageReqVO);
    }

}