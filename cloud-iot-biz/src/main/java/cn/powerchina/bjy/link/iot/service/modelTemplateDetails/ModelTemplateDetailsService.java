package cn.powerchina.bjy.link.iot.service.modelTemplateDetails;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.link.iot.controller.admin.modelTemplateDetails.bo.ModelTemplateDetailsBO;
import cn.powerchina.bjy.link.iot.controller.admin.modelTemplateDetails.vo.ModelTemplateDetailsPageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.modelTemplateDetails.vo.ModelTemplateDetailsSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.modelTemplateDetails.ModelTemplateDetailsDO;
import jakarta.validation.Valid;

/**
 * 物模型分类信息表
 *
 * <AUTHOR>
 * @date 2025-03-25 11:00:21
 */
public interface ModelTemplateDetailsService {

    /**
     * 获得产品分页
     *
     * @param pageReqVO 分页查询
     * @return 产品分页
     */
    PageResult<ModelTemplateDetailsBO> getModelTemplateDetailsPage(ModelTemplateDetailsPageReqVO pageReqVO);

    /**
     * 获得产品
     *
     * @param id 编号
     * @return 产品
     */
    ModelTemplateDetailsDO getModelTemplateDetails(Long id);

    /**
     * 获得产品
     *
     * @param id 编号
     * @return 产品
     */
    ModelTemplateDetailsBO getModelTemplateDetailsBO(Long id);
    /**
     * 创建产品
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createModelTemplateDetails(@Valid ModelTemplateDetailsSaveReqVO createReqVO);

    /**
     * 更新产品
     *
     * @param updateReqVO 更新信息
     */
    void updateModelTemplateDetails(@Valid ModelTemplateDetailsSaveReqVO updateReqVO);

    void updateModelTemplateDetails(ModelTemplateDetailsDO updateModelCategorizeDO);

    /**
     * 删除产品
     *
     * @param id 编号
     */
    void deleteModelTemplateDetails(Long id);

}

