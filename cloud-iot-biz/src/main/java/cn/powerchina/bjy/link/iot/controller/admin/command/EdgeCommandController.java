package cn.powerchina.bjy.link.iot.controller.admin.command;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.link.iot.controller.admin.command.vo.EdgeCommandReqVO;
import cn.powerchina.bjy.link.iot.service.edgecommand.EdgeCommandService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 设备指令")
@RestController
@RequestMapping("/iot/command")
@Validated
public class EdgeCommandController {

    @Resource
    private EdgeCommandService edgeCommandService;

    @PostMapping("/down")
    @Operation(summary = "设备指令下发")
//    @PreAuthorize("@ss.hasPermission('iot:command:down')")
    public CommonResult<Boolean> commandDown(@Valid @RequestBody EdgeCommandReqVO edgeCommandReqVO) {
//        {
//            "thingName": "采集通道数据",
//            "thingIdentity": "channel_value",
//            "inputParams": "[{\"thingIdentity\":\"channels\",\"thingValue\":16,\"datatype\":\"STRING\"}]",
//            "operatorSource": 0,
//            "productCode": "CP1726102887459180",
//            "deviceCode": "D1726128048418604",
//            "thingType": 2
//        }
        edgeCommandService.commandDown(edgeCommandReqVO);
        return success(true);
    }
}
