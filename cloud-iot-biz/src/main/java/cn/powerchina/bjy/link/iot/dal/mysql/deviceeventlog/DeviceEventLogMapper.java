package cn.powerchina.bjy.link.iot.dal.mysql.deviceeventlog;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.iot.controller.admin.deviceeventlog.vo.DeviceEventLogPageReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.deviceeventlog.DeviceEventLogDO;
import com.alibaba.nacos.common.utils.CollectionUtils;
import org.apache.ibatis.annotations.Mapper;

/**
 * 设备事件日志 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface DeviceEventLogMapper extends BaseMapperX<DeviceEventLogDO> {

    default PageResult<DeviceEventLogDO> selectPage(DeviceEventLogPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<DeviceEventLogDO>()
                .eqIfPresent(DeviceEventLogDO::getDeviceCode, reqVO.getDeviceCode())
                .likeIfPresent(DeviceEventLogDO::getDeviceName, reqVO.getDeviceName())
                .eqIfPresent(DeviceEventLogDO::getThingIdentity, reqVO.getThingIdentity())
                .likeIfPresent(DeviceEventLogDO::getThingName, reqVO.getThingName())
                .eqIfPresent(DeviceEventLogDO::getEventType, reqVO.getEventType())
                .eqIfPresent(DeviceEventLogDO::getThingValue, reqVO.getThingValue())
                .eqIfPresent(DeviceEventLogDO::getDeviceMode, reqVO.getDeviceMode())
                .eqIfPresent(DeviceEventLogDO::getRemark, reqVO.getRemark())
                .betweenIfPresent(DeviceEventLogDO::getCreateTime, reqVO.getCreateTime())
                .in(CollectionUtils.isNotEmpty(reqVO.getDeviceCodes()),DeviceEventLogDO::getDeviceCode,reqVO.getDeviceCodes())
                .orderByDesc(DeviceEventLogDO::getId));
    }

}