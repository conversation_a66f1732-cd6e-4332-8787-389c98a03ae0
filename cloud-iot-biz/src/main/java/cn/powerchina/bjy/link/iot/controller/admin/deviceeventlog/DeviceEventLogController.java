package cn.powerchina.bjy.link.iot.controller.admin.deviceeventlog;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.link.iot.controller.admin.deviceeventlog.vo.*;
import cn.powerchina.bjy.link.iot.dal.dataobject.deviceeventlog.DeviceEventLogDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.product.ProductDO;
import cn.powerchina.bjy.link.iot.service.deviceeventlog.DeviceEventLogService;
import cn.powerchina.bjy.link.iot.service.product.ProductService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Objects;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 设备事件日志")
@RestController
@RequestMapping("/iot/device-event-log")
@Validated
public class DeviceEventLogController {

    @Resource
    private DeviceEventLogService deviceEventLogService;

    @Resource
    private ProductService productService;

    @PostMapping("/create")
    @Operation(summary = "创建设备事件日志")
//    @PreAuthorize("@ss.hasPermission('iot:device-event-log:create')")
    public CommonResult<Long> createDeviceEventLog(@Valid @RequestBody DeviceEventLogSaveReqVO createReqVO) {
        return success(deviceEventLogService.createDeviceEventLog(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新设备事件日志")
//    @PreAuthorize("@ss.hasPermission('iot:device-event-log:update')")
    public CommonResult<Boolean> updateDeviceEventLog(@Valid @RequestBody DeviceEventLogSaveReqVO updateReqVO) {
        deviceEventLogService.updateDeviceEventLog(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除设备事件日志")
    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('iot:device-event-log:delete')")
    public CommonResult<Boolean> deleteDeviceEventLog(@RequestParam("id") Long id) {
        deviceEventLogService.deleteDeviceEventLog(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得设备事件日志")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('iot:device-event-log:query')")
    public CommonResult<DeviceEventLogRespVO> getDeviceEventLog(@RequestParam("id") Long id) {
        DeviceEventLogDO deviceEventLog = deviceEventLogService.getDeviceEventLog(id);
        return success(BeanUtils.toBean(deviceEventLog, DeviceEventLogRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得设备事件日志分页")
//    @PreAuthorize("@ss.hasPermission('iot:device-event-log:query')")
    public CommonResult<PageResult<DeviceEventLogRespVO>> getDeviceEventLogPage(@Valid DeviceEventLogPageReqVO pageReqVO) {
        PageResult<DeviceEventLogDO> pageResult = deviceEventLogService.getDeviceEventLogPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, DeviceEventLogRespVO.class));
    }
    @GetMapping("/getPage")
    @Operation(summary = "获得所有设备事件日志分页")
//    @PreAuthorize("@ss.hasPermission('iot:device-event-log:query')")
    public CommonResult<PageResult<DeviceEventLogRespVO>> getAllDeviceEventLogPage(@Valid DeviceEventLogPageReqVO pageReqVO) {
        return success(deviceEventLogService.getAllDeviceEventLogPage(pageReqVO));
    }
    @GetMapping("/search-products")
    @Operation(summary = "根据关键字搜索产品列表")
    @Parameter(name = "keyword", description = "搜索关键字", example = "温控器")
    public CommonResult<List<ProductVO>> searchProducts(@RequestParam(required = false) String keyword,@RequestParam(required = false) Long resourceSpaceId) {
        // 调用 service 查询产品列表
        List<ProductDO> productList = productService.searchProductsByKeyword(keyword,resourceSpaceId);
        // 转换为 VO 对象
        List<ProductVO> productVOList = BeanUtils.toBean(productList, ProductVO.class);
        return success(productVOList);
    }

    @GetMapping("/event-types")
    @Operation(summary = "获取某产品下所有事件类型")
    public CommonResult<List<EventTypeVO>> getEventTypesByProduct(@RequestParam String productCode) {
        return success(deviceEventLogService.getEventTypesByProduct(productCode));
    }

    @GetMapping("/event-names")
    @Operation(summary = "获取某产品+事件类型下所有事件名称")
    public CommonResult<List<EventNameVO>> getEventNamesByProductAndType(@RequestParam String productCode, @RequestParam Integer eventType) {
        return success(deviceEventLogService.getEventNamesByProductAndType(productCode, eventType));
    }

/*    @GetMapping("/runningState")
    @Operation(summary = "获得设备运行状态事件日志分页")
    @PreAuthorize("@ss.hasPermission('iot:device-property-log:query')")
    public CommonResult<PageResult<Map<String, Object>>> getDevicePropertyLogRunningState(@RequestParam("productCode") String productCode,
                                                                                          @RequestParam("deviceCode") String deviceCode,
                                                                                          @RequestParam(value = "thingName", required = false) String thingName,
                                                                                          @RequestParam(value = "eventType", required = false) Integer eventType,
                                                                                          @Valid PageParam pageParam) {
        PageResult<Map<String, Object>> devicePropertyLogRunningState = deviceEventLogService.getDeviceEventLogRunningState(productCode, deviceCode, thingName, eventType, pageParam);
        return success(devicePropertyLogRunningState);


    }*/

}