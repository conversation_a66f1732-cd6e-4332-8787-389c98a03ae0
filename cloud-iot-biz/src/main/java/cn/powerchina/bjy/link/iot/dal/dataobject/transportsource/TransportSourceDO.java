package cn.powerchina.bjy.link.iot.dal.dataobject.transportsource;

import cn.powerchina.bjy.cloud.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

/**
 * 转发规则-数据源 DO
 *
 * <AUTHOR>
 */
@TableName("iot_transport_source")
@KeySequence("iot_transport_source_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TransportSourceDO extends BaseDO {

    /**
     * 主键id
     */
    @TableId
    private Long id;
    /**
     * 规则id
     */
    private Long ruleId;
    /**
     * 数据类型，逗号分隔
     */
    private String dataType;
    /**
     * 资源空间id：全部是-1
     */
    private Long resourceSpaceId;
    /**
     * 产品code：全部是-1
     */
    private String productCode;
    /**
     * 设备code:全部-1
     */
    private String deviceCode;

    /**
     * 资源空间名称
     */
    private String resourceSpaceName;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 设备名称
     */
    private String deviceName;

}