package cn.powerchina.bjy.link.iot.controller.admin.transportsource.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 转发规则-数据源 Response VO")
@Data
@ExcelIgnoreUnannotated
public class TransportSourceRespVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "14614")
    @ExcelProperty("主键id")
    private Long id;

    @Schema(description = "规则id", requiredMode = Schema.RequiredMode.REQUIRED, example = "27450")
    @ExcelProperty("规则id")
    private Long ruleId;

    @Schema(description = "数据类型，逗号分隔", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("数据类型，逗号分隔")
    private String dataType;

    @Schema(description = "数据类型，逗号分隔", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("数据类型，逗号分隔")
    private String dataTypeName;

    @Schema(description = "资源空间id：全部是-1", example = "29505")
    @ExcelProperty("资源空间id：全部是-1")
    private Long resourceSpaceId;

    @Schema(description = "资源空间名称")
    private String resourceSpaceName;

    @Schema(description = "产品code：全部是-1", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("产品code：全部是-1")
    private String productCode;

    @Schema(description = "产品名称")
    private String productName;

    @Schema(description = "设备code:全部-1", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("设备code:全部-1")
    private String deviceCode;

    @Schema(description = "设备名称")
    private String deviceName;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}