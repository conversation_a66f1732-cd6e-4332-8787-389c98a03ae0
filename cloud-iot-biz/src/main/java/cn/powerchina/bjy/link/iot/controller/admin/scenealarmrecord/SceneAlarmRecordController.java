package cn.powerchina.bjy.link.iot.controller.admin.scenealarmrecord;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.excel.core.util.ExcelUtils;
import cn.powerchina.bjy.cloud.framework.security.core.util.SecurityFrameworkUtils;
import cn.powerchina.bjy.link.iot.controller.admin.scenealarmrecord.vo.*;
import cn.powerchina.bjy.link.iot.dal.dataobject.scenealarmrecord.SceneAlarmRecordDO;
import cn.powerchina.bjy.link.iot.service.scenealarmrecord.SceneAlarmRecordService;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.constraints.*;
import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;
import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 告警记录")
@RestController
@RequestMapping("/iot/scene-alarm-record")
@Validated
public class SceneAlarmRecordController {

    @Resource
    private SceneAlarmRecordService sceneAlarmRecordService;

    @PostMapping("/create")
    @Operation(summary = "创建告警记录")
//    @PreAuthorize("@ss.hasPermission('iot:scene-alarm-record:create')")
    public CommonResult<Long> createSceneAlarmRecord(@Valid @RequestBody SceneAlarmRecordSaveReqVO createReqVO) {
        return success(sceneAlarmRecordService.createSceneAlarmRecord(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新告警记录")
//    @PreAuthorize("@ss.hasPermission('iot:scene-alarm-record:update')")
    public CommonResult<Boolean> updateSceneAlarmRecord(@Valid @RequestBody SceneAlarmRecordSaveReqVO updateReqVO) {
        sceneAlarmRecordService.updateSceneAlarmRecord(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除告警记录")
    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('iot:scene-alarm-record:delete')")
    public CommonResult<Boolean> deleteSceneAlarmRecord(@RequestParam("id") Long id) {
        sceneAlarmRecordService.deleteSceneAlarmRecord(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得告警记录")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('iot:scene-alarm-record:query')")
    public CommonResult<SceneAlarmRecordRespVO> getSceneAlarmRecord(@RequestParam("id") Long id) {
        SceneAlarmRecordDO sceneAlarmRecord = sceneAlarmRecordService.getSceneAlarmRecord(id);
        return success(BeanUtils.toBean(sceneAlarmRecord, SceneAlarmRecordRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得告警记录分页")
//    @PreAuthorize("@ss.hasPermission('iot:scene-alarm-record:query')")
    public CommonResult<PageResult<SceneAlarmRecordRespVO>> getSceneAlarmRecordPage(@Valid SceneAlarmRecordPageReqVO pageReqVO) {
        return success(sceneAlarmRecordService.getSceneAlarmRecordPage(pageReqVO));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出告警记录 Excel")
//    @PreAuthorize("@ss.hasPermission('iot:scene-alarm-record:export')")
    public void exportSceneAlarmRecordExcel(@Valid SceneAlarmRecordPageReqVO pageReqVO,
                                            HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<SceneAlarmRecordRespVO> list = sceneAlarmRecordService.getSceneAlarmRecordPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "告警记录.xls", "数据", SceneAlarmRecordRespVO.class, list);
    }
    @PostMapping("/process")
    @Operation(summary = "处理告警记录")
    public CommonResult<Boolean> processAlarmRecord(@Valid @RequestBody AlarmProcessReqVO processReqVO) {
        //获取当前登录用户
        Long userID = SecurityFrameworkUtils.getLoginUserId();
        String userName = SecurityFrameworkUtils.getLoginUserName();
        //处理告警
        boolean result = sceneAlarmRecordService.processAlarmRecord(processReqVO.getId(),
                processReqVO.getProcessRecord(),userID, userName);
        return success(result);
    }

    @PostMapping("/recover")
    @Operation(summary = "恢复告警记录")
//    @PreAuthorize("@ss.hasPermission('iot:scene-rule-alarm-record:process')")
    public CommonResult<Boolean> recoverAlarmRecord(@Valid @RequestBody AlarmRecoverReqVO recoverReqVO) {
        // 获取当前登录用户
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        String userName = SecurityFrameworkUtils.getLoginUserName();

        // 恢复告警
        boolean result = sceneAlarmRecordService.recoverAlarmRecord(
                recoverReqVO.getId(),
                userId,
                userName);

        return success(result);
    }

    @GetMapping("/getAlarmRecordDetail")
    @Operation(summary = "告警记录详情")
//    @PreAuthorize("@ss.hasPermission('iot:scene-rule-alarm-record:process')")
    public CommonResult<SceneAlarmRecordRespVO> getAlarmRecordDetail(@RequestParam("alarmId") Long alarmId, @RequestParam("alarmDetailId") Long alarmDetailId) {
        return success(sceneAlarmRecordService.getAlarmRecordDetail(alarmId, alarmDetailId));
    }

}
