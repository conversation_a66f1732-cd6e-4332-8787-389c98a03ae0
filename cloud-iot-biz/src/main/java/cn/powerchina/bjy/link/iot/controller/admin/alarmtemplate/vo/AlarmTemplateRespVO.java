package cn.powerchina.bjy.link.iot.controller.admin.alarmtemplate.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 告警模板 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AlarmTemplateRespVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "2457")
    @ExcelProperty("主键id")
    private Long id;

    @Schema(description = "规则ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "25655")
    @ExcelProperty("规则ID")
    private Long ruleId;

    @Schema(description = "告警名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @ExcelProperty("告警名称")
    private String alarmName;

    @Schema(description = "告警等级(1-轻微,2-中等,3-严重,4-非常严重)")
    @ExcelProperty("告警等级(1-轻微,2-中等,3-严重,4-非常严重)")
    private Integer alarmLevel;

    @Schema(description = "告警描述")
    @ExcelProperty("告警描述")
    private String alarmContent;

    @Schema(description = "排序", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("排序")
    private Integer sort;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
