package cn.powerchina.bjy.link.iot.controller.admin.product.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.util.Date;

/**
 * @Description: 描述
 * @Author: yhx
 * @CreateDate: 2024/8/19
 */
@Data
@Builder
public class ProductCountReqVO {

    @Schema(description = "开始时间")
    private Date startDate;

    @Schema(description = "结束时间")
    private Date endDate;
}
