package cn.powerchina.bjy.link.iot.dal.mysql.messagestatisticday;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.iot.controller.admin.messagestatisticday.vo.MessageStatisticDayPageReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.messagestatisticday.MessageStatisticDayDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

/**
 * 设备消息数按日统计 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface MessageStatisticDayMapper extends BaseMapperX<MessageStatisticDayDO> {

    default PageResult<MessageStatisticDayDO> selectPage(MessageStatisticDayPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<MessageStatisticDayDO>()
                .eqIfPresent(MessageStatisticDayDO::getStatisticDay, reqVO.getStatisticDay())
                .eqIfPresent(MessageStatisticDayDO::getStatisticCount, reqVO.getStatisticCount())
                .betweenIfPresent(MessageStatisticDayDO::getStatisticDay, reqVO.getStatisticTime())
                .orderByAsc(MessageStatisticDayDO::getStatisticDay));
    }

    /**
     * 统计消息数量
     *
     * @param statisticDay
     * @param statisticType
     * @return
     */
    Long selectCountMessage(@Param("statisticDay") Date statisticDay, @Param("statisticType") Integer statisticType);

    /**
     * 更新统计消息数量
     *
     * @param statisticDay
     * @param statisticType
     * @return
     */
    Integer updateStatisticCount(@Param("statisticDay") Date statisticDay, @Param("statisticType") Integer statisticType,
                                 @Param("statisticCount") Long statisticCount);
}
