package cn.powerchina.bjy.link.iot.controller.admin.notificationmethod.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 通知方式 Response VO")
@Data
@ExcelIgnoreUnannotated
public class NotificationMethodRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "25671")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "关联的告警模板ID", example = "23110")
    @ExcelProperty("关联的告警模板ID")
    private Long alarmTemplateId;

    @Schema(description = "通知方式（1:钉钉 2：邮件 3：短信）")
    @ExcelProperty("通知方式（1:钉钉 2：邮件 3：短信）")
    private Integer notificationMethod;

    @Schema(description = "通知账号/webhook地址", example = "22183")
    @ExcelProperty("通知账号/webhook地址")
    private String notificationAccount;

    @Schema(description = "通知内容")
    @ExcelProperty("通知内容")
    private String notificationContent;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
