package cn.powerchina.bjy.link.iot.service.transporttarget;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.link.iot.controller.admin.transporttarget.vo.TransportTargetPageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.transporttarget.vo.TransportTargetRespVO;
import cn.powerchina.bjy.link.iot.controller.admin.transporttarget.vo.TransportTargetSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.transportsource.TransportSourceDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.transporttarget.TransportTargetDO;
import cn.powerchina.bjy.link.iot.enums.TransportSourceTypeEnum;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 转发规则-转发目标 Service 接口
 *
 * <AUTHOR>
 */
public interface TransportTargetService {

    /**
     * 创建转发规则-转发目标
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createTransportTarget(@Valid TransportTargetSaveReqVO createReqVO);

    /**
     * 更新转发规则-转发目标
     *
     * @param updateReqVO 更新信息
     */
    void updateTransportTarget(@Valid TransportTargetSaveReqVO updateReqVO);

    /**
     * 删除转发规则-转发目标
     *
     * @param id 编号
     */
    void deleteTransportTarget(Long id);

    /**
     * 获得转发规则-转发目标
     *
     * @param id 编号
     * @return 转发规则-转发目标
     */
    TransportTargetDO getTransportTarget(Long id);

    /**
     * 获得转发规则-转发目标分页
     *
     * @param pageReqVO 分页查询
     * @return 转发规则-转发目标分页
     */
    PageResult<TransportTargetRespVO> getTransportTargetPage(TransportTargetPageReqVO pageReqVO);

    List<TransportTargetDO> getTransportTargetList();

    void dataTransportTarget(TransportSourceTypeEnum dataType, List<TransportSourceDO> transportSourceList, Object obj);
}