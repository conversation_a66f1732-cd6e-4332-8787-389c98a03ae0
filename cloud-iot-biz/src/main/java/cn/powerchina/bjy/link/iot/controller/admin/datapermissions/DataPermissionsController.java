package cn.powerchina.bjy.link.iot.controller.admin.datapermissions;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.link.iot.controller.admin.datapermissions.vo.DataPermissionsPageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.datapermissions.vo.DataPermissionsRespVO;
import cn.powerchina.bjy.link.iot.controller.admin.datapermissions.vo.DataPermissionsSaveReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.datapermissions.vo.DataPermissionsTreeVO;
import cn.powerchina.bjy.link.iot.controller.admin.modelCategorize.vo.ModelCategorizeListReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.modelCategorize.vo.ModelCategorizeRespVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.datapermissions.DataPermissionsDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.modelCategorize.ModelCategorizeDO;
import cn.powerchina.bjy.link.iot.service.datapermissions.DataPermissionsService;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.*;

import java.util.List;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 数据权限")
@RestController
@RequestMapping("/iot/data/permissions")
@Validated
public class DataPermissionsController {

    @Resource
    private DataPermissionsService dataPermissionsService;

    @PostMapping("/create")
    @Operation(summary = "创建数据权限")
    public CommonResult<Boolean> createDataPermissions(@Valid @RequestBody DataPermissionsSaveReqVO createReqVO) {
        return success(dataPermissionsService.createDataPermissions(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新数据权限")
    public CommonResult<Boolean> updateDataPermissions(@Valid @RequestBody DataPermissionsSaveReqVO updateReqVO) {
        return CommonResult.success( dataPermissionsService.updateDataPermissions(updateReqVO));
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除数据权限")
    @Parameter(name = "id", description = "编号", required = true)
    public CommonResult<Boolean> deleteDataPermissions(@RequestParam("id") Long id) {
        dataPermissionsService.deleteDataPermissions(id);
        return success(true);
    }

//    @GetMapping("/getDataPermissions")
//    @Operation(summary = "获取当前用户拥有的数据权限")
//    public CommonResult<DataPermissionsSaveReqVO> getDataPermissions() {
//        DataPermissionsSaveReqVO dataPermissions = dataPermissionsService.getDataPermissions();
//        return success(dataPermissions);
//    }

    @GetMapping("/page")
    @Operation(summary = "获得数据权限分页")
    public CommonResult<PageResult<DataPermissionsRespVO>> getDataPermissionsPage(@Valid DataPermissionsPageReqVO pageReqVO) {
        PageResult<DataPermissionsDO> pageResult = dataPermissionsService.getDataPermissionsPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, DataPermissionsRespVO.class));
    }
    @GetMapping("/getAllList")
    @Operation(summary = "获取数据权限列表", description = "获取数据权限列表")
    public CommonResult<List<DataPermissionsTreeVO>> getAllList() {
        List<DataPermissionsTreeVO> list = dataPermissionsService.getAllList();
        return success(list);
    }
    @GetMapping("/getAllListByRoleId")
    @Operation(summary = "根据角色id获取当前的数据权限列表", description = "根据角色id获取当前的数据权限列表")
    public CommonResult<DataPermissionsSaveReqVO> getAllListByRoleId(String roleId) {
        return success(dataPermissionsService.getAllListByRoleId(roleId));
    }

}