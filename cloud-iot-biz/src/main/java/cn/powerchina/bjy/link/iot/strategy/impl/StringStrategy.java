package cn.powerchina.bjy.link.iot.strategy.impl;

import cn.powerchina.bjy.cloud.framework.common.util.json.JsonUtils;
import cn.powerchina.bjy.link.iot.dto.up.EdgeReadPropertyValue;
import cn.powerchina.bjy.link.iot.strategy.ProductModelStrategy;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * StringStrategy
 *
 * <AUTHOR>
 **/
@Slf4j
@Component("stringStrategy")
public class StringStrategy implements ProductModelStrategy {

    private final static String KEY = "length";

    /**
     * 字符串类型规则为长度不超过某个值
     * 1、规则为空则不忽略；
     * 2、如果规则长度不存在则不忽略；
     * 3、如果具体内容为空或者内容长度超过规则值则忽略；
     *
     * @param context  具体内容
     * @param strategy 规则
     * @return
     */
    @Override
    public boolean shouldIgnore(Object context, String strategy) {
        if (StringUtils.isBlank(strategy) || null == context) {
            return false;
        }
        try {
            Map<String, Integer> item = JsonUtils.parseObject(strategy, new TypeReference<>() {
            });
            Integer length = item.get(KEY);
            if (null == length) {
                return false;
            }
            String data = (String) context;
            return StringUtils.isEmpty(data) || data.length() > length;
        } catch (Exception e) {
            log.error("物模型数据类型【string】配置错误，配置【{}】", strategy, e);
        }
        return true;
    }

    @Override
    public boolean shouldIgnore(String context, String strategy, EdgeReadPropertyValue.EdgeDevicePropertyValueDTO valueDTO) {
        if (StringUtils.isBlank(strategy) || null == context) {
            return false;
        }
        try {
            Map<String, Integer> item = JsonUtils.parseObject(strategy, new TypeReference<>() {
            });
            Integer length = item.get(KEY);
            if (null == length) {
                return false;
            }
            return StringUtils.isEmpty(context) || context.length() > length;
        } catch (Exception e) {
            log.error("物模型数据类型【string】配置错误，值【{}】配置【{}】", context, strategy, e);
        }
        return true;
    }
}
