package cn.powerchina.bjy.link.iot.controller.admin.scenerule.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;

import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 场景规则 Response VO")
@Data
@ExcelIgnoreUnannotated
public class SceneRulePageRespVO {

    @Schema(description = "主键id", example = "16863")
    @ExcelProperty("主键id")
    private Long id;

    @Schema(description = "规则名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    @ExcelProperty("规则名称")
    @NotNull(message = "规则名称不能为空")
    private String ruleName;

    @Schema(description = "所属资源空间ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "15362")
    @ExcelProperty("所属资源空间ID")
    @NotNull(message = "所属资源空间不能为空")
    private Long resourceSpaceId;

    @Schema(description = "资源空间名称")
    @ExcelProperty("资源空间名称")
    private String spaceName;

    @Schema(description = "状态:0-禁用,1-启用", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("状态:0-禁用,1-启用")
    private Integer status;

    @Schema(description = "是否抑制:0-禁用抑制,1-启用抑制", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("是否抑制:0-禁用抑制,1-启用抑制")
    private Integer inhibition;

    @Schema(description = "规则优先级")
    @ExcelProperty("规则优先级")
    private Integer rulePriority;

    @Schema(description = "创建人")
    @ExcelProperty("创建人")
    private String creatorName;

    @Schema(description = "规则描述")
    private String ruleDesc;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createTime;

}