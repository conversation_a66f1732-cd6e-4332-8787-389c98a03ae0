package cn.powerchina.bjy.link.iot.controller.admin.alarmtemplate.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "告警发送模板")
@Data
@ExcelIgnoreUnannotated
public class AlarmMsgRespVO {

    @Schema(description = "触发时间")
    @ExcelProperty("触发时间")
    private String triggerTime;

    @Schema(description = "设备编码")
    @ExcelProperty("设备编码")
    private String deviceCode;

    @Schema(description = "设备名称")
    @ExcelProperty("设备名称")
    private String deviceName;

    @Schema(description = "产品名称")
    @ExcelProperty("产品名称")
    private String productName;

    @Schema(description = "规则名称", example = "赵六")
    @ExcelProperty("规则名称")
    private String ruleName;

    @Schema(description = "告警等级(1-轻微,2-中等,3-严重,4-非常严重)")
    @ExcelProperty("告警等级(1-轻微,2-中等,3-严重,4-非常严重)")
    private String alarmLevel;

    @Schema(description = "告警名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @ExcelProperty("告警名称")
    private String alarmName;

    @Schema(description = "告警内容")
    @ExcelProperty("告警内容")
    private String alarmContent;


}
