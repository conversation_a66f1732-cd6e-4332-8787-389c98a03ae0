package cn.powerchina.bjy.link.iot.dto.down;

import cn.powerchina.bjy.link.iot.model.MessageToEdge;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 物联网平台指令下发
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EdgeCommandDTO implements MessageToEdge, Serializable {

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 设备编码
     */
    private String deviceCode;

    /**
     * 网关实例编码
     */
    private String edgeCode;

    /**
     * 从站号
     */
    private String slaveId;

    /**
     * 驱动code
     */
    private String driverCode;

    /**
     * 连接方式（1-RTU；2-TCP）
     */
    private Integer connectType;

    /**
     * 连接配置详细信息，格式json
     * 1、RTU包括串口、波特率、数据位、校验位、停止位
     * 2、TCP包括IP地址、端口、数据位
     */
    private String extra;

    /**
     * 是否是采集仪，1：是，0：否
     */
    private Integer selfType;

    /**
     * 消息id
     */
    private String messageId;

    /**
     * 下发时间
     */
    private long currentTime;

    /**
     * 控制符
     */
    private String thingIdentity;

    /**
     * 额外信息
     */
    private Map<String, Object> extraMap;

    /**
     * 子设备信息
     */
    private List<EdgeDeviceCommandDTO> deviceCommandDTOList;

    /**
     * 子设备控制信息
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class EdgeDeviceCommandDTO {
        /**
         * 产品编码
         */
        private String productCode;
        /**
         * 设备编码
         */
        private String deviceCode;

        /**
         * MCU通道编码
         */
        private String mcuChannel;

        /**
         * 产品物模型对应数据位，如(property1,1)
         */
        private Map<String, Integer> thingIdentityMap;

        /**
         * 额外参数
         */
        private String extra;
    }
}
