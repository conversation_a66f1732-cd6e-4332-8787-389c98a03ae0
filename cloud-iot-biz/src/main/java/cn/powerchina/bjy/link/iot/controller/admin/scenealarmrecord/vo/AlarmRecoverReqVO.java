package cn.powerchina.bjy.link.iot.controller.admin.scenealarmrecord.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(description = "管理后台 - 告警恢复请求 VO")
@Data
public class AlarmRecoverReqVO {

    @Schema(description = "告警记录ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "告警记录ID不能为空")
    private Long id;
}
