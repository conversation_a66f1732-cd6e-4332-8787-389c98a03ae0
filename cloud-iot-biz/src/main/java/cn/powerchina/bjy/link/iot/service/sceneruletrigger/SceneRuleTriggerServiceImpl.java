package cn.powerchina.bjy.link.iot.service.sceneruletrigger;

import cn.hutool.core.collection.CollectionUtil;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.iot.controller.admin.sceneruletrigger.vo.SceneRuleTriggerPageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.sceneruletrigger.vo.SceneRuleTriggerSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.datapermissions.DataPermissionsDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.sceneruletrigger.SceneRuleTriggerDO;
import cn.powerchina.bjy.link.iot.dal.mysql.device.DeviceMapper;
import cn.powerchina.bjy.link.iot.dal.mysql.product.ProductMapper;
import cn.powerchina.bjy.link.iot.dal.mysql.sceneruletrigger.SceneRuleTriggerMapper;
import cn.powerchina.bjy.link.iot.enums.ConditionTypeEnum;
import cn.powerchina.bjy.link.iot.enums.TriggerTypeEnum;
import cn.powerchina.bjy.link.iot.service.sceneruletimetrigger.SceneRuleTimeTriggerService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import java.util.stream.Collectors;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.link.iot.enums.ErrorCodeConstants.SCENE_RULE_TRIGGER_NOT_EXISTS;

/**
 * 规则触发/条件限制 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SceneRuleTriggerServiceImpl implements SceneRuleTriggerService {

    @Resource
    private SceneRuleTriggerMapper sceneRuleTriggerMapper;

    @Resource
    private SceneRuleTimeTriggerService ruleTimeTriggerService;

    @Resource
    private DeviceMapper deviceMapper;

    @Resource
    private ProductMapper productMapper;

    @Override
    public void createAndUpdateSceneRuleTrigger(List<SceneRuleTriggerSaveReqVO> saveOrUpdateList, Long ruleId, Integer type) {
        List<Long> oldIdList = new ArrayList<>();
        //Integer conditionType = saveOrUpdateList.get(0).getConditionType();
        List<SceneRuleTriggerDO> oldEntityList = getSceneRuleTriggerByRuleId(ruleId, type);
        if (!CollectionUtils.isEmpty(oldEntityList)) {
            oldIdList = oldEntityList.stream().map(SceneRuleTriggerDO::getId).collect(Collectors.toList());
        }
        //saveOrUpdateList为空，oldIdList不为空时，说明是全部删除了。将所属条件全部删除
        if (CollectionUtil.isEmpty(saveOrUpdateList) && !CollectionUtils.isEmpty(oldIdList)) {
            sceneRuleTriggerMapper.deleteBatchIds(oldIdList);
            return;
        }

        for (SceneRuleTriggerSaveReqVO ruleTriggerSaveReqVO : saveOrUpdateList) {
            ruleTriggerSaveReqVO.setRuleId(ruleId);
            ruleTriggerSaveReqVO.setConditionType(type);
            Long triggerId = ruleTriggerSaveReqVO.getId();
            if (Objects.isNull(ruleTriggerSaveReqVO.getId())) {
                triggerId = createSceneRuleTrigger(ruleTriggerSaveReqVO);
            } else {
                updateSceneRuleTrigger(ruleTriggerSaveReqVO);
                oldIdList.remove(ruleTriggerSaveReqVO.getId());
            }
            //判断是否是定时触发
            if (ConditionTypeEnum.TRIGGER_TYPE.getType().equals(type) && TriggerTypeEnum.TIME_TRIGGER.getType().equals(ruleTriggerSaveReqVO.getTriggerType())) {
                ruleTimeTriggerService.createAndUpdateSceneRuleTimeTrigger(ruleTriggerSaveReqVO.getRuleTimeTriggerSaveReqVO(), ruleId, triggerId);
            }
        }
        //删除
        if (!CollectionUtils.isEmpty(oldIdList)) {
            sceneRuleTriggerMapper.deleteBatchIds(oldIdList);
        }
    }

    @Override
    public List<SceneRuleTriggerDO> getSceneRuleTriggerByRuleId(Long ruleId, Integer conditionType) {
        return sceneRuleTriggerMapper.selectList(new LambdaQueryWrapperX<SceneRuleTriggerDO>().
                eq(SceneRuleTriggerDO::getRuleId, ruleId).eq(SceneRuleTriggerDO::getConditionType, conditionType)
                .orderByAsc(SceneRuleTriggerDO::getSort));
    }

    /**
     * 根据失效状态获取数据
     */
    @Override
    public List<SceneRuleTriggerDO> getSceneRuleTriggerByInvalid(Long ruleId, Integer invalid) {
        return sceneRuleTriggerMapper.selectList(new LambdaQueryWrapperX<SceneRuleTriggerDO>()
                .eq(SceneRuleTriggerDO::getRuleId, ruleId)
                .eq(SceneRuleTriggerDO::getIsInvalid, invalid)
                .orderByAsc(SceneRuleTriggerDO::getSort));
    }

    @Override
    public Long createSceneRuleTrigger(SceneRuleTriggerSaveReqVO createReqVO) {
        // 插入
        SceneRuleTriggerDO sceneRuleTrigger = BeanUtils.toBean(createReqVO, SceneRuleTriggerDO.class);
        sceneRuleTriggerMapper.insert(sceneRuleTrigger);
        // 返回
        return sceneRuleTrigger.getId();
    }

    @Override
    public void updateSceneRuleTrigger(SceneRuleTriggerSaveReqVO updateReqVO) {
        // 校验存在
        validateSceneRuleTriggerExists(updateReqVO.getId());
        // 更新
        SceneRuleTriggerDO updateObj = BeanUtils.toBean(updateReqVO, SceneRuleTriggerDO.class);
        sceneRuleTriggerMapper.updateById(updateObj);
    }

    @Override
    public void deleteSceneRuleTrigger(Long id) {
        // 校验存在
        validateSceneRuleTriggerExists(id);
        // 删除
        sceneRuleTriggerMapper.deleteById(id);
    }

    private void validateSceneRuleTriggerExists(Long id) {
        if (sceneRuleTriggerMapper.selectById(id) == null) {
            throw exception(SCENE_RULE_TRIGGER_NOT_EXISTS);
        }
    }

    @Override
    public SceneRuleTriggerDO getSceneRuleTrigger(Long id) {
        return sceneRuleTriggerMapper.selectById(id);
    }

    @Override
    public PageResult<SceneRuleTriggerDO> getSceneRuleTriggerPage(SceneRuleTriggerPageReqVO pageReqVO) {
        return sceneRuleTriggerMapper.selectPage(pageReqVO);
    }

    //触发条件、限制条件场景删除
    @Override
    public List<Long> deleteSceneRuleTrigger(String productCode, String deviceCode) {

        List<SceneRuleTriggerDO> ruleTriggerDOList = sceneRuleTriggerMapper.selectList(new LambdaQueryWrapperX<SceneRuleTriggerDO>()
                .eqIfPresent(SceneRuleTriggerDO::getProductCode, productCode)
                .eqIfPresent(SceneRuleTriggerDO::getDeviceCode, deviceCode)
                .eq(SceneRuleTriggerDO::getConditionType, ConditionTypeEnum.TRIGGER_TYPE.getType())
                .eq(SceneRuleTriggerDO::getTriggerType, TriggerTypeEnum.DEVICE_TRIGGER.getType())
                .eq(SceneRuleTriggerDO::getIsInvalid, 0));
        List<SceneRuleTriggerDO> ruleConditionDoList = sceneRuleTriggerMapper.selectList(new LambdaQueryWrapperX<SceneRuleTriggerDO>()
                .eqIfPresent(SceneRuleTriggerDO::getProductCode, productCode)
                .eqIfPresent(SceneRuleTriggerDO::getDeviceCode, deviceCode)
                .eq(SceneRuleTriggerDO::getConditionType, ConditionTypeEnum.CONDITION_TYPE.getType())
                .eq(SceneRuleTriggerDO::getIsInvalid, 0));

        List<SceneRuleTriggerDO> ruleTriggerList = new ArrayList<>();
        ruleTriggerList.addAll(ruleTriggerDOList);
        ruleTriggerList.addAll(ruleConditionDoList);

        if (!CollectionUtils.isEmpty(ruleTriggerList)) {
            ruleTriggerList.forEach(item -> item.setIsInvalid(2));
        }
        if (!CollectionUtils.isEmpty(ruleTriggerList)) {
            sceneRuleTriggerMapper.updateBatch(ruleTriggerList);
        }

        return ruleTriggerList.stream()
                .map(SceneRuleTriggerDO::getRuleId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
    }

    //触发条件、限制条件场景失效
    public List<Long> invalidSceneRuleTrigger(Long userId, List<DataPermissionsDO> dataPermissionsList) {
        Map<String, String> dataPermissionsMap = new HashMap<>();
        dataPermissionsList.forEach(item -> {
            dataPermissionsMap.put(item.getDataId(), item.getParentId());
        });
        //如果设备没匹配上，找全部设备（-产品），全部设备没匹配上，找所有产品(-资源空间id),还没匹配上更改状态
        //查询所有设备
        Map<String, Long> devicesMap = new HashMap<>();
        Map<String, String> devicesProductMap = new HashMap<>();
        deviceMapper.selectList().forEach(device -> {
            devicesMap.put(device.getDeviceCode(), device.getId());
            devicesProductMap.put(device.getDeviceCode(), device.getProductCode());
        });
        //查询所有产品
        Map<String, Long> productMap = new HashMap<>();
        Map<String, Long> productSpaceMap = new HashMap<>();
        productMapper.selectList().forEach(product -> {
            productMap.put(product.getProductCode(), product.getId());
            productSpaceMap.put(product.getProductCode(), product.getResourceSpaceId());
        });

        List<SceneRuleTriggerDO> ruleTriggerDOList = sceneRuleTriggerMapper.selectList(new LambdaQueryWrapperX<SceneRuleTriggerDO>()
                .eq(SceneRuleTriggerDO::getCreator, userId)
                .eq(SceneRuleTriggerDO::getConditionType, ConditionTypeEnum.TRIGGER_TYPE.getType())
                .eq(SceneRuleTriggerDO::getTriggerType, TriggerTypeEnum.DEVICE_TRIGGER.getType())
                .eq(SceneRuleTriggerDO::getIsInvalid, 0)
        );
        List<SceneRuleTriggerDO> ruleConditionDoList = sceneRuleTriggerMapper.selectList(new LambdaQueryWrapperX<SceneRuleTriggerDO>()
                .eq(SceneRuleTriggerDO::getCreator, userId)
                .eq(SceneRuleTriggerDO::getConditionType, ConditionTypeEnum.CONDITION_TYPE.getType())
                .eq(SceneRuleTriggerDO::getIsInvalid, 0)
        );

        List<SceneRuleTriggerDO> ruleTriggerList = new ArrayList<>();
        ruleTriggerList.addAll(ruleTriggerDOList);
        ruleTriggerList.addAll(ruleConditionDoList);

        List<SceneRuleTriggerDO> InvalidList = new ArrayList<>();
        //如果设备没匹配上，找全部设备（-产品），全部设备没匹配上，找所有产品(-资源空间id),还没匹配上就更改状态
        if (!CollectionUtils.isEmpty(ruleTriggerList)) {
            ruleTriggerList.forEach(item -> {
                if (devicesMap.containsKey(item.getDeviceCode())) {
                    //获取规则的设备id
                    Long deviceId = devicesMap.get(item.getDeviceCode());
                    //获取产品编码
                    String productCode = devicesProductMap.get(item.getDeviceCode());
                    //通过设备id与权限中的设备id比对
                    if (!dataPermissionsMap.containsKey(String.valueOf(deviceId))) {
                        //如果设备没匹配上，看是否勾选了全部设备（-产品）
                        Long productId = productMap.get(productCode);
                        if (!dataPermissionsMap.containsKey("-" + productId)) {
                            //如果全部设备没匹配上，看是否勾选了所有产品(-资源空间id)
                            Long resourceSpaceId = productSpaceMap.get(productCode);
                            if (!dataPermissionsMap.containsKey("-" + resourceSpaceId)) {
                                item.setIsInvalid(1);
                                InvalidList.add(item);
                            }
                        }
                    }
                }
                //全部设备的情景,匹配产品，产品没匹配上，就匹配所有产品(-资源空间id)
                if ("-1".equals(item.getDeviceCode())) {
                    Long productId = productMap.get(item.getProductCode());
                    if (!dataPermissionsMap.containsKey("-" + productId)) {
                        Long resourceSpaceId = productSpaceMap.get(item.getProductCode());
                        if (!dataPermissionsMap.containsKey("-" + resourceSpaceId)) {
                            item.setIsInvalid(1);
                            InvalidList.add(item);
                        }
                    }
                }
            });
        }
        if (!CollectionUtils.isEmpty(InvalidList)) {
            sceneRuleTriggerMapper.updateBatch(InvalidList);
        }
        return InvalidList.stream()
                .map(SceneRuleTriggerDO::getRuleId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    public List<Long> effectiveSceneRuleTrigger(Long userId, List<DataPermissionsDO> dataPermissionsList) {
        Map<String, String> dataPermissionsMap = new HashMap<>();
        dataPermissionsList.forEach(item -> {
            dataPermissionsMap.put(item.getDataId(), item.getParentId());
        });
        //如果设备没匹配上，找全部设备（-产品），全部设备没匹配上，找所有产品(-资源空间id),还没匹配上更改状态
        //查询所有设备
        Map<String, Long> devicesMap = new HashMap<>();
        Map<String, String> devicesProductMap = new HashMap<>();
        deviceMapper.selectList().forEach(device -> {
            devicesMap.put(device.getDeviceCode(), device.getId());
            devicesProductMap.put(device.getDeviceCode(), device.getProductCode());
        });
        //查询所有产品
        Map<String, Long> productMap = new HashMap<>();
        Map<String, Long> productSpaceMap = new HashMap<>();
        productMapper.selectList().forEach(product -> {
            productMap.put(product.getProductCode(), product.getId());
            productSpaceMap.put(product.getProductCode(), product.getResourceSpaceId());
        });

        List<SceneRuleTriggerDO> ruleTriggerDOList = sceneRuleTriggerMapper.selectList(new LambdaQueryWrapperX<SceneRuleTriggerDO>()
                .eq(SceneRuleTriggerDO::getCreator, userId)
                .eq(SceneRuleTriggerDO::getConditionType, ConditionTypeEnum.TRIGGER_TYPE.getType())
                .eq(SceneRuleTriggerDO::getTriggerType, TriggerTypeEnum.DEVICE_TRIGGER.getType())
                .eq(SceneRuleTriggerDO::getIsInvalid, 1)
        );
        List<SceneRuleTriggerDO> ruleConditionDoList = sceneRuleTriggerMapper.selectList(new LambdaQueryWrapperX<SceneRuleTriggerDO>()
                .eq(SceneRuleTriggerDO::getCreator, userId)
                .eq(SceneRuleTriggerDO::getConditionType, ConditionTypeEnum.CONDITION_TYPE.getType())
                .eq(SceneRuleTriggerDO::getIsInvalid, 1)
        );

        List<SceneRuleTriggerDO> ruleTriggerList = new ArrayList<>();
        ruleTriggerList.addAll(ruleTriggerDOList);
        ruleTriggerList.addAll(ruleConditionDoList);

        List<SceneRuleTriggerDO> InvalidList = new ArrayList<>();
        //如果设备没匹配上，找全部设备（-产品），全部设备没匹配上，找所有产品(-资源空间id),还没匹配上就更改状态
        if (!CollectionUtils.isEmpty(ruleTriggerList)) {
            ruleTriggerList.forEach(item -> {
                if (devicesMap.containsKey(item.getDeviceCode())) {
                    //获取规则的设备id
                    Long deviceId = devicesMap.get(item.getDeviceCode());
                    //获取产品编码
                    String productCode = devicesProductMap.get(item.getDeviceCode());
                    //通过设备id与权限中的设备id比对
                    boolean isValid = dataPermissionsMap.containsKey(String.valueOf(deviceId))
                            || dataPermissionsMap.containsKey("-" + productMap.get(productCode))
                            || dataPermissionsMap.containsKey("-" + productSpaceMap.get(productCode));
                    if (isValid) {
                        item.setIsInvalid(0);
                        InvalidList.add(item);
                    }
                }
                //全部设备的情景,匹配产品，产品没匹配上，就匹配所有产品(-资源空间id)
                if ("-1".equals(item.getDeviceCode())) {
                    Long productId = productMap.get(item.getProductCode());
                    Long resourceSpaceId = productSpaceMap.get(item.getProductCode());

                    if (dataPermissionsMap.containsKey("-" + productId)
                            || dataPermissionsMap.containsKey("-" + resourceSpaceId)) {
                        item.setIsInvalid(0);
                        InvalidList.add(item);
                    }
                }
            });
        }
        if (!CollectionUtils.isEmpty(InvalidList)) {
            sceneRuleTriggerMapper.updateBatch(InvalidList);
        }
        return InvalidList.stream()
                .map(SceneRuleTriggerDO::getRuleId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
    }
}
