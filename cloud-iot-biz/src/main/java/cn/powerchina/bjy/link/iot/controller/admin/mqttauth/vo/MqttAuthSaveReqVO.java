package cn.powerchina.bjy.link.iot.controller.admin.mqttauth.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

@Schema(description = "管理后台 - mqtt认证新增/修改 Request VO")
@Data
public class MqttAuthSaveReqVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "24425")
    private Long id;

    @Schema(description = "用户名", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @NotEmpty(message = "用户名不能为空")
    private String userName;

    @Schema(description = "密钥")
    private String secret;

    @Schema(description = "认证类型(1:应用管理;2:产品管理;3:设备管理;4:edge)", example = "2")
    private Integer type;

    @Schema(description = "状态(1:启用;0:禁用)", example = "2")
    private Integer status;

}