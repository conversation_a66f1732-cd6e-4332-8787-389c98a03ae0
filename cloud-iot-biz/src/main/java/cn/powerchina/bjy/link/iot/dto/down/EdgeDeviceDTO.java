package cn.powerchina.bjy.link.iot.dto.down;

import cn.powerchina.bjy.link.iot.model.MessageToEdge;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 边缘网关-设备信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EdgeDeviceDTO implements MessageToEdge, Serializable {

    /**
     * 设备编码
     */
    private String deviceCode;

    /**
     * MCU通道编码
     */
    private String mcuChannel;

    /**
     * 额外参数
     */
    private String extra;
}
