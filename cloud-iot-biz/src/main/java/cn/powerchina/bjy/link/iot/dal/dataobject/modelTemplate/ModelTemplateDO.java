package cn.powerchina.bjy.link.iot.dal.dataobject.modelTemplate;

import cn.powerchina.bjy.cloud.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

@TableName("iot_model_template")
@KeySequence("iot_model_template_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ModelTemplateDO extends BaseDO {

    /**
     * 主键
     */
    @TableField(value = "id")
    private Long id;
    /**
     * 物模板一级分类ID
     */
    @TableField(value = "categorize_one_id")
    private Long categorizeOneId;

    /**
     * 物模板二级分类ID
     */
    @TableField(value = "categorize_two_id")
    private Long categorizeTwoId;
    /**
     * 物模板名称
     */
    @TableField(value = "template_name")
    private String templateName;
    /**
     * 描述
     */
    @TableField(value = "remark")
    private String remark;
    /**
     * 租户编号
     */
    @TableField(value = "tenant_id")
    private Long tenantId;
    /**
     * 是否删除，默认为0
     */
    @TableField(value = "deleted")
    private Boolean deleted;
}
