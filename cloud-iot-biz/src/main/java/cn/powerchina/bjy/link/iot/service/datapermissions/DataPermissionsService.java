package cn.powerchina.bjy.link.iot.service.datapermissions;

import java.util.*;

import cn.powerchina.bjy.link.iot.controller.admin.datapermissions.vo.DataPermissionsPageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.datapermissions.vo.DataPermissionsSaveReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.datapermissions.vo.DataPermissionsTreeVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.datapermissions.DataPermissionsDO;
import jakarta.validation.*;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;

/**
 * 数据权限 Service 接口
 *
 * <AUTHOR>
 */
public interface DataPermissionsService {

    /**
     * 创建数据权限
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Boolean createDataPermissions(@Valid DataPermissionsSaveReqVO createReqVO);

    /**
     * 更新数据权限
     *
     * @param updateReqVO 更新信息
     */
    Boolean updateDataPermissions(@Valid DataPermissionsSaveReqVO updateReqVO);

    /**
     * 删除数据权限
     *
     * @param id 编号
     */
    void deleteDataPermissions(Long id);

//    /**
//     * 获得数据权限
//     *
//     * @param id 编号
//     * @return 数据权限
//     */
//    DataPermissionsSaveReqVO getDataPermissions();

    /**
     * 获得数据权限分页
     *
     * @param pageReqVO 分页查询
     * @return 数据权限分页
     */
    PageResult<DataPermissionsDO> getDataPermissionsPage(DataPermissionsPageReqVO pageReqVO);

    List<DataPermissionsTreeVO> getAllList();

    DataPermissionsSaveReqVO getAllListByRoleId(String roleId);

}