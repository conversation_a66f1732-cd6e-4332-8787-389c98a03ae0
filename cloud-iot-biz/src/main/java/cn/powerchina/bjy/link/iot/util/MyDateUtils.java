package cn.powerchina.bjy.link.iot.util;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.Calendar;
import java.util.Date;

/**
 * @Description: 描述
 * @Author: yhx
 * @CreateDate: 2024/8/19
 */
public class MyDateUtils {

    public static String getFormatDate(Date date, String format) {
        return new SimpleDateFormat(format).format(date);
    }

    public static Date getParseDate(String date, String format) {
        try {
            return new SimpleDateFormat(format).parse(date);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * localDateTime转date
     *
     * @param localDateTime
     * @return
     */
    public static Date transportLocalDateTimeToDate(LocalDateTime localDateTime, String format) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(localDateTime.getYear(), localDateTime.getMonthValue() - 1, localDateTime.getDayOfMonth(), 0, 0, 0);
        return getParseDate(getFormatDate(calendar.getTime(), format), format);
    }
}
