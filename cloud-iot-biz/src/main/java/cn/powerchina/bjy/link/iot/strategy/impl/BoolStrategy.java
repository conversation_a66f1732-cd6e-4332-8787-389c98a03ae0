package cn.powerchina.bjy.link.iot.strategy.impl;

import cn.powerchina.bjy.link.iot.dto.up.EdgeReadPropertyValue;
import cn.powerchina.bjy.link.iot.strategy.ProductModelStrategy;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * BoolStrategy
 * 布尔类型只校验收到的值为0或者为1
 *
 * <AUTHOR>
 **/
@Component("boolStrategy")
public class BoolStrategy implements ProductModelStrategy {

    private final static int ZERO = 0;
    private final static int ONE = 1;

    @Override
    public boolean shouldIgnore(Object context, String strategy) {
        if (StringUtils.isBlank(strategy) || null == context) {
            return false;
        }
        int data = (int) context;
        return data != ZERO && data != ONE;
    }

    @Override
    public boolean shouldIgnore(String context, String strategy, EdgeReadPropertyValue.EdgeDevicePropertyValueDTO valueDTO) {
        if (StringUtils.isBlank(strategy) || null == context) {
            return false;
        }
        int data = Integer.parseInt(context);
        return data != ZERO && data != ONE;
    }
}
