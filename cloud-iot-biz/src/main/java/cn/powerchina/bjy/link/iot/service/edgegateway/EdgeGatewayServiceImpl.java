package cn.powerchina.bjy.link.iot.service.edgegateway;

import cn.hutool.core.util.ObjectUtil;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.collection.CollectionUtils;
import cn.powerchina.bjy.cloud.framework.common.util.json.JsonUtils;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.cloud.framework.web.core.util.WebFrameworkUtils;
import cn.powerchina.bjy.link.iot.aop.gateway.EdgeGatewayPermissionCheck;
import cn.powerchina.bjy.link.iot.controller.admin.edgegateway.bo.EdgeExtraBO;
import cn.powerchina.bjy.link.iot.controller.admin.edgegateway.bo.EdgeGatewayBO;
import cn.powerchina.bjy.link.iot.controller.admin.edgegateway.vo.*;
import cn.powerchina.bjy.link.iot.controller.admin.mqttauth.vo.MqttAuthSaveReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.edgegateway.vo.*;
import cn.powerchina.bjy.link.iot.dal.dataobject.device.DeviceDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.edgechannel.EdgeChannelDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.edgegateway.EdgeGatewayDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.edgegateway.EdgeGatewayDeviceDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.product.ProductDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.resourcespace.ResourceSpaceDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.transporttarget.TransportTargetDO;
import cn.powerchina.bjy.link.iot.dal.mysql.device.DeviceMapper;
import cn.powerchina.bjy.link.iot.dal.mysql.edgechannel.EdgeChannelMapper;
import cn.powerchina.bjy.link.iot.dal.mysql.edgegateway.EdgeGatewayMapper;
import cn.powerchina.bjy.link.iot.dal.mysql.product.ProductMapper;
import cn.powerchina.bjy.link.iot.dal.mysql.transporttarget.TransportTargetMapper;
import cn.powerchina.bjy.link.iot.dto.down.*;
import cn.powerchina.bjy.link.iot.enums.*;
import cn.powerchina.bjy.link.iot.model.DeviceCheckOnlineModel;
import cn.powerchina.bjy.link.iot.mq.MqttPublisher;
import cn.powerchina.bjy.link.iot.mq.RocketMQv5Client;
import cn.powerchina.bjy.link.iot.service.mqttauth.MqttAuthService;
import cn.powerchina.bjy.link.iot.service.resourcespace.ResourceSpaceService;
import cn.powerchina.bjy.link.iot.util.CodeGenerator;
import com.alibaba.druid.sql.ast.DistributedByType;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.client.RestTemplate;

import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.link.iot.enums.ErrorCodeConstants.*;

/**
 * 边缘网关 Service 实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Validated
public class EdgeGatewayServiceImpl implements EdgeGatewayService {
    @Value("${edge.broker.url:}")
    private String videoConvertUrl;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    private EdgeGatewayMapper edgeGatewayMapper;

    @Resource
    private EdgeChannelMapper edgeChannelMapper;

    @Resource
    private DeviceMapper deviceMapper;

    @Autowired
    private ProductMapper productMapper;

    @Resource
    private RocketMQv5Client rocketMQv5Client;

    @Autowired
    private ResourceSpaceService resourceSpaceService;

    @Autowired
    private MqttAuthService mqttAuthService;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private MqttPublisher mqttPublisher;

    @Resource
    private TransportTargetMapper transportTargetMapper;


    @Override
    public Long createEdgeGateway(EdgeGatewaySaveReqVO createReqVO) {

        RLock rLock = redissonClient.getLock(RedisLockKeyConstant.PRODUCT_DEVICE_LIMIT_KEY_PREFIX+createReqVO.getProductCode());
        rLock.lock();

        try {
            // 校验唯一标识符是否已经存在
            LambdaQueryWrapperX<DeviceDO> wrapperX = new LambdaQueryWrapperX<>();
            wrapperX.eq(DeviceDO::getDeviceSerial, createReqVO.getSerial());
            if (deviceMapper.selectCount(wrapperX) > 0) {
                throw exception(DEVICE_SERIAL_EXISTS);
            }

            // 获取产品信息
            ProductDO productDO = productMapper.selectByCode(createReqVO.getProductCode());

            // 产品限额-已使用额度>0代表还可以创建设备；否则就是不能再创建设备
            if (productDO.getDeviceLimit() - productDO.getUsedQuota() > 0) {
                // edge扩展属性
                EdgeExtraBO edgeExtraBO = new EdgeExtraBO();
                edgeExtraBO.setEdgeHost("");
                edgeExtraBO.setEdgePort(null);

                // 组装要保存的设备信息
                DeviceDO deviceDO = new DeviceDO();
                deviceDO.setProductCode(createReqVO.getProductCode());
                deviceDO.setDeviceName(createReqVO.getEdgeName());
                deviceDO.setDeviceCode(CodeGenerator.createCode(SceneTypeEnum.DEVICE.getPrefix()));
                deviceDO.setDeviceSerial(createReqVO.getSerial());
                deviceDO.setDistributeState(DistributeStateEnum.UNSENT.getType());
                deviceDO.setLinkState(LinkStateEnum.NO_ACTIVE.getType());
                deviceDO.setExtra(JsonUtils.toJsonString(edgeExtraBO));
                deviceDO.setEdgeCode(deviceDO.getDeviceCode());
                deviceDO.setRemark(createReqVO.getDescription());
                deviceDO.setDeviceSecret(UUID.randomUUID().toString().replaceAll("-", ""));
                deviceDO.setDeviceType(DeviceTypeEnum.EDGE.getType());
                deviceDO.setNodeType(productDO.getNodeType());

                // 查询是否有已删除设备。唯一标识一样且已删除时,设备code重新启用赋值给新增的设备
                String deviceCode = deviceMapper.selectCodeIsDelBySerial(createReqVO.getSerial());
                if (!StringUtils.isBlank(deviceCode)) {
                    deviceDO.setDeviceCode(deviceCode);
                }

                deviceDO.setDeleted(false);
                deviceDO.setCreator(WebFrameworkUtils.getLoginUserId()+"");
                deviceDO.setCreateTime(LocalDateTime.now());
                deviceDO.setUpdater(WebFrameworkUtils.getLoginUserId()+"");
                deviceDO.setUpdateTime(LocalDateTime.now());
                deviceDO.setStatus(AuthStatusEnum.ENABLE.getType());


                // 保存设备信息
                deviceMapper.saveDevice(deviceDO);

                // 更新产品已使用额度
                productDO.setUsedQuota(productDO.getUsedQuota() + 1);
                productMapper.updateById(productDO);

                // 保存认证信息
                MqttAuthSaveReqVO mqttAuthSaveReqVO = new MqttAuthSaveReqVO();
                mqttAuthSaveReqVO.setUserName(deviceDO.getDeviceCode());
                mqttAuthSaveReqVO.setSecret(deviceDO.getDeviceSecret());
                mqttAuthSaveReqVO.setType(AuthTypeEnum.EDGE_TYPE.getType());
                if (Objects.equals(productDO.getDynamicRegister(), DynamicRegisterEnum.YES.getType())) {
                    mqttAuthSaveReqVO.setStatus(MqttAuthStatusEnum.DISABLE.getType());
                } else {
                    mqttAuthSaveReqVO.setStatus(MqttAuthStatusEnum.ENABLE.getType());
                }
                mqttAuthService.createMqttAuth(mqttAuthSaveReqVO);

                // 返回
                return deviceDO.getId();
            } else {
                throw exception(PRODUCT_USED_QUOTA_MORE_DEVICE_LIMIT);
            }
        } finally {
            rLock.unlock();
        }
    }

    @Override
    public void updateEdgeGateway(EdgeGatewaySaveReqVO updateReqVO) {
        // 校验存在
        validateEdgeGatewayExists(updateReqVO.getId());
        // 更新
        EdgeGatewayDO updateObj = BeanUtils.toBean(updateReqVO, EdgeGatewayDO.class);
        edgeGatewayMapper.updateById(updateObj);
    }

    @Transactional
    @Override
    public void deleteEdgeGateway(Long id) {
        // 校验存在
        validateEdgeGatewayExists(id);
        // 校验网关下是否存在设备
        validateDeviceExists(id);

        DeviceDO deviceDO = deviceMapper.selectById(id);

        // 删除
        deviceMapper.deleteById(id);
        // 删除mqtt认证
        mqttAuthService.deleteByUserName(deviceDO.getDeviceCode());
    }

    private void validateEdgeGatewayExists(Long id) {
        if (deviceMapper.selectById(id) == null) {
            throw exception(EDGE_GATEWAY_NOT_EXISTS);
        }
    }

    private void validateDeviceExists(Long id) {
        DeviceDO deviceDO = deviceMapper.selectById(id);
        if (deviceMapper.selectCountByEdgeCode(deviceDO.getEdgeCode()) > 1) {
            throw exception(EDGE_GATEWAY_DEVICE_EXISTS);
        }
    }

    @Override
    public EdgeGatewayDO getEdgeGateway(Long id) {
        return Objects.isNull(id) ? null : edgeGatewayMapper.selectById(id);
    }

    @EdgeGatewayPermissionCheck
    @Override
    public PageResult<EdgeGatewayBO> getEdgeGatewayPage(EdgeGatewayPageReqVO pageReqVO) {

        PageResult<EdgeGatewayBO> boPageResult = deviceMapper.listEdge(pageReqVO);

        if (Objects.nonNull(boPageResult) && !org.springframework.util.CollectionUtils.isEmpty(boPageResult.getList())) {
            for (EdgeGatewayBO gatewayBO : boPageResult.getList()) {
                EdgeExtraBO edgeExtraBO = Optional.ofNullable(JsonUtils.parseObject(gatewayBO.getExtra(), EdgeExtraBO.class)).orElse(new EdgeExtraBO());

                gatewayBO.setEdgeServiceName("cloud-link-edge_" + gatewayBO.getEdgeCode());
                gatewayBO.setEdgeHost(edgeExtraBO.getEdgeHost());
                gatewayBO.setEdgePort(edgeExtraBO.getEdgePort());
                setSpaceName(gatewayBO);
            }
        }
        return boPageResult;
    }

    /**
     * 获得边缘实例下一级的设备
     * @param pageReqVO 分页查询条件
     * @return 边缘实例下一级的设备
     */
    @Override
    public PageResult<EdgeDevicePageResVO> getEdgeDevicePage(EdgeDevicePageReqVO pageReqVO) {
        PageResult<EdgeDevicePageResVO> pageResult = deviceMapper.listEdgeDevice(pageReqVO);

        DeviceDO tempDeviceDO = deviceMapper.selectByCode(pageReqVO.getEdgeCode());

        pageResult.getList().forEach(deviceDO -> {
            deviceDO.setEdgeName(tempDeviceDO.getDeviceName());
            deviceDO.setEdgeCode(tempDeviceDO.getDeviceCode());
            deviceDO.setNodeTypeCN(NodeTypeEnum.getDescByType(deviceDO.getNodeType()));
            deviceDO.setLinkStateCN(LinkStateEnum.getDescByType(deviceDO.getLinkState()));
        });
        return pageResult;
    }

    /**
     * 获得边缘实例下一级的设备的子设备
     * @param pageReqVO 分页查询条件
     * @return 边缘实例下一级的设备的子设备
     */
    @Override
    public PageResult<EdgeChildDevicePageResVO> getEdgeChildDevicePage(EdgeChildDevicePageReqVO pageReqVO) {
        PageResult<EdgeChildDevicePageResVO> pageResult = deviceMapper.listEdgeChildDevice(pageReqVO);
        pageResult.getList().forEach(deviceDO -> {
            deviceDO.setNodeTypeCN(NodeTypeEnum.getDescByType(deviceDO.getNodeType()));
            deviceDO.setLinkStateCN(LinkStateEnum.getDescByType(deviceDO.getLinkState()));
        });
        return pageResult;
    }

    /**
     * 设置资源空间名称
     *
     * @param bO
     */
    private void setSpaceName(EdgeGatewayBO bO) {
        if (Objects.nonNull(bO) && Objects.nonNull(bO.getResourceSpaceId())) {
            if (Objects.equals(bO.getResourceSpaceId(), 0L)) {
                bO.setSpaceName("全部");
            } else {
                ResourceSpaceDO spaceDO = resourceSpaceService.getResourceSpace(bO.getResourceSpaceId());
                bO.setSpaceName(Objects.isNull(spaceDO) ? null : spaceDO.getSpaceName());
            }
        }
    }

    @Override
    public PageResult<EdgeGatewayDeviceDO> getEdgeGatewayDevicePage(EdgeGatewayPageReqVO pageReqVO) {
        return edgeGatewayMapper.selectJoinPage(pageReqVO);
    }


    @Override
    public String getEdgeServiceNameByCode(String edgeCode) {
        LambdaQueryWrapperX<EdgeGatewayDO> wrapperX = new LambdaQueryWrapperX<>();
        wrapperX.select(EdgeGatewayDO::getEdgeServiceName)
                .eq(EdgeGatewayDO::getEdgeCode, edgeCode);
        EdgeGatewayDO edgeGatewayDO = edgeGatewayMapper.selectOne(wrapperX);
        if (ObjectUtil.isNotNull(edgeGatewayDO)) {
            return edgeGatewayDO.getEdgeServiceName();
        } else {
            throw exception(EDGE_GATEWAY_NOT_EXISTS);
        }
    }

    @Override
    public void receiveRegister(EdgeSyncUpDTO edgeSyncUpDTO) {
        EdgeGatewayDO gatewayDO = edgeGatewayMapper.selectByEdgeCode(edgeSyncUpDTO.getNode());
        //如果是心跳，设置心跳时间
        if (Objects.nonNull(gatewayDO) && Objects.equals(edgeSyncUpDTO.getSyncType(), EdgeSyncTypeEnum.HEART.getType())) {
            gatewayDO.setHeartTime(LocalDateTime.now());
        }
        //如果是断开，更新状态
        else if (Objects.nonNull(gatewayDO) && Objects.equals(edgeSyncUpDTO.getSyncType(), EdgeSyncTypeEnum.OFF.getType())) {
            gatewayDO.setEdgeStatus(OnlineStatusEnum.OFFLINE.getType());
        }
        //如果是上线，更新信息
        else {
            gatewayDO = buildEdgeGatewayDO(edgeSyncUpDTO, Objects.nonNull(gatewayDO) ? gatewayDO.getId() : null);
        }
        int result;
        if (Objects.nonNull(gatewayDO.getId())) {
            result = edgeGatewayMapper.updateById(gatewayDO);
        } else {
            result = edgeGatewayMapper.insert(gatewayDO);
        }
        //如果是断开，需要设置当前边缘实例下的设备下线
        if (result > 0 && Objects.equals(edgeSyncUpDTO.getSyncType(), EdgeSyncTypeEnum.OFF.getType())) {
            deviceMapper.updateLinkStateByEdgeCode(gatewayDO.getEdgeCode(), OnlineStatusEnum.OFFLINE.getType());
        }
    }

    @Override
    public void edgeOnlineCheck(String edgeCode) {
        //查询边缘网关的设备在线状态，必须传网关code
        if (StringUtils.isBlank(edgeCode)) {
            return;
        }
        //查询在线边缘网关
        QueryWrapper<EdgeGatewayDO> queryWrapperEdge = new QueryWrapper<>();
        queryWrapperEdge.lambda().eq(EdgeGatewayDO::getDeleted, Boolean.FALSE)
                .eq(EdgeGatewayDO::getEdgeStatus, OnlineStatusEnum.ONLINE.getType());
        if (StringUtils.isNotBlank(edgeCode)) {
            queryWrapperEdge.lambda().eq(EdgeGatewayDO::getEdgeCode, edgeCode);
        }
        List<EdgeGatewayDO> edgeGatewayDOS = edgeGatewayMapper.selectList(queryWrapperEdge);
        if (!org.springframework.util.CollectionUtils.isEmpty(edgeGatewayDOS)) {
            for (EdgeGatewayDO edgeGatewayDO : edgeGatewayDOS) {
                //查询网关设备
                QueryWrapper<DeviceDO> queryWrapperDevice = new QueryWrapper<>();
                queryWrapperDevice.lambda().eq(DeviceDO::getDeleted, Boolean.FALSE)
                        .eq(DeviceDO::getEdgeCode, edgeGatewayDO.getEdgeCode())
                        .eq(DeviceDO::getNodeType, NodeTypeEnum.EDGE.getType());
                List<DeviceDO> deviceDOS = deviceMapper.selectList(queryWrapperDevice);
                if (!org.springframework.util.CollectionUtils.isEmpty(deviceDOS)) {
                    for (DeviceDO deviceDO : deviceDOS) {
                        //查询网关子设备
                        QueryWrapper<DeviceDO> queryWrapperDeviceSub = new QueryWrapper<>();
                        queryWrapperDeviceSub.lambda().eq(DeviceDO::getDeleted, Boolean.FALSE)
                                .eq(DeviceDO::getEdgeCode, edgeGatewayDO.getEdgeCode())
                                .eq(DeviceDO::getParentCode, deviceDO.getDeviceCode());
                        List<DeviceDO> deviceDOSSub = deviceMapper.selectList(queryWrapperDeviceSub);
                        // 转换为边缘网关需要的格式
                        EdgeOnlineCheckDTO edgeOnlineCheckDTO = convertToEdgeOnlineCheckDTO(deviceDO, deviceDOSSub);
                        if (log.isDebugEnabled()) {
                            log.debug("消息内容为【{}】", JsonUtils.toJsonString(edgeOnlineCheckDTO));
                        }
                        edgeOnlineCheckDTO.setCurrentTime(System.currentTimeMillis());
                        rocketMQv5Client.syncSendFifoMessage(IotTopicConstant.TOPIC_DEVICE_ONLINE, edgeOnlineCheckDTO, IotTopicConstant.GROUP_DEVICE_ONLINE);
                    }
                }
            }
        }
    }

    private Map<String, List<EdgeGatewayDeviceDO>> fillEdgeDeviceMap(List<EdgeGatewayDeviceDO> deviceDOList, Predicate<EdgeGatewayDeviceDO> predicate) {
        Map<String, List<EdgeGatewayDeviceDO>> gwDeviceMap = new HashMap<>(deviceDOList.size());
        deviceDOList.stream().filter(predicate).forEach(item -> {
            // 网关设备
            if (StringUtils.isBlank(item.getParentCode())) {
                if (gwDeviceMap.containsKey(item.getDeviceCode())) {
                    log.warn("设备号重复，deviceCode【{}】", item.getDeviceCode());
                } else {
                    List<EdgeGatewayDeviceDO> deviceDOS = new ArrayList<>();
                    deviceDOS.add(item);
                    gwDeviceMap.put(item.getDeviceCode(), deviceDOS);
                }
            } else {
                // 子设备
                if (gwDeviceMap.containsKey(item.getParentCode())) {
                    gwDeviceMap.get(item.getParentCode()).add(item);
                } else {
                    gwDeviceMap.put(item.getParentCode(), Lists.newArrayList(item));
                }
            }
        });
        return gwDeviceMap;
    }

    private List<EdgeOnlineCheckDTO> convertToEdgeOnlineCheckDTO(Map<String, List<EdgeGatewayDeviceDO>> gwDeviceMap, Map<String, List<EdgeGatewayDeviceDO>> subDeviceMap) {
        List<EdgeOnlineCheckDTO> result = new ArrayList<>();
        if (null == gwDeviceMap) {
            return result;
        }
        gwDeviceMap.forEach((k, v) -> {
            EdgeOnlineCheckDTO edgeOnlineCheckDTO = new EdgeOnlineCheckDTO();
            EdgeMcuDTO edgeMcuDTO = BeanUtils.toBean(v.get(0), EdgeMcuDTO.class);

            if (StringUtils.isNotBlank(v.get(0).getChannelCode())) {
                EdgeChannelDO channelDO = edgeChannelMapper.selectByCode(v.get(0).getChannelCode());
                if (channelDO != null) {
                    edgeMcuDTO.setExtra(channelDO.getExtra());
                }
            }
            edgeOnlineCheckDTO.setMcuDTO(edgeMcuDTO);
            edgeOnlineCheckDTO.setDeviceDTOList(buildEdgeDeviceDTO(subDeviceMap.get(k)));
            result.add(edgeOnlineCheckDTO);
        });

        return result;

    }

    /**
     * 转换为网关需要的格式
     *
     * @param wgDeviceDo
     * @param subDeviceDos
     * @return
     */
    private EdgeOnlineCheckDTO convertToEdgeOnlineCheckDTO(DeviceDO wgDeviceDo, List<DeviceDO> subDeviceDos) {
        EdgeOnlineCheckDTO edgeOnlineCheckDTO = new EdgeOnlineCheckDTO();
        EdgeMcuDTO edgeMcuDTO = BeanUtils.toBean(wgDeviceDo, EdgeMcuDTO.class);
        if (StringUtils.isNotBlank(wgDeviceDo.getChannelCode())) {
            EdgeChannelDO channelDO = edgeChannelMapper.selectByCode(wgDeviceDo.getChannelCode());
            if (channelDO != null) {
                edgeMcuDTO.setExtra(channelDO.getExtra());
                edgeMcuDTO.setConnectType(channelDO.getConnectType());
            }
        }
        edgeOnlineCheckDTO.setMcuDTO(edgeMcuDTO);
        edgeOnlineCheckDTO.setDeviceDTOList(buildEdgeDeviceCheckStatusDTO(subDeviceDos));
        return edgeOnlineCheckDTO;
    }

    /**
     * 组装边缘网关数据格式
     *
     * @param edgeSyncUpDTO
     * @param id
     * @return
     */
    private EdgeGatewayDO buildEdgeGatewayDO(EdgeSyncUpDTO edgeSyncUpDTO, Long id) {
        return EdgeGatewayDO.builder()
                .id(id)
                .edgeCode(edgeSyncUpDTO.getNode())
                .edgeName(edgeSyncUpDTO.getName())
                .edgeServiceName(edgeSyncUpDTO.getService())
                .edgeHost(edgeSyncUpDTO.getHost())
                .edgePort(edgeSyncUpDTO.getPort())
                .description(edgeSyncUpDTO.getRemark())
                .edgeIdentifier(edgeSyncUpDTO.getMacAddr())
                .edgeStatus(OnlineStatusEnum.ONLINE.getType())
                .onlineTime(LocalDateTime.now())
                .heartTime(LocalDateTime.now())
                .resourceSpaceId(edgeSyncUpDTO.getResourceSpaceId())
                .build();
    }

    /**
     * 修改网关实例在线状态
     *
     * @param edgeCode
     * @param edgeStatus
     * @param changeTime
     * @return
     */
    @Override
    public Integer modifyEdgeGateWayEdgeStatus(String edgeCode, Integer edgeStatus, Date changeTime) {
        int result;
        if ((result = edgeGatewayMapper.updateEdgeGateWayEdgeStatus(edgeCode, edgeStatus, changeTime)) > 0) {
            //修改当前边缘实例下的网关设备和网关子设备下线
            deviceMapper.updateLinkStateByEdgeCode(edgeCode, edgeStatus);
            // 通知大坝设备在线离线
            List<DeviceDO> deviceDOS = deviceMapper.selectList(new LambdaQueryWrapperX<DeviceDO>().eq(DeviceDO::getEdgeCode, edgeCode));
            try {
                if (!org.springframework.util.CollectionUtils.isEmpty(deviceDOS)) {
                    //网关设备
                    List<DeviceDO> edgeDeviceList = deviceDOS.stream().filter(item -> NodeTypeEnum.EDGE.getType().equals(item.getNodeType())).toList();
                    if (!org.springframework.util.CollectionUtils.isEmpty(edgeDeviceList)) {
                        edgeDeviceList.forEach(item -> {
                            //子设备状态
                            List<DeviceCheckOnlineModel.ChildDevice> childDeviceList = new ArrayList<>();
                            List<DeviceDO> subDeviceList = deviceDOS.stream().filter(deviceDO -> item.getDeviceCode().equals(deviceDO.getParentCode())).toList();
                            subDeviceList.forEach(subDevice -> {
                                DeviceCheckOnlineModel.ChildDevice childDevice = new DeviceCheckOnlineModel.ChildDevice();
                                childDevice.setCheckResult(edgeStatus);
                                childDevice.setDeviceCode(subDevice.getDeviceCode());
                                childDeviceList.add(childDevice);
                            });

                            DeviceCheckOnlineModel deviceCheckOnlineModel = new DeviceCheckOnlineModel();
                            deviceCheckOnlineModel.setCheckResult(edgeStatus);
                            deviceCheckOnlineModel.setDeviceCode(item.getDeviceCode());
                            deviceCheckOnlineModel.setChildDeviceStatus(childDeviceList);
                            rocketMQv5Client.syncSendFifoMessage(IotTopicConstant.TOPIC_DEVICE_STATUS_CHANGE, deviceCheckOnlineModel, IotTopicConstant.GROUP_DEVICE_STATUS_CHANGE);
                        });
                    }
                }
            } catch (Exception e) {
                log.error("send dam change device status error {}", e.getMessage());
            }
        }
        return result;
    }

    @Override
    public EdgeGatewayBO getEdgeGatewayBO(Long id) {
        DeviceDO deviceDO = deviceMapper.selectById(id);
        ProductDO productDO = productMapper.selectByCode(deviceDO.getProductCode());

        EdgeExtraBO edgeExtraBO = JsonUtils.parseObject(deviceDO.getExtra(), EdgeExtraBO.class);

        EdgeGatewayBO gatewayBO = new EdgeGatewayBO();
        gatewayBO.setId(deviceDO.getId());
        gatewayBO.setEdgeCode(deviceDO.getDeviceCode());
        gatewayBO.setEdgeName(deviceDO.getDeviceName());
        gatewayBO.setEdgeServiceName("cloud-link-edge_"+deviceDO.getDeviceCode());
        gatewayBO.setEdgeHost(edgeExtraBO.getEdgeHost());
        gatewayBO.setEdgePort(edgeExtraBO.getEdgePort());
        gatewayBO.setDescription(deviceDO.getRemark());
        gatewayBO.setCreateTime(deviceDO.getRegisterTime());
        gatewayBO.setEdgeIdentifier(deviceDO.getDeviceSerial());
        gatewayBO.setEdgeStatus(deviceDO.getLinkState());
        gatewayBO.setOnlineTime(deviceDO.getLastUpTime());
        gatewayBO.setResourceSpaceId(productDO.getResourceSpaceId());
        setSpaceName(gatewayBO);
        gatewayBO.setDeviceSecret(deviceDO.getDeviceSecret());
        gatewayBO.setNodeType(deviceDO.getNodeType());
        gatewayBO.setNodeTypeCN(NodeTypeEnum.getDescByType(deviceDO.getNodeType()));
        gatewayBO.setActiveTime(deviceDO.getRegisterTime());
        gatewayBO.setProductId(productDO.getId());
        gatewayBO.setProductName(productDO.getProductName());
        return gatewayBO;
    }

    @Override
    public void detectEdgeGatewayOnlineStatus() {
        //5分钟没心跳则离线
        Date heartTimeNormal = DateUtils.addMinutes(new Date(), -5);
        List<EdgeGatewayDO> gatewayDOList = edgeGatewayMapper.selectList(new LambdaQueryWrapperX<EdgeGatewayDO>()
                .eq(EdgeGatewayDO::getEdgeStatus, OnlineStatusEnum.ONLINE.getType())
                .lt(EdgeGatewayDO::getHeartTime, heartTimeNormal));
        if (!org.springframework.util.CollectionUtils.isEmpty(gatewayDOList)) {
            gatewayDOList.forEach(item -> {
                modifyEdgeGateWayEdgeStatus(item.getEdgeCode(), OnlineStatusEnum.OFFLINE.getType(), new Date());
            });
        }
    }

    @Override
    public void processCameraMove(CameraMoveReqVO moveReqVO) {
        // 1. 验证设备存在
        DeviceDO device = deviceMapper.selectById(moveReqVO.getDeviceId());
        if (device == null) {
            throw exception(DEVICE_NOT_EXISTS);
        }
        // 2. 构建边缘指令DTO
        PtzCommandVO ptzCommandVO = new PtzCommandVO();
        ptzCommandVO.setDeviceId(moveReqVO.getDeviceId());
        ptzCommandVO.setDeviceName(device.getDeviceName());
        ptzCommandVO.setDeviceSerial(device.getDeviceSerial());
        ptzCommandVO.setCommand("ptz_move");

        // 3. 设置指令参数
        Map<String, Object> params = new HashMap<>();
        params.put("direction", moveReqVO.getCommand());
        params.put("speed", moveReqVO.getSpeed());
        ptzCommandVO.setParams(params);

        // 4. 发送MQTT指令
        try {
            String messageId = UUID.randomUUID().toString();
            String jsonValue = JSON.toJSONString(ptzCommandVO);

            //发送MQTT消息
            boolean mqttFlag = Boolean.TRUE.equals(
                    redisTemplate.opsForValue().setIfAbsent(messageId + "_" +"/iot/sys/move/camera", jsonValue, 1, TimeUnit.MINUTES));

            if (mqttFlag) {
                String lockKey = "move:camera:lock";
                RLock lock = redissonClient.getLock(lockKey);
                try {
                    mqttPublisher.publish("/iot/sys/move/camera", jsonValue);
                    lock.lock();
                    // 可添加指令发送统计
                } catch (Exception e) {
                    log.error("摄像头移动指令MQTT发送失败: {}", e.getMessage());
                } finally {
                    lock.unlock();
                }
            }
        } catch (Exception e) {
            log.error("摄像头移动指令处理失败: {}", e.getMessage());
        }
    }

    public String convertVideo(ConvertVideoReqVO convertVideoReqVO) throws  Exception{
        String httpFlvUrl =null ;
        // 1. 验证设备存在
        DeviceDO device = deviceMapper.selectById(convertVideoReqVO.getDeviceId());
        if (device == null) {
            throw exception(DEVICE_NOT_EXISTS);
        }

        // 2. 构建请求参数
        Map<String, String> params = new HashMap<>();
        params.put("deviceId", convertVideoReqVO.getDeviceId());
        params.put("deviceSerial", convertVideoReqVO.getDeviceSerial());
        // 3. 发送HTTP请求
        RestTemplate restTemplate = new RestTemplate();
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<Map<String, String>> requestEntity = new HttpEntity<>(params, headers);
            log.info("视频转换接口调用地址，响应: {}",videoConvertUrl+"/edge-gateway/convert-video");
            ResponseEntity<String> response = restTemplate.postForEntity(
                    videoConvertUrl+"/edge-gateway/convert-video",
                    requestEntity,
                    String.class
            );
            log.info("视频转换接口调用成功，响应: {}", response.getBody());
            if (response.getStatusCode().is2xxSuccessful()) {
                // 解析JSON响应，提取data字段
                JSONObject jsonResponse = JSON.parseObject(response.getBody());
                return jsonResponse.getString("data"); // 返回data字段中的URL
            } else {
                log.error("视频转换接口调用失败，状态码: {}", response.getStatusCodeValue());
                throw new Exception("视频转换失败");
            }
        } catch (Exception e) {
            log.error("视频转换接口调用异常: {}", e.getMessage());
            throw new Exception("视频转换失败");
        }
    }

    @Override
    public void closeVideo(String videoName) {
        RestTemplate restTemplate = new RestTemplate();
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<String> requestEntity = new HttpEntity<>(headers);
            log.info("视频关闭接口调用地址: {}",videoConvertUrl+"/edge-gateway/close-video?videoName="+videoName);
            ResponseEntity<String> response = restTemplate.getForEntity(
                    videoConvertUrl+"/edge-gateway/close-video?videoName="+videoName,
                    String.class
            );
        } catch (Exception e) {
            log.error("视频关闭接口调用异常: {}", e.getMessage());
        }
    }

    @Override
    public void keepAliveVideo(String videoName) {
        RestTemplate restTemplate = new RestTemplate();
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<String> requestEntity = new HttpEntity<>(headers);
            log.info("视频保活接口调用地址: {}",videoConvertUrl+"/edge-gateway/keepAlive-video?videoName="+videoName);
            ResponseEntity<String> response = restTemplate.getForEntity(
                    videoConvertUrl+"/edge-gateway/keepAlive-video?videoName="+videoName,
                    String.class
            );
        } catch (Exception e) {
            log.error("视频保活接口调用异常: {}", e.getMessage());
        }
    }

    /**
     * 根据条件将设备进行映射
     *
     * @param deviceDOList 设备集合
     * @param predicate    断言
     * @return <deviceCode, List<DeviceDO>> -- deviceCode为网关设备编码，List为设备集合（网关设备时list.size为1）
     */
    private Map<String, List<DeviceDO>> fillDeviceMap(List<DeviceDO> deviceDOList, Predicate<DeviceDO> predicate) {
        Map<String, List<DeviceDO>> gwDeviceMap = new HashMap<>(deviceDOList.size());
        deviceDOList.stream().filter(predicate).forEach(item -> {
            // 网关设备
            if (StringUtils.isBlank(item.getParentCode())) {
                if (gwDeviceMap.containsKey(item.getDeviceCode())) {
                    log.warn("设备号重复，deviceCode【{}】", item.getDeviceCode());
                } else {
                    List<DeviceDO> deviceDOS = new ArrayList<>();
                    deviceDOS.add(item);
                    gwDeviceMap.put(item.getDeviceCode(), deviceDOS);
                }
            } else {
                // 子设备
                if (gwDeviceMap.containsKey(item.getParentCode())) {
                    gwDeviceMap.get(item.getParentCode()).add(item);
                } else {
                    gwDeviceMap.put(item.getParentCode(), Lists.newArrayList(item));
                }
            }
        });
        return gwDeviceMap;
    }

    /*
    private List<EdgeSyncDownDTO> convertToEdgeSyncDownDTO(Map<String, List<DeviceDO>> gwDeviceMap,
                                                           Map<String, List<DeviceDO>> subDeviceMap) {
        List<EdgeSyncDownDTO> result = new ArrayList<>();
        // 网关已注册，但子设备存在未注册情况
        if ((null == gwDeviceMap || gwDeviceMap.isEmpty()) && subDeviceMap != null && !subDeviceMap.isEmpty()) {
            subDeviceMap.forEach((k, v) -> {
                DeviceDO gwDevice = deviceMapper.selectByCode(k);
                if (null == gwDevice) {
                    return;
                }
                // gwDeviceMap不会为null
                gwDeviceMap.put(k, Lists.newArrayList(gwDevice));
            });
        }
        if (null == gwDeviceMap) {
            return result;
        }
        gwDeviceMap.forEach((k, v) -> {
            EdgeSyncDownDTO syncDownDTO = new EdgeSyncDownDTO();
            syncDownDTO.setMcuDTO(edgeMcuDTO(v.get(0)));
            syncDownDTO.setDeviceDTOList(edgeDeviceDTO(subDeviceMap.get(k)));

            result.add(syncDownDTO);
        });

        return result;

    }*/

    private List<EdgeDeviceDTO> buildEdgeDeviceDTO(List<EdgeGatewayDeviceDO> deviceDOList) {
        List<EdgeDeviceDTO> result = new ArrayList<>();

        if (CollectionUtils.isAnyEmpty(deviceDOList)) {
            return result;
        }
        deviceDOList.forEach(item -> {
            EdgeDeviceDTO deviceDTO = new EdgeDeviceDTO();
            deviceDTO.setDeviceCode(item.getDeviceCode());
            deviceDTO.setMcuChannel(item.getMcuChannel());
            deviceDTO.setExtra(item.getExtra());
            result.add(deviceDTO);
        });

        return result;
    }

    /**
     * 构建边缘网关设备列表
     *
     * @param deviceDOList
     * @return
     */
    private List<EdgeDeviceDTO> buildEdgeDeviceCheckStatusDTO(List<DeviceDO> deviceDOList) {
        List<EdgeDeviceDTO> result = new ArrayList<>();
        if (CollectionUtils.isAnyEmpty(deviceDOList)) {
            return result;
        }
        deviceDOList.forEach(item -> {
            EdgeDeviceDTO deviceDTO = new EdgeDeviceDTO();
            deviceDTO.setDeviceCode(item.getDeviceCode());
            deviceDTO.setMcuChannel(item.getMcuChannel());
            deviceDTO.setExtra(item.getExtra());
            result.add(deviceDTO);
        });
        return result;
    }

    private EdgeMcuDTO edgeMcuDTO(DeviceDO deviceDO) {
        if (StringUtils.isBlank(deviceDO.getChannelCode())) {
            return BeanUtils.toBean(deviceDO, EdgeMcuDTO.class);
        }
        EdgeMcuDTO result = BeanUtils.toBean(deviceDO, EdgeMcuDTO.class);
        EdgeChannelDO channelDO = edgeChannelMapper.selectByCode(deviceDO.getChannelCode());

        if (channelDO != null) {
            result.setExtra(channelDO.getExtra());
            result.setConnectType(channelDO.getConnectType());
        }

        return result;
    }

    private List<EdgeDeviceDTO> edgeDeviceDTO(List<DeviceDO> deviceDOList) {
        List<EdgeDeviceDTO> result = new ArrayList<>();

        if (CollectionUtils.isAnyEmpty(deviceDOList)) {
            return result;
        }
        deviceDOList.forEach(item -> {
            EdgeDeviceDTO deviceDTO = new EdgeDeviceDTO();
            deviceDTO.setDeviceCode(item.getDeviceCode());
            deviceDTO.setMcuChannel(item.getMcuChannel());
            deviceDTO.setExtra(item.getExtra());
            result.add(deviceDTO);
        });

        return result;
    }

}