package cn.powerchina.bjy.link.iot.controller.admin.modelCategorize.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

@Schema(description = "管理后台 - 物模板分类新增/修改 Request VO")
@Data
public class ModelCategorizeSaveReqVO {

    @Schema(description = "主键，更新时必填")
    private Long id;

    /**
     * 父ID
     */
    @Schema(description = "父ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("父ID")
    private Long parentId;

    @Schema(description = "分类名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "")
    @NotBlank(message = "分类名称不能为空")
    @Size(max = 32, message = "分类名称长度不能超过 32 个字符")
    private String categorizeName;

    @Schema(description = "分类层级",  example = "")
    private Integer level;

    @Schema(description = "分类顺序", requiredMode = Schema.RequiredMode.REQUIRED, example = "")
    private Integer sort;
}
