package cn.powerchina.bjy.link.iot.dal.dataobject.roledatapermissions;
import lombok.*;
import com.baomidou.mybatisplus.annotation.*;
import cn.powerchina.bjy.cloud.framework.mybatis.core.dataobject.BaseDO;

/**
 * 角色与权限的关系 DO
 *
 * <AUTHOR>
 */
@TableName("iot_role_data_permissions")
@KeySequence("iot_role_data_permissions_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RoleDataPermissionsDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 角色ID
     */
    private Long roleId;
    /**
     * 数据范围（1：全部数据权限 2：指定数据权限）
     */
    private Integer dataScope;
    /**
     * 备注
     */
    private String remark;

}
