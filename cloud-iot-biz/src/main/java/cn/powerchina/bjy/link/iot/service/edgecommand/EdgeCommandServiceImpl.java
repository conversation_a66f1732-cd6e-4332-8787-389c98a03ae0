package cn.powerchina.bjy.link.iot.service.edgecommand;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import cn.powerchina.bjy.cloud.framework.common.util.json.JsonUtils;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.link.iot.controller.admin.command.bo.InputParamsModel;
import cn.powerchina.bjy.link.iot.controller.admin.command.vo.EdgeCommandReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.edgegateway.vo.ConvertVideoReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.instructiondownlog.vo.InstructionDownLogSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.device.DeviceDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.edgechannel.EdgeChannelDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.instructiondownlog.InstructionDownLogDO;
import cn.powerchina.bjy.link.iot.dto.down.EdgeCommandDTO;
import cn.powerchina.bjy.link.iot.dto.down.EdgePropertyDTO;
import cn.powerchina.bjy.link.iot.enums.*;
import cn.powerchina.bjy.link.iot.model.IotDeviceMessage;
import cn.powerchina.bjy.link.iot.mq.RocketMQv5Client;
import cn.powerchina.bjy.link.iot.service.device.DeviceService;
import cn.powerchina.bjy.link.iot.service.edgechannel.EdgeChannelService;
import cn.powerchina.bjy.link.iot.service.edgegateway.EdgeGatewayService;
import cn.powerchina.bjy.link.iot.service.instructiondownlog.InstructionDownLogService;
import cn.powerchina.bjy.link.iot.service.properties.PropertiesService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Strings;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Stream;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;

@Service
@Slf4j
public class EdgeCommandServiceImpl implements EdgeCommandService {

    @Resource
    private InstructionDownLogService instructionDownLogService;
    @Resource
    private RocketMQv5Client rocketMQv5Client;
    @Resource
    private EdgeGatewayService edgeGatewayService;
    @Resource
    private EdgeChannelService edgeChannelService;
    @Resource
    private DeviceService deviceService;
    @Resource
    private PropertiesService propertiesService;

    @Override
    public void commandDown(EdgeCommandReqVO edgeCommandReqVO) {
        //设备信息
        DeviceDO deviceDO = deviceService.getDeviceByCode(edgeCommandReqVO.getDeviceCode());
        //判断设备存不存在
        if (ObjectUtil.isNull(deviceDO)) {
            throw exception(ErrorCodeConstants.DEVICE_NOT_EXISTS_OR_IS_DEL);
        }
        if (Objects.equals(deviceDO.getDriverCode(), DriveEnum.MODBUS.getType())) {
            //网关子设备时 先获取网关信息再赋值
            if (NodeTypeEnum.EDGE_SUB.getType().equals(deviceDO.getNodeType())) {
                deviceDO = deviceService.getDeviceByCode(deviceDO.getParentCode());
            }
            //设备控制信息
//        EdgeCommandDTO.EdgeDeviceCommandDTO edgeDeviceCommandDTO = BeanUtils.toBean(edgeCommandReqVO, EdgeCommandDTO.EdgeDeviceCommandDTO.class);
            //判断是否设置COM2通道
            if (StringUtils.isBlank(deviceDO.getChannelCode())) {
                throw exception(ErrorCodeConstants.DEVICE_CHANNEL_IS_NULL);
            }
            //通道信息是否存在
            EdgeChannelDO edgeChannelByDriverCode = edgeChannelService.getEdgeChannelByCode(deviceDO.getChannelCode());
            if (ObjectUtil.isEmpty(edgeChannelByDriverCode)) {
                throw exception(ErrorCodeConstants.EDGE_CHANNEL_NOT_EXISTS);
            }
        }

        //记录日志
        InstructionDownLogSaveReqVO instruction = BeanUtils.toBean(edgeCommandReqVO, InstructionDownLogSaveReqVO.class);
        instruction.setInputParams(JSONUtil.toJsonStr(edgeCommandReqVO.getInputParams()));//指令下发参数
        String msgId = instructionDownLogService.createInstructionDown(instruction);

        /*
        //构建指令消息实体
        EdgeCommandDTO edgeCommandDTO = BeanUtils.toBean(deviceDO, EdgeCommandDTO.class);
        edgeCommandDTO.setConnectType(edgeChannelByDriverCode.getConnectType());
        edgeCommandDTO.setExtra(edgeChannelByDriverCode.getExtra());
        edgeCommandDTO.setSelfType(Objects.equals(deviceDO.getNodeType(), NodeTypeEnum.EDGE.getType()) ? 1 : 0);
        edgeCommandDTO.setThingIdentity(edgeCommandReqVO.getThingIdentity());
        //分情况指令
        //如果是采集通道，需要查找子设备
        if (Objects.equals(edgeCommandDTO.getThingIdentity(), CommandServeEnum.CHANNEL_VALUE.getThingIdentity())) {
            String inputParam = edgeCommandReqVO.getInputParams();
            List<InputParamsModel> inputParams = JsonUtils.parseArray(inputParam, InputParamsModel.class);
            if (!CollectionUtils.isEmpty(inputParams)) {
                //  "inputParams": "[{\"thingIdentity\":\"channels\",\"thingValue\":16,\"datatype\":\"STRING\"}]",
                DeviceDO finalDeviceDO = deviceDO;
                inputParams.forEach(item -> {
                    //查找指定通道的子设备指令
                    if (item.getThingIdentity().equals(CommandServeEnum.CHANNEL_VALUE.getThingParam())) {
                        List<String> mcuChannelList = Stream.of(item.getThingValue().split(",")).filter(StringUtils::isNotBlank).distinct().toList();
                        EdgePropertyDTO propertyDTO = propertiesService.fetchProperties(finalDeviceDO.getDeviceCode(), mcuChannelList);
                        edgeCommandDTO.setDeviceCommandDTOList(propertyDTO.getDevicePropertyDTOList().stream().map(item2 -> {
                            EdgeCommandDTO.EdgeDeviceCommandDTO commandDTO = new EdgeCommandDTO.EdgeDeviceCommandDTO();
                            org.springframework.beans.BeanUtils.copyProperties(item2, commandDTO);
                            return commandDTO;
                        }).toList());
                    }
                });
            }
        }
        //如果是设置MCU日期，则去系统日期
        else if (Objects.equals(edgeCommandDTO.getThingIdentity(), CommandServeEnum.SET_DATETIME.getThingIdentity())) {
            Map<String, Object> dateMap = new HashMap<>();
            dateMap.put(CommandServeEnum.SET_DATETIME.getThingParam(), new SimpleDateFormat("yyMMddHHmmss").format(new Date()));
            edgeCommandDTO.setExtraMap(dateMap);
        }
        //采集时刻(下行时当前时间)
        edgeCommandDTO.setCurrentTime(System.currentTimeMillis());
        edgeCommandDTO.setMessageId(msgId);
        //下发
        rocketMQv5Client.syncSendFifoMessage(IotTopicConstant.TOPIC_DEVICE_COMMAND, edgeCommandDTO,  IotTopicConstant.GROUP_DEVICE_COMMAND);
         */

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("requestId", msgId);
        jsonObject.put("deviceCode", deviceDO.getEdgeCode());
        jsonObject.put("subDeviceCode", deviceDO.getDeviceCode());
        jsonObject.put("subDeviceId", deviceDO.getDeviceCode());
        jsonObject.put("thingIdentity", edgeCommandReqVO.getThingIdentity());
        jsonObject.put("serviceId", "default");
        if (StringUtils.equals(edgeCommandReqVO.getThingIdentity(), CommandServeEnum.SET_DATETIME.getThingIdentity())) {
            JSONObject params = new JSONObject();
            params.put("datetime", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyMMddHHmmss")));
            jsonObject.put("params", params);
        }
        if (Strings.CS.equals(edgeCommandReqVO.getThingIdentity(), CommandServeEnum.STREAM_START.getThingIdentity())) {
            // 开始推流
            ConvertVideoReqVO convertVideoReqVO = new ConvertVideoReqVO();
            convertVideoReqVO.setDeviceId(String.valueOf(deviceDO.getId()));
            convertVideoReqVO.setDeviceSerial(deviceDO.getDeviceSerial());
            jsonObject.put("params", convertVideoReqVO);
        }
        if (Strings.CS.equals(edgeCommandReqVO.getThingIdentity(), CommandServeEnum.STREAM_TOUCH.getThingIdentity())) {
            // 保活
            InstructionDownLogDO instructionDownLogDO = instructionDownLogService.getLatestByDeviceCodeAndThingIdentity(deviceDO.getDeviceCode(), edgeCommandReqVO.getThingIdentity());
            if (Objects.isNull(instructionDownLogDO) || StringUtils.isBlank(instructionDownLogDO.getOutputParams())) {
                throw exception(ErrorCodeConstants.VIDEO_STREAM_INACTIVE_SKIP_KEEPALIVE);
            }
            JSONObject outputParams = JSON.parseObject(instructionDownLogDO.getOutputParams());
            String httpFlvUrl = outputParams.getString("data");
            String streamId = null;
            if (httpFlvUrl != null && httpFlvUrl.contains("/") && httpFlvUrl.endsWith(".flv")) {
                int lastSlashIndex = httpFlvUrl.lastIndexOf("/");
                int flvIndex = httpFlvUrl.lastIndexOf(".flv");
                if (lastSlashIndex != -1 && flvIndex != -1 && lastSlashIndex < flvIndex) {
                    streamId = httpFlvUrl.substring(lastSlashIndex + 1, flvIndex);
                }
            }

            JSONObject params = new JSONObject();
            params.put("token", streamId);
            jsonObject.put("params", params);
        }
        if (Strings.CS.equals(edgeCommandReqVO.getThingIdentity(), CommandServeEnum.STREAM_STOP.getThingIdentity())) {
            // 停止推流
            InstructionDownLogDO instructionDownLogDO = instructionDownLogService.getLatestByDeviceCodeAndThingIdentity(deviceDO.getDeviceCode(), edgeCommandReqVO.getThingIdentity());
            if (Objects.isNull(instructionDownLogDO) || StringUtils.isBlank(instructionDownLogDO.getOutputParams())) {
                throw exception(ErrorCodeConstants.VIDEO_STREAM_INACTIVE_SKIP_STOP);
            }
            JSONObject outputParams = JSON.parseObject(instructionDownLogDO.getOutputParams());
            String httpFlvUrl = outputParams.getString("data");
            String streamId = null;
            if (httpFlvUrl != null && httpFlvUrl.contains("/") && httpFlvUrl.endsWith(".flv")) {
                int lastSlashIndex = httpFlvUrl.lastIndexOf("/");
                int flvIndex = httpFlvUrl.lastIndexOf(".flv");
                if (lastSlashIndex != -1 && flvIndex != -1 && lastSlashIndex < flvIndex) {
                    streamId = httpFlvUrl.substring(lastSlashIndex + 1, flvIndex);
                }
            }

            JSONObject params = new JSONObject();
            params.put("token", streamId);
            jsonObject.put("params", params);
        }
        if (Strings.CS.equals(edgeCommandReqVO.getThingIdentity(), CommandServeEnum.PTZ_START.getThingIdentity())) {
            // 云台控制
            String direction = "";
            Integer speed = null;
            String inputParams = edgeCommandReqVO.getInputParams();
            JSONArray inputArray = JSON.parseArray(inputParams);
            for (int i = 0; i < inputArray.size(); i++) {
                JSONObject item = inputArray.getJSONObject(i);
                String thingIdentity = item.getString("thingIdentity");

                if ("ptz_command".equals(thingIdentity)) {
                    direction = item.getString("thingValue");
                } else if ("ptz_speed".equals(thingIdentity)) {
                    speed = item.getInteger("thingValue");
                }
            }

            JSONObject paramsParams = new JSONObject();
            paramsParams.put("direction", direction);
            paramsParams.put("speed", speed);

            JSONObject params = new JSONObject();
            params.put("deviceId", String.valueOf(deviceDO.getId()));
            params.put("deviceName", deviceDO.getDeviceName());
            params.put("deviceSerial", deviceDO.getDeviceSerial());
            params.put("params", paramsParams);

            jsonObject.put("params", params);
        }

        jsonObject.put("method", "commands.request");
        rocketMQv5Client.syncSendFifoMessage(IotDeviceMessage.TOPIC_GATEWAY_DEVICE_MESSAGE, jsonObject,  IotTopicConstant.GROUP_DEVICE_COMMAND);

    }
}
