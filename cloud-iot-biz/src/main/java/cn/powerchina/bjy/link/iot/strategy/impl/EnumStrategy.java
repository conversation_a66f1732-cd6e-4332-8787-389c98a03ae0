package cn.powerchina.bjy.link.iot.strategy.impl;

import cn.powerchina.bjy.cloud.framework.common.util.collection.CollectionUtils;
import cn.powerchina.bjy.cloud.framework.common.util.json.JsonUtils;
import cn.powerchina.bjy.link.iot.dto.up.EdgeReadPropertyValue;
import cn.powerchina.bjy.link.iot.strategy.ProductModelStrategy;
import cn.powerchina.bjy.link.iot.strategy.vo.EnumLimitVO;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * EnumStrategy
 * 枚举类型校验上传的值是否属于配置的枚举值集合
 * 入参结构：{"enumItem":[{key:'', value:''}]}
 *
 * <AUTHOR>
 **/
@Slf4j
@Component("enumStrategy")
public class EnumStrategy implements ProductModelStrategy {
    @Override
    public boolean shouldIgnore(Object context, String strategy) {
        if (StringUtils.isBlank(strategy) || null == context) {
            return false;
        }
        try {
            EnumLimitVO enumLimitVO = JsonUtils.parseObject(strategy, new TypeReference<>() {
            });
            // 如果规则为空，则正常处理数据
            if (null == enumLimitVO || CollectionUtils.isAnyEmpty(enumLimitVO.getEnumItem())) {
                return false;
            }
            String data = (String) context;
            return !enumLimitVO.keySet().contains(data);
        } catch (Exception e) {
            log.error("物模型数据类型【enum】配置转换异常，配置【{}】", strategy, e);
        }
        return false;
    }

    @Override
    public boolean shouldIgnore(String context, String strategy, EdgeReadPropertyValue.EdgeDevicePropertyValueDTO valueDTO) {
        if (StringUtils.isBlank(strategy) || null == context) {
            return false;
        }
        try {
            EnumLimitVO enumLimitVO = JsonUtils.parseObject(strategy, new TypeReference<>() {
            });
            // 如果规则为空，则正常处理数据
            if (null == enumLimitVO || CollectionUtils.isAnyEmpty(enumLimitVO.getEnumItem())) {
                return false;
            }
            return !enumLimitVO.keySet().contains(context);
        } catch (Exception e) {
            log.error("物模型数据类型【enum】配置转换异常，配置【{}】", strategy, e);
        }
        return false;
    }
}
