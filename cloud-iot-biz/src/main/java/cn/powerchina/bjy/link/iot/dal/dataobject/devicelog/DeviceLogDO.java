package cn.powerchina.bjy.link.iot.dal.dataobject.devicelog;

import cn.powerchina.bjy.cloud.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 设备日志 DO
 *
 * <AUTHOR>
 */
@TableName("iot_device_log")
@KeySequence("iot_device_log_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeviceLogDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 设备编号
     */
    private String deviceCode;
    /**
     * 日志级别(error=0,warn=1,info=2,debug=3,other=4)
     */
    private Integer logLevel;
    /**
     * 日志大小
     */
    private Integer fileSize;
    /**
     * 日志文件路径
     */
    private String path;
    /**
     * 备注
     */
    private String remark;

}