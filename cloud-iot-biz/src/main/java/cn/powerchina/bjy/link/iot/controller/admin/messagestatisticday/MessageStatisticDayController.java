package cn.powerchina.bjy.link.iot.controller.admin.messagestatisticday;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.link.iot.controller.admin.messagestatisticday.vo.MessageStatisticDayPageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.messagestatisticday.vo.MessageStatisticDayRespVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.messagestatisticday.MessageStatisticDayDO;
import cn.powerchina.bjy.link.iot.service.messagestatisticday.MessageStatisticDayService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;


@Tag(name = "管理后台 - 设备消息数按日统计")
@RestController
@RequestMapping("/iot/message/statistic/day")
@Validated
public class MessageStatisticDayController {

    @Resource
    private MessageStatisticDayService messageStatisticDayService;

    @GetMapping("/page")
    @Operation(summary = "获得设备消息数按日统计分页")
    // @PreAuthorize("@ss.hasPermission('iot:message-statistic-day:query')")
    public CommonResult<PageResult<MessageStatisticDayRespVO>> getMessageStatisticDayPage(@Valid MessageStatisticDayPageReqVO pageReqVO) {
        PageResult<MessageStatisticDayDO> pageResult = messageStatisticDayService.getMessageStatisticDayPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, MessageStatisticDayRespVO.class));
    }

}
