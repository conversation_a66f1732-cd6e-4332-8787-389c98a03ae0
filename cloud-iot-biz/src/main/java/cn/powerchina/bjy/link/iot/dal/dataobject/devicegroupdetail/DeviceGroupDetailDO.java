package cn.powerchina.bjy.link.iot.dal.dataobject.devicegroupdetail;

import cn.powerchina.bjy.cloud.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 设备分组明细 DO
 *
 * <AUTHOR>
 */
@TableName("iot_device_group_detail")
@KeySequence("iot_device_group_detail_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeviceGroupDetailDO extends BaseDO {

    /**
     * 主键id
     */
    @TableId
    private Long id;
    /**
     * 设备分组id
     */
    private Long deviceGroupId;
    /**
     * 设备编号
     */
    private String deviceCode;

}