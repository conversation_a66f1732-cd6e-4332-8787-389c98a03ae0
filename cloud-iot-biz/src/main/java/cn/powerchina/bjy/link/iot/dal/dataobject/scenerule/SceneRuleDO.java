package cn.powerchina.bjy.link.iot.dal.dataobject.scenerule;

import cn.powerchina.bjy.cloud.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;
import com.baomidou.mybatisplus.annotation.*;

import java.time.LocalDate;

/**
 * 场景规则 DO
 *
 * <AUTHOR>
 */
@TableName("iot_scene_rule")
@KeySequence("iot_scene_rule_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SceneRuleDO extends BaseDO {

    /**
     * 主键id
     */
    @TableId
    private Long id;
    /**
     * 规则名称
     */
    private String ruleName;
    /**
     * 所属资源空间ID
     */
    private Long resourceSpaceId;
    /**
     * 状态:0-禁用,1-启用
     */
    private Integer status;
    /**
     * 是否抑制:0-禁用抑制,1-启用抑制
     */
    private Integer inhibition;
    /**
     * 生效时段类型:1-全天,2-自定义
     */
    private Integer effectiveType;
    /**
     * 生效开始时间
     */
    private String effectiveStartTime;
    /**
     * 生效结束时间
     */
    private String effectiveEndTime;
    /**
     * 重复类型:1-每天,2-指定日期,3-指定周期,4-自定义
     */
    private Integer repeatType;
    /**
     * 开始日期/指定日期
     */
    private LocalDate repeatStartDate;
    /**
     * 结束日期
     */
    private LocalDate repeatEndDate;
    /**
     * 每周重复的星期几,如:1,2,3,4,5,6,7
     */
    private String repeatWeekDays;
    /**
     * 规则表达式
     */
    private String ruleExpression;
    private String inhibitionExpression;
    /**
     * 规则优先级
     */
    private Integer rulePriority;
    /**
     * 规则描述
     */
    private String ruleDesc;

    private String ruleAction;

    /**
     * 未触发次数
     */
    private Integer unsatisfiedTimes;

}