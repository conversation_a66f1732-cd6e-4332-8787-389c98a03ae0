package cn.powerchina.bjy.link.iot.service.devicecurrentattribute;

import java.util.*;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.link.iot.controller.admin.devicecurrentattribute.vo.DeviceShadowPageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.devicecurrentattribute.vo.DeviceShadowSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.devicecurrentattribute.DeviceShadowDO;
import jakarta.validation.*;

/**
 * 设备上报的最新属性 Service 接口
 *
 * <AUTHOR>
 */
public interface DeviceShadowService {

    /**
     * 创建设备上报的最新属性
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createDeviceShadow(@Valid DeviceShadowSaveReqVO createReqVO);

    /**
     * 更新设备上报的最新属性
     *
     * @param updateReqVO 更新信息
     */
    void updateDeviceShadow(@Valid DeviceShadowSaveReqVO updateReqVO);

    /**
     * 删除设备上报的最新属性
     *
     * @param id 编号
     */
    void deleteDeviceShadow(Long id);

    /**
     * 获得设备上报的最新属性
     *
     * @param id 编号
     * @return 设备上报的最新属性
     */
    DeviceShadowDO getDeviceShadow(Long id);

    /**
     * 获得设备上报的最新属性分页
     *
     * @param pageReqVO 分页查询
     * @return 设备上报的最新属性分页
     */
    PageResult<DeviceShadowDO> getDeviceShadowPage(DeviceShadowPageReqVO pageReqVO);

    void createOrUpdateShadow(@Valid List<DeviceShadowDO> deviceShadowDOList);
    void createShadow(@Valid List<DeviceShadowDO> deviceShadowDOList);
    void updateShadow(@Valid List<DeviceShadowDO> deviceShadowDOList);

    /**
     * 查询上报当前属性值
     * @param deviceCode
     * @return
     */
    List<DeviceShadowDO> getShadowListByDeviceCode(String deviceCode);

    DeviceShadowDO getShadowByDeviceCodeAndThingIdentity(String deviceCode, String thingIdentity);

    /**
     * 查询所有设备的当前属性值
     * @return
     */
    List<DeviceShadowDO> getShadowList();

}