package cn.powerchina.bjy.link.iot.util;

import cn.powerchina.bjy.link.iot.controller.admin.notificationconfig.vo.NotificationConfigSaveReqVO;
import jakarta.mail.*;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;

import java.util.Properties;

@Slf4j
public class EmailCheckUtils {
    private static final int SSL_PORT = 465;
    private static final int TLS_PORT = 587;

    public static boolean checkConnection(NotificationConfigSaveReqVO notificationConfig) {
        Properties props = getProperties(notificationConfig);

        Session session = Session.getInstance(props,
                new Authenticator() {
                    protected PasswordAuthentication getPasswordAuthentication() {
                        return new PasswordAuthentication(notificationConfig.getUsername(), notificationConfig.getNotificationPass());
                    }
                });

        try (Transport transport = session.getTransport("smtp")) {
            transport.connect(notificationConfig.getNotificationHost(), Integer.parseInt(notificationConfig.getNotificationPort()), notificationConfig.getUsername(), notificationConfig.getNotificationPass());
            return transport.isConnected();
        } catch (AuthenticationFailedException e) {
            log.error("邮箱认证失败: {}", e.getMessage());
        } catch (MessagingException e) {
            log.error("邮箱通信异常: {}", e.getMessage());
        }
        return false;
    }

    private static @NotNull Properties getProperties(NotificationConfigSaveReqVO notificationConfig) {
        Properties props = new Properties();
        props.put("mail.smtp.auth", "true");
        props.put("mail.smtp.host", notificationConfig.getNotificationHost());
        props.put("mail.smtp.port", Integer.parseInt(notificationConfig.getNotificationPort()));

        // 端口特定配置
        if (Integer.parseInt(notificationConfig.getNotificationPort()) == SSL_PORT) {
            props.put("mail.smtp.ssl.enable", "true");
            props.put("mail.smtp.ssl.protocols", "TLSv1.2");
        } else if (Integer.parseInt(notificationConfig.getNotificationPort()) == TLS_PORT) {
            props.put("mail.smtp.starttls.enable", "true");
            props.put("mail.smtp.starttls.required", "true");
        }

        // 超时设置
        props.put("mail.smtp.connectiontimeout", "5000");
        props.put("mail.smtp.timeout", "5000");
        props.put("mail.smtp.writetimeout", "5000");
        return props;
    }
}
