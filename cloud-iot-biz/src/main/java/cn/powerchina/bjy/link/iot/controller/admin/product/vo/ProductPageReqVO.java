package cn.powerchina.bjy.link.iot.controller.admin.product.vo;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 产品分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ProductPageReqVO extends PageParam {

    @Schema(description = "产品名称")
    private String productName;

    @Schema(description = "产品编码")
    private String productCode;

    @Schema(description = "产品型号")
    private String productModel;

    @Schema(description = "厂商")
    private String firmName;

    @Schema(description = "产品描述")
    private String description;

    @Schema(description = "节点类型（0直连，1网关，2网关子设备）")
    private Integer nodeType;

    @Schema(description = "协议编号")
    private String protocolCode;

    @Schema(description = "联网方式")
    private String networkMethod;

    @Schema(description = "数据格式")
    private String dataFormat;

    @Schema(description = "产品启用状态（0未启用，1启用，默认1）")
    private Integer productState;

    @Schema(description = "秘钥")
    private String productSecret;

    @Schema(description = "资源空间id")
    private Long resourceSpaceId;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "产品code集合")
    private List<String> productCodes;

}