package cn.powerchina.bjy.link.iot.strategy;

import cn.powerchina.bjy.link.iot.dto.up.EdgeReadPropertyValue;

/**
 * ProductModelPolicy
 * 物模型校验策略
 *
 * <AUTHOR>
 **/
public interface ProductModelStrategy {

    /**
     * 校验context，如果不符合规则则忽略（返回true），符合规则则不能忽略（返回false）
     * 入参context需提前进行类型转换，如int类型，入参为int值而非字符串,shouldIgnore(2, "");
     *
     * @param context  具体内容
     * @param strategy 规则
     * @return true - 忽略
     * false - 正常数据
     */
    boolean shouldIgnore(Object context, String strategy);

    /**
     * 校验context，如果不符合规则则忽略（返回true），符合规则则不能忽略（返回false）
     * 入参context无需提前进行类型转换，如int类型，入参为字符串而非int值，shouldIgnore("2", "");
     *
     * @param context  具体内容
     * @param strategy 规则
     * @return true - 忽略
     * false - 正常数据
     */
    boolean shouldIgnore(String context, String strategy, EdgeReadPropertyValue.EdgeDevicePropertyValueDTO valueDTO);
}
