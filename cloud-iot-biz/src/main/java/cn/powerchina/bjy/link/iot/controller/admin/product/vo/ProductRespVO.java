package cn.powerchina.bjy.link.iot.controller.admin.product.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 产品 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ProductRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "产品名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("产品名称")
    private String productName;

    @Schema(description = "产品编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("产品编码")
    private String productCode;

    @Schema(description = "产品型号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("产品型号")
    private String productModel;

    @Schema(description = "厂商", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("厂商")
    private String firmName;

    @Schema(description = "产品描述")
    @ExcelProperty("产品描述")
    private String description;

    @Schema(description = "节点类型（0直连，1网关，2网关子设备）", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("节点类型（0直连，1网关，2网关子设备）")
    private Integer nodeType;

    @Schema(description = "协议编号")
    @ExcelProperty("协议编号")
    private String protocolCode;

    @Schema(description = "联网方式")
    @ExcelProperty("联网方式")
    private String networkMethod;

    @Schema(description = "数据格式")
    @ExcelProperty("数据格式")
    private String dataFormat;

    @Schema(description = "产品启用状态（0未启用，1启用，默认1）", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("产品启用状态（0未启用，1启用，默认1）")
    private Integer productState;

    @Schema(description = "秘钥", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("秘钥")
    private String productSecret;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "设备数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("设备数量")
    private Long deviceCount;

    @Schema(description = "资源空间id", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("资源空间id")
    private Long resourceSpaceId;

    @Schema(description = "资源空间名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("资源空间名称")
    private String spaceName;

    @Schema(description = "模板ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("模板ID")
    private String templateIds;

    @Schema(description = "设备限额", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long deviceLimit;

    @Schema(description = "已使用额度", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long usedQuota;

    @Schema(description = "动态注册(0:否; 1:是)", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer dynamicRegister;

    @Schema(description = "预注册(0:否; 1:是)", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer preRegister;

    @Schema(description = "认证方式(1:设备密钥)", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer authMethod;

}