package cn.powerchina.bjy.link.iot.controller.admin.product.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(description = "管理后台 - 产品新增/修改 Request VO")
@Data
public class ProductSaveReqVO {

    @Schema(description = "主键，更新时必填")
    private Long id;

    @Schema(description = "产品名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "")
    @NotEmpty(message = "产品名称不能为空")
    private String productName;

    @Schema(description = "产品编码")
    private String productCode;

    @Schema(description = "产品型号")
    private String productModel;

    @Schema(description = "厂商")
    private String firmName;

    @Schema(description = "产品描述", example = "你猜")
    private String description;

    @Schema(description = "节点类型（0直连，1网关，2网关子设备）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "节点类型（0直连，1网关，2网关子设备）不能为空")
    private Integer nodeType;

    @Schema(description = "协议编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "协议不能为空")
    private String protocolCode;

    @Schema(description = "联网方式")
    private String networkMethod;

    @Schema(description = "数据格式", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "数据格式不能为空")
    private String dataFormat;

    @Schema(description = "产品启用状态（0未启用，1启用，默认1）")
    private Integer productState = 1;

    @Schema(description = "秘钥")
    private String productSecret;

    @Schema(description = "资源空间id")
    private Long resourceSpaceId;

    @Schema(description = "物模板id")
    private String templateIds;

    @Schema(description = "认证方式(1:设备密钥)")
    private Integer authMethod;

    @Schema(description = "设备限额")
    private Long deviceLimit;

    @Schema(description = "已使用额度")
    private Long usedQuota;

    @Schema(description = "是否动态注册")
    private Integer dynamicRegister;

    @Schema(description = "是否预注册")
    private Integer preRegister;

}