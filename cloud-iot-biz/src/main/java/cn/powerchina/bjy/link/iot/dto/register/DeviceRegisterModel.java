package cn.powerchina.bjy.link.iot.dto.register;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 设备上下线
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DeviceRegisterModel implements Serializable {

    private String deviceId;
    private String deviceName;
    private String userName;
    private String password;
    private String productId;
    private String gatewayId;
    private Long reportTime;

    /**
     * 类型(0:edge实例; 1:设备）
     */
    private Integer deviceType;


}
