package cn.powerchina.bjy.link.iot.controller.admin.device.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 设备新增/修改 Request VO")
@Data
@JsonIgnoreProperties(ignoreUnknown=true)
public class DeviceSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "720")
    private Long id;

    @Schema(description = "产品编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "产品编码不能为空")
    private String productCode;

    @Schema(description = "项目编码")
    private String projectCode;

    @Schema(description = "设备名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    @NotEmpty(message = "设备名称不能为空")
    private String deviceName;

    @Schema(description = "设备编号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String deviceCode;

    @Schema(description = "父设备号（网关）")
    private String parentCode;

    @Schema(description = "设备唯一标识")
    @NotEmpty(message = "设备唯一标识不能为空")
    private String deviceSerial;

    @Schema(description = "是否启用设备影子(0=禁用，1=启用)，默认启用")
    private Boolean shadow;

    @Schema(description = "通道编码")
    private String channelCode;

    @Schema(description = "mcu通道号")
    private String mcuChannel;

    @Schema(description = "从站号")
    private String slaveId;

    @Schema(description = "下发状态（0-未下发；1-已下发；）", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer distributeState;

    @Schema(description = "连接状态（0：未激活 1：离线 2；在线 3：禁用）", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer linkState;

    @Schema(description = "节点类型(0直连，1网关，2网关子设备）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "节点类型(0直连，1网关，2网关子设备）不能为空")
    private Integer nodeType;

    @Schema(description = "经度")
    private String longitude;

    @Schema(description = "维度")
    private String latitude;

    @Schema(description = "差异化扩展")
    private String extra;

    @Schema(description = "最后上线时间")
    private LocalDateTime lastUpTime;

    @Schema(description = "网关实例编码")
    private String edgeCode;

    @Schema(description = "驱动编码")
    private String driverCode;

    @Schema(description = "描述")
    private String remark;

    @Schema(description = "设备密钥")
    private String deviceSecret;

    @Schema(description = "被替换设备编码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String replaceDeviceCode;

    @Schema(description = "启用状态（0:禁用 1：启用）", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer status;

    /**
     * 类型(0:edge实例; 1:设备）
     */
    private Integer deviceType;

}