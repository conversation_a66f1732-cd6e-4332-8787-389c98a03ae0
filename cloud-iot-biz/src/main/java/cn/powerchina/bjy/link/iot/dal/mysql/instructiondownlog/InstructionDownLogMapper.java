package cn.powerchina.bjy.link.iot.dal.mysql.instructiondownlog;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.iot.controller.admin.instructiondownlog.vo.InstructionDownLogPageReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.instructiondownlog.InstructionDownLogDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 指令下发操作记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InstructionDownLogMapper extends BaseMapperX<InstructionDownLogDO> {

    default PageResult<InstructionDownLogDO> selectPage(InstructionDownLogPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<InstructionDownLogDO>()
                .eqIfPresent(InstructionDownLogDO::getDeviceCode, reqVO.getDeviceCode())
                .eqIfPresent(InstructionDownLogDO::getThingType, reqVO.getThingType())
                .eqIfPresent(InstructionDownLogDO::getThingIdentity, reqVO.getThingIdentity())
                .likeIfPresent(InstructionDownLogDO::getThingName, reqVO.getThingName())
                .eqIfPresent(InstructionDownLogDO::getOperatorSource, reqVO.getOperatorSource())
                .eqIfPresent(InstructionDownLogDO::getMessageId, reqVO.getMessageId())
                .eqIfPresent(InstructionDownLogDO::getInputParams, reqVO.getInputParams())
                .eqIfPresent(InstructionDownLogDO::getOutputParams, reqVO.getOutputParams())
                .eqIfPresent(InstructionDownLogDO::getUpConsumeTime, reqVO.getUpConsumeTime())
                .eqIfPresent(InstructionDownLogDO::getDownConsumeTime, reqVO.getDownConsumeTime())
                .betweenIfPresent(InstructionDownLogDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(InstructionDownLogDO::getId));
    }

}