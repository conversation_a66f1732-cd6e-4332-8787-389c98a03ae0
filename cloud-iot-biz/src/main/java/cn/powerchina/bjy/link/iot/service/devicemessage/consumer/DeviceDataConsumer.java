//package cn.powerchina.bjy.link.iot.service.devicemessage.consumer;
//
//import cn.powerchina.bjy.link.iot.service.scenerule.SceneRuleService;
//import jakarta.annotation.Resource;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.rocketmq.common.message.MessageExt;
//import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
//import org.apache.rocketmq.spring.core.RocketMQListener;
//import org.springframework.stereotype.Service;
//
//@Service
//@Slf4j
//public class DeviceDataConsumer {
//
//    @Resource
//    private SceneRuleService sceneRuleService;
//
////    @Resource
////    private SceneRuleAlarmRecordService sceneRuleAlarmRecordService;
//    //主题和消费者组需要根据实际的情况修改
//    @RocketMQMessageListener(
//            topic = "device-data-topic",
//            consumerGroup = "alarm-consumer-group",
//            requestTimeout = 10, consumptionThreadCount = 10
//    )
//    public class DeviceDataListener implements RocketMQListener<MessageExt> {
//
//        @Override
//        public void onMessage(MessageExt message) {
////            try {
////                // 1. 解析设备数据
////                String body = new String(message.getBody(), StandardCharsets.UTF_8);
////                DeviceData deviceData = JSON.parseObject(body, DeviceData.class);
////
////                // 2. 获取可能触发的规则
////                List<SceneRuleDO> matchedRules = sceneRuleService.findMatchingRules(deviceData);
////
////                // 3. 对每个匹配的规则进行评估
////                for (SceneRuleDO rule : matchedRules) {
////                    // 4. 检查规则触发条件是否满足
////                    if (evaluateRuleConditions(rule, deviceData)) {
////
////                        // 5. 对每个告警动作进行处理
////                        for (SceneRuleActionDO action : rule.getActions()) {
////                            if (action.getType() == ActionType.ALARM) {
////                                Long alarmId = action.getAlarmId();
////
////                                // 6. 关键步骤：告警去重检查
////                                if (!sceneRuleAlarmRecordService.existsUnrecoveredAlarm(alarmId)) {
////                                    // 7. 创建告警记录
////                                    SceneRuleAlarmRecordSaveReqVO createReqVO = new SceneRuleAlarmRecordSaveReqVO();
////                                    createReqVO.setAlarmId(alarmId);
////                                    createReqVO.setTriggerTime(LocalDateTime.now());
////                                    createReqVO.setSource(deviceData.getDeviceId());
////                                    // 设置其他属性...
////
////                                    sceneRuleAlarmRecordService.createSceneRuleAlarmRecord(createReqVO);
////
////                                    log.info("创建告警记录: 规则[{}], 告警[{}]", rule.getName(), action.getAlarmName());
////                                } else {
////                                    // 告警被去重
////                                    log.info("告警去重: 规则[{}], 告警[{}], 10分钟内已存在未恢复告警",
////                                            rule.getName(), action.getAlarmName());
////                                }
////                            }
////                        }
////                    }
////                }
////
////            } catch (Exception e) {
////                log.error("处理设备数据异常: " + message.getMsgId(), e);
////                // 根据业务需要决定是否重试或忽略异常消息
////            }
////        }
////
////        private boolean evaluateRuleConditions(SceneRuleDO rule, DeviceData deviceData) {
////            // 实现规则条件匹配逻辑
////            return true; // 简化示例，实际需要实现具体的规则评估逻辑
//       }
//  }
//}