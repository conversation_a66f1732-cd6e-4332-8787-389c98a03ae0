package cn.powerchina.bjy.link.iot.service.user;

import cn.hutool.core.bean.BeanUtil;
import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.cloud.system.api.permission.RoleApi;
import cn.powerchina.bjy.cloud.system.api.permission.dto.RoleRespDTO;
import cn.powerchina.bjy.cloud.system.api.user.AdminUserApi;
import cn.powerchina.bjy.cloud.system.api.user.dto.AdminUserRespDTO;
import cn.powerchina.bjy.cloud.system.api.user.dto.UserSaveRequestVO;
import cn.powerchina.bjy.cloud.system.api.user.dto.UserUpdatePasswordVO;
import cn.powerchina.bjy.link.iot.controller.admin.user.vo.UserPageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.user.vo.UserPasswordVO;
import cn.powerchina.bjy.link.iot.controller.admin.user.vo.UserRespVO;
import cn.powerchina.bjy.link.iot.controller.admin.user.vo.UserSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.user.UserDO;
import cn.powerchina.bjy.link.iot.dal.mysql.user.UserMapper;
import cn.powerchina.bjy.link.iot.enums.ErrorCodeConstants;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.link.iot.enums.ErrorCodeConstants.USER_NOT_EXISTS;


/**
 * 用户信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class UserServiceImpl implements UserService {

    @Resource
    private UserMapper userMapper;
    @Resource
    private AdminUserApi userApi;
    @Resource
    private RoleApi roleApi;

    @Override
    public Long createUser(UserSaveReqVO createReqVO) {
        UserSaveRequestVO createApiVO = new UserSaveRequestVO();
        BeanUtil.copyProperties(createReqVO, createApiVO);
        // 插入
        UserDO user = BeanUtils.toBean(createReqVO, UserDO.class);
        if (Objects.nonNull(userMapper.selectOne(new LambdaQueryWrapperX<UserDO>().eq(UserDO::getMobile, user.getMobile())))) {
            throw exception(ErrorCodeConstants.USER_ADMIN_IPHONE_EXISTS);
        }
        if (Objects.nonNull(userMapper.selectOne(new LambdaQueryWrapperX<UserDO>().eq(UserDO::getEmail, user.getEmail())))) {
            throw exception(ErrorCodeConstants.USER_ADMIN_EMAIL_EXISTS);
        }
        if (Objects.nonNull(userMapper.selectOne(new LambdaQueryWrapperX<UserDO>()
                .func(wrapper ->
                        wrapper.eq(UserDO::getUsername, user.getUsername().toLowerCase())
                                .or()
                                .eq(UserDO::getUsername, user.getUsername().toUpperCase()))))) {
            throw exception(ErrorCodeConstants.USER_ADMIN_NAME_EXISTS);
        }
        if (!createReqVO.getPassword().equals(createReqVO.getConfirmPassword())) {
            throw exception(ErrorCodeConstants.USER_PASSWORD_INCONSISTENCY);
        }
        Long userId;
        //system系统已存在，但iot不存在，直接插入
        AdminUserRespDTO userByUsername = userApi.getUserByUsername(createReqVO.getUsername());
        if (null == userByUsername) {
            userId = userApi.saveUser(createApiVO).getCheckedData();
        } else {
            userId = userByUsername.getId();
        }
        user.setId(userId);
        userMapper.insert(user);
        // 返回
        return user.getId();
    }

    @Override
    public void updateUser(UserSaveReqVO updateReqVO) {
        // 校验存在
        UserDO userDO = validateUserExists(updateReqVO.getId());
        UserSaveRequestVO createApiVO = new UserSaveRequestVO();
        BeanUtil.copyProperties(updateReqVO, createApiVO);
        createApiVO.setStatus(userDO.getStatus());
        userApi.updateUser(createApiVO).getCheckedData();
        //默认普通用户，无需更新用户角色关系，直接更新用户表
        UserDO UserDOT = userMapper.selectOne(new LambdaQueryWrapperX<UserDO>().eq(UserDO::getMobile, updateReqVO.getMobile()));
        if (Objects.nonNull(UserDOT) && (Objects.isNull(updateReqVO.getId()) || !Objects.equals(updateReqVO.getId(), UserDOT.getId()))) {
            throw exception(ErrorCodeConstants.USER_ADMIN_IPHONE_EXISTS);
        }
        UserDO UserDOName = userMapper.selectOne(new LambdaQueryWrapperX<UserDO>().eq(UserDO::getUsername, updateReqVO.getUsername()));
        if (Objects.nonNull(UserDOName) && (Objects.isNull(updateReqVO.getId()) || !Objects.equals(updateReqVO.getId(), UserDOName.getId()))) {
            throw exception(ErrorCodeConstants.USER_ADMIN_NAME_EXISTS);
        }
        // 更新
        UserDO updateObj = BeanUtils.toBean(updateReqVO, UserDO.class);
        userMapper.updateById(updateObj);
    }

    @Override
    public void deleteUser(Long id) {
        // 校验存在
        validateUserExists(id);
        //rpc删除system库的用户
        userApi.deleteUser(id);
        // 删除
        userMapper.deleteById(id);
    }

    @Override
    public UserDO getUser(Long id) {
        UserDO userDO = userMapper.selectById(id);
        if (Objects.isNull(userDO)) {
            CommonResult<AdminUserRespDTO> user = userApi.getUser(id);
            if (user.isSuccess()) {
                userDO = new UserDO();
                BeanUtil.copyProperties(user.getCheckedData(), userDO);
            }
        }
        return userDO;
    }

    @Override
    public PageResult<UserRespVO> getUserPage(UserPageReqVO pageReqVO) {
        PageResult<UserDO> pageResult = userMapper.selectPage(pageReqVO);
        PageResult<UserRespVO> respResult = null;
        if (pageResult != null) {
            respResult = BeanUtils.toBean(pageResult, UserRespVO.class);
            if (!CollectionUtils.isEmpty(respResult.getList())) {
                respResult.getList().stream().forEach(item -> {
                    CommonResult<Map<Long, List<RoleRespDTO>>> commonResult = roleApi.userRoles(Collections.singleton(item.getId()));
                    if (Objects.nonNull(commonResult) && !CollectionUtils.isEmpty(commonResult.getData())) {
                        List<RoleRespDTO> roleList = commonResult.getData().get(item.getId());
                        StringBuilder sb = new StringBuilder();
                        if (!CollectionUtils.isEmpty(roleList)) {
                            for (int i = 0; i < roleList.size(); i++) {
                                sb.append(roleList.get(i).getName());
                                if (i < roleList.size() - 1) {
                                    sb.append(",");
                                }
                            }
                        }
                        item.setRoleName(sb.toString());
                    }
                });
            }
        }
        return respResult;
    }

    @Override
    public void updateUserStatus(Long id, Integer status) {
        // 校验用户存在
        validateUserExists(id);
        UserSaveRequestVO createApiVO = new UserSaveRequestVO();
        createApiVO.setId(id);
        createApiVO.setStatus(status);
        userApi.updateUser(createApiVO).getCheckedData();
        // 更新状态
        UserDO updateObj = new UserDO();
        updateObj.setId(id);
        updateObj.setStatus(status);
        userMapper.updateById(updateObj);
    }

    @Override
    public void updatePassword(UserPasswordVO userPasswordVO) {
        UserUpdatePasswordVO userUpdatePasswordVO = new UserUpdatePasswordVO();
        BeanUtil.copyProperties(userPasswordVO, userUpdatePasswordVO);
        Long userId = userApi.updateUserPassword(userUpdatePasswordVO).getCheckedData();
        if (Objects.nonNull(userPasswordVO.getMobile())) {
            UserDO updateObj = new UserDO();
            updateObj.setId(userId);
            updateObj.setMobile(userPasswordVO.getMobile());
            userMapper.updateById(updateObj);
        }
    }

    @Override
    public UserDO getUserByUsername(UserSaveReqVO updateReqVO) {
        //先判断iot是否存在同用户
        UserDO UserDOName = userMapper.selectOne(new LambdaQueryWrapperX<UserDO>().eq(UserDO::getUsername, updateReqVO.getUsername()));
        if (Objects.nonNull(UserDOName) && (Objects.isNull(updateReqVO.getId()) || !Objects.equals(updateReqVO.getId(), UserDOName.getId()))) {
            throw exception(ErrorCodeConstants.USER_ADMIN_NAME_EXISTS);
        }
        if (null != UserDOName) {
            return UserDOName;
        }
        AdminUserRespDTO userByUsername = userApi.getUserByUsername(updateReqVO.getUsername());
        if (null != userByUsername) {
            UserDO userDO = new UserDO();
            userDO.setUsername(updateReqVO.getUsername());
            userDO.setMobile(userByUsername.getMobile());
            userDO.setName(userByUsername.getName());
            return userDO;
        }
        return null;
    }


    private UserDO validateUserExists(Long id) {
        UserDO userDO = userMapper.selectById(id);
        if (Objects.isNull(userDO)) {
            throw exception(USER_NOT_EXISTS);
        }
        return userDO;
    }

}