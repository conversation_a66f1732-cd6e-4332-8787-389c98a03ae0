package cn.powerchina.bjy.link.iot.service.properties;

import cn.powerchina.bjy.link.iot.dto.down.EdgePropertyDTO;

import java.util.List;

/**
 * 获取属性 service
 *
 * <AUTHOR>
 **/
public interface PropertiesService {

    /**
     * 获取属性
     * 当前以device维度进行下发，包括两种情况：
     * 1）device为网关设备，则会查询该mcu下所有的子设备一起进行下发；
     * 2）device为子设备，则只查询当前子设备信息和对应的网关信息进行下发；
     *
     * @param deviceCode 设备编码
     */
    void fetchEdgeProperties(String deviceCode, List<String> mcuChannelList);

    /**
     * 获取属性
     * 当前以device维度进行下发，包括两种情况：
     * 1）device为网关设备，则会查询该mcu下所有mucChannelList子设备一起进行下发；
     * 2）device为子设备，则只查询当前子设备信息和对应的网关信息进行下发；
     *
     * @param deviceCode
     * @param mcuChannelList
     * @return
     */
    EdgePropertyDTO fetchProperties(String deviceCode, List<String> mcuChannelList);
}
