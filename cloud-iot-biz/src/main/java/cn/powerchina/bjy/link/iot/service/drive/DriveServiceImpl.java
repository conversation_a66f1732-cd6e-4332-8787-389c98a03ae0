package cn.powerchina.bjy.link.iot.service.drive;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.link.iot.controller.admin.device.vo.DeviceExtraReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.drive.vo.DrivePageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.drive.vo.DriveSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.device.DeviceDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.drive.DriveDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.edgechannel.EdgeChannelDO;
import cn.powerchina.bjy.link.iot.dal.mysql.device.DeviceMapper;
import cn.powerchina.bjy.link.iot.dal.mysql.drive.DriveMapper;
import cn.powerchina.bjy.link.iot.enums.DeviceTypeEnum;
import cn.powerchina.bjy.link.iot.enums.DriveEnum;
import cn.powerchina.bjy.link.iot.enums.LinkStateEnum;
import cn.powerchina.bjy.link.iot.enums.NodeTypeEnum;
import cn.powerchina.bjy.link.iot.mq.MqttPublisher;
import cn.powerchina.bjy.link.iot.service.edgechannel.EdgeChannelService;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.List;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.link.iot.enums.ErrorCodeConstants.DRIVE_NOT_EXISTS;

/**
 * 驱动 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class DriveServiceImpl implements DriveService {

    @Resource
    private EdgeChannelService edgeChannelService;

    @Resource
    private DriveMapper driveMapper;

    @Resource
    private DeviceMapper deviceMapper;

    @Resource
    private MqttPublisher mqttPublisher;

    @Override
    public Long createDrive(DriveSaveReqVO createReqVO) {
        // 插入
        DriveDO drive = BeanUtils.toBean(createReqVO, DriveDO.class);
        driveMapper.insert(drive);
        // 返回
        return drive.getId();
    }

    @Override
    public void updateDrive(DriveSaveReqVO updateReqVO) {
        // 校验存在
        validateDriveExists(updateReqVO.getId());
        // 更新
        DriveDO updateObj = BeanUtils.toBean(updateReqVO, DriveDO.class);
        driveMapper.updateById(updateObj);
    }

    @Override
    public void deleteDrive(Long id) {
        // 校验存在
        validateDriveExists(id);
        // 删除
        driveMapper.deleteById(id);
    }

    private void validateDriveExists(Long id) {
        if (driveMapper.selectById(id) == null) {
            throw exception(DRIVE_NOT_EXISTS);
        }
    }

    @Override
    public DriveDO getDrive(Long id) {
        return driveMapper.selectById(id);
    }

    @Override
    public PageResult<DriveDO> getDrivePage(DrivePageReqVO pageReqVO) {
        return driveMapper.selectPage(pageReqVO);
    }

    @Override
    public void deployDrive(String driverCode, String edgeCode) {

        List<DeviceDO> deviceDOList = deviceMapper.selectList(new LambdaQueryWrapper<DeviceDO>()
                .eq(DeviceDO::getDriverCode, driverCode)
                .eq(DeviceDO::getEdgeCode, edgeCode)
                .eq(DeviceDO::getStatus, 1)
                .eq(DeviceDO::getDeviceType, DeviceTypeEnum.DEVICE.getType())
                .eq(DeviceDO::getLinkState, LinkStateEnum.NO_ACTIVE.getType())
        );
        for(DeviceDO deviceDO : deviceDOList){
            if(DriveEnum.MODBUS.getType().equals(deviceDO.getDriverCode()) && NodeTypeEnum.EDGE.getType().equals(deviceDO.getNodeType())){
                EdgeChannelDO edgeChannel = edgeChannelService.getEdgeChannelByCode(deviceDO.getChannelCode());
                DeviceExtraReqVO deviceExtraReqVO = new DeviceExtraReqVO();
                deviceExtraReqVO.setDeviceExtra(deviceDO.getExtra());
                deviceExtraReqVO.setChannelExtra(edgeChannel.getExtra());
                deviceExtraReqVO.setConnectType(edgeChannel.getConnectType());
                deviceDO.setExtra(JSON.toJSONString(deviceExtraReqVO));
            }
        }
        mqttPublisher.publish("/iot/devices/edge/"+edgeCode+"/sync", JSON.toJSONString(deviceDOList));
    }

}