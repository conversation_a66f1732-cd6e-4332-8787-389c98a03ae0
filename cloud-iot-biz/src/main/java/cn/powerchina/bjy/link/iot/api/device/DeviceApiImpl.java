package cn.powerchina.bjy.link.iot.api.device;

import cn.hutool.core.collection.CollectionUtil;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.link.iot.api.devicegroup.dto.DeviceRespDTO;
import cn.powerchina.bjy.link.iot.dal.dataobject.device.DeviceDO;
import cn.powerchina.bjy.link.iot.service.device.DeviceService;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@RestController
@Validated
public class DeviceApiImpl implements DeviceApi {

    @Resource
    private DeviceService deviceService;

    public Map<Long, DeviceRespDTO> loadDevices(Collection<Long> deviceIds) {
        if (CollectionUtil.isEmpty(deviceIds)) {
            return Collections.emptyMap();
        }

        Map<Long, DeviceRespDTO> respDTOMap = deviceService.getDevices(deviceIds).values().stream()
                .map(o -> BeanUtils.toBean(o, DeviceRespDTO.class))
                .collect(Collectors.toMap(DeviceRespDTO::getId, Function.identity()));
        return fetchParentInfo(respDTOMap);
    }

    @Override
    public List<DeviceRespDTO> loadChildDevices(Collection<String> parentCodes) {
        if (CollectionUtil.isEmpty(parentCodes)) {
            return Collections.emptyList();
        }

        Map<Long, DeviceRespDTO> respDTOMap = deviceService.findDeviceByParentCode(parentCodes).stream().map(o -> BeanUtils.toBean(o, DeviceRespDTO.class)).collect(Collectors.toMap(DeviceRespDTO::getId, Function.identity()));
        return new ArrayList<>(fetchParentInfo(respDTOMap).values());
    }

    private Map<Long, DeviceRespDTO> fetchParentInfo(Map<Long, DeviceRespDTO> respDTOMap) {
        Set<String> parentCodes = respDTOMap.values().stream().map(DeviceRespDTO::getParentCode).filter(Objects::nonNull).collect(Collectors.toSet());
        if (CollectionUtil.isEmpty(parentCodes)) {
            return respDTOMap;
        }

        Map<String, DeviceDO> parentDeviceMap = deviceService.getDeviceByCodeList(new ArrayList<>(parentCodes)).stream().collect(Collectors.toMap(DeviceDO::getDeviceCode, Function.identity()));
        respDTOMap.values().forEach(respDTO -> {
            DeviceDO deviceParent = parentDeviceMap.get(respDTO.getParentCode());
            if (deviceParent != null) {
                respDTO.setParentName(deviceParent.getDeviceName());
                respDTO.setParentProductCode(deviceParent.getProductCode());
                respDTO.setParentSerial(deviceParent.getDeviceSerial());
            }
        });

        return respDTOMap;
    }
}
