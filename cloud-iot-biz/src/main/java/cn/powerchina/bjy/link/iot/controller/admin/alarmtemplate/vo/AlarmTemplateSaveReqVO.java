package cn.powerchina.bjy.link.iot.controller.admin.alarmtemplate.vo;

import cn.powerchina.bjy.link.iot.controller.admin.notificationmethod.vo.NotificationMethodSaveReqVO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;

@Schema(description = "管理后台 - 告警模板新增/修改 Request VO")
@Data
public class AlarmTemplateSaveReqVO {

    @Schema(description = "主键id",  example = "2457")
    private Long id;

    @Schema(description = "规则ID", example = "25655")
    private Long ruleId;

    @Schema(description = "动作id",  example = "25655")
    private Long actionId;

    @Schema(description = "告警名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @NotEmpty(message = "告警名称不能为空")
    private String alarmName;

    @Schema(description = "告警等级(1-轻微,2-中等,3-严重,4-非常严重)")
    private Integer alarmLevel;

    @Schema(description = "告警描述")
    private String alarmContent;

    @Schema(description = "排序", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer sort;

    @Valid
    @Schema(description = "通知方式表")
    private List<NotificationMethodSaveReqVO> notificationMethodSaveReqVOList;

}
