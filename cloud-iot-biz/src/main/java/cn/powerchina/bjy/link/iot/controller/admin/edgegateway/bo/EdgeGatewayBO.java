package cn.powerchina.bjy.link.iot.controller.admin.edgegateway.bo;

import cn.powerchina.bjy.link.iot.dal.dataobject.edgegateway.EdgeGatewayDO;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Description: 边缘网关BO
 * @Author: yhx
 * @CreateDate: 2024/8/15
 */
@Data
public class EdgeGatewayBO extends EdgeGatewayDO {

    private String extra;

    /**
     * 产品id
     */
    private Long productId;

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 资源空间名称
     */
    private String spaceName;

    /**
     * 密钥
     */
    private String deviceSecret;

    /**
     * 节点类型编码
     */
    private Integer nodeType;

    /**
     * 节点类型名称
     */
    private String nodeTypeCN;

    /**
     * 激活时间
     */
    private LocalDateTime activeTime;
}
