package cn.powerchina.bjy.link.iot.service.product;

import cn.hutool.core.lang.UUID;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.collection.CollectionUtils;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.iot.aop.product.ProductDataPermissionCheck;
import cn.powerchina.bjy.link.iot.common.role.RoleCommon;
import cn.powerchina.bjy.link.iot.controller.admin.mqttauth.vo.MqttAuthSaveReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.product.bo.ProductBO;
import cn.powerchina.bjy.link.iot.controller.admin.product.vo.ProductCountReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.product.vo.ProductPageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.product.vo.ProductSaveReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.productmodel.vo.ProductModelSaveReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.transportsource.vo.ProductModelRelayVO;
import cn.powerchina.bjy.link.iot.controller.admin.transportsource.vo.ProductRelayVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.device.DeviceDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.modelTemplate.ModelTemplateDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.modelTemplateDetails.ModelTemplateDetailsDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.product.ProductDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.resourcespace.ResourceSpaceDO;
import cn.powerchina.bjy.link.iot.dal.mysql.device.DeviceMapper;
import cn.powerchina.bjy.link.iot.dal.mysql.modelTemplate.ModelTemplateMapper;
import cn.powerchina.bjy.link.iot.dal.mysql.modelTemplateDetails.ModelTemplateDetailsMapper;
import cn.powerchina.bjy.link.iot.dal.mysql.product.ProductMapper;
import cn.powerchina.bjy.link.iot.enums.*;
import cn.powerchina.bjy.link.iot.service.mqttauth.MqttAuthService;
import cn.powerchina.bjy.link.iot.service.productmodel.ProductModelService;
import cn.powerchina.bjy.link.iot.service.resourcespace.ResourceSpaceService;
import cn.powerchina.bjy.link.iot.service.scenerule.SceneRuleService;
import cn.powerchina.bjy.link.iot.service.sceneruleaction.SceneRuleActionService;
import cn.powerchina.bjy.link.iot.service.sceneruletrigger.SceneRuleTriggerService;
import cn.powerchina.bjy.link.iot.service.tdengine.IotDevicePropertyService;
import cn.powerchina.bjy.link.iot.service.transportsource.TransportSourceService;
import cn.powerchina.bjy.link.iot.util.CodeGenerator;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import jakarta.annotation.Resource;
import jakarta.security.auth.message.AuthStatus;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.link.iot.enums.ErrorCodeConstants.*;

/**
 * 产品 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class ProductServiceImpl implements ProductService {

    @Resource
    private ProductMapper productMapper;

    @Resource
    private DeviceMapper deviceMapper;

    @Resource
    private ModelTemplateMapper modelTemplateMapper;

    @Resource
    private ModelTemplateDetailsMapper modelTemplateDetailsMapper;

    @Autowired
    private ResourceSpaceService resourceSpaceService;

    @Autowired
    private MqttAuthService mqttAuthService;

    @Resource
    private ProductModelService productModelService;

    @Resource
    private IotDevicePropertyService iotDevicePropertyService;

    @Resource
    private TransportSourceService transportSourceService;

    @Resource
    private SceneRuleTriggerService sceneRuleTriggerService;
    @Resource
    @Lazy
    private SceneRuleActionService sceneRuleActionService;
    @Resource
    @Lazy
    private SceneRuleService sceneRuleService;

    @Resource
    private RoleCommon roleCommon;

    @Override
    public Long createProduct(ProductSaveReqVO createReqVO) {
        // 插入
        ProductDO product = BeanUtils.toBean(createReqVO, ProductDO.class);
        if (StringUtils.isNotEmpty(createReqVO.getTemplateIds())) {
            String[] str = createReqVO.getTemplateIds().split(",");
            product.setTemplateId(Long.parseLong(str[2]));
        }
        // 校验产品名是否重复
        validateProductNameExists(product.getProductName(), product.getResourceSpaceId());

        // 填充产品编码
        product.setProductCode(CodeGenerator.createCode(SceneTypeEnum.PRODUCT.getPrefix()));

        // 设置秘钥
        product.setProductSecret(UUID.randomUUID().toString(true));

        // 设备限额
        product.setDeviceLimit(1000L);

        // 已使用限额
        product.setUsedQuota(0L);

        // 动态注册
        product.setDynamicRegister(1);

        // 预注册
        product.setPreRegister(1);

        productMapper.insert(product);

        // 创建产品对应的mqtt认证信息
        MqttAuthSaveReqVO mqttAuthSaveReqVO = new MqttAuthSaveReqVO();
        mqttAuthSaveReqVO.setUserName(product.getProductCode());
        mqttAuthSaveReqVO.setSecret(product.getProductSecret());
        mqttAuthSaveReqVO.setType(AuthTypeEnum.PRODUCT_TYPE.getType());
        mqttAuthSaveReqVO.setStatus(AuthStatusEnum.ENABLE.getType());
        mqttAuthService.createMqttAuth(mqttAuthSaveReqVO);

        try {
            //创建时序数据库超级表
            iotDevicePropertyService.defineDevicePropertyData(product);
        } catch (Exception e) {
            throw exception(PRODUCT_MODEL_TD_CREATE);
        }

        //组装转发数据
        ProductRelayVO productRelayVO = BeanUtils.toBean(product, ProductRelayVO.class);

        if (StringUtils.isNotEmpty(createReqVO.getTemplateIds())) {
            //同步模板中的属性，服务，事件到产品模型中
            List<ModelTemplateDetailsDO> list = modelTemplateDetailsMapper.selectListByTemplateId(product.getTemplateId());
            if (!CollectionUtils.isAnyEmpty(list)) {
                for (ModelTemplateDetailsDO modelTemplateDetailsDO : list) {
                    ProductModelSaveReqVO productModelSaveReqVO = BeanUtils.toBean(modelTemplateDetailsDO, ProductModelSaveReqVO.class);
                    productModelSaveReqVO.setProductCode(product.getProductCode());
                    productModelSaveReqVO.setThingIdentity(modelTemplateDetailsDO.getTemplateIdentity());
                    productModelSaveReqVO.setThingType(modelTemplateDetailsDO.getTemplateType());
                    productModelSaveReqVO.setThingName(modelTemplateDetailsDO.getTemplateDetailsName());
                    //productModelSaveReqVO.setInputParams(modelTemplateDetailsDO.getInputParams());
                    productModelSaveReqVO.setId(null);
                    if (!StringUtils.isEmpty(modelTemplateDetailsDO.getInputParams())) {
                        String inParams = modelTemplateDetailsDO.getInputParams()
                                .replaceAll("templateType", "thingType")
                                .replaceAll("templateDetailsName", "thingName")
                                .replaceAll("templateIdentity", "thingIdentity");
                        productModelSaveReqVO.setInputParams(inParams);
                    }
                    if (!StringUtils.isEmpty(modelTemplateDetailsDO.getOutputParams())) {

                        String outParams = modelTemplateDetailsDO.getOutputParams()
                                .replaceAll("templateType", "thingType")
                                .replaceAll("templateDetailsName", "thingName")
                                .replaceAll("templateIdentity", "thingIdentity");
                        productModelSaveReqVO.setOutputParams(outParams);
                    }

                    productModelSaveReqVO.setId(productModelService.createProductModel(productModelSaveReqVO));

                    productRelayVO.getProductModelList().add(BeanUtils.toBean(productModelSaveReqVO, ProductModelRelayVO.class));
                }
            }
        }
        //数据转发
        try {
            transportSourceService.dataForwarding(TransportSourceTypeEnum.PRODUCT_CREATE, productRelayVO);
        } catch (Exception e) {
            log.error("{}:", TRANSPORT_EXCEPTIOM.getMsg(), e);
        }
        // 返回
        return product.getId();
    }

    @Override
    public void updateProduct(ProductSaveReqVO updateReqVO) {
        // 校验存在
        ProductDO product = validateProductExists(updateReqVO.getId());
        // 校验是否修改节点类型
        validateProductNodeType(updateReqVO);
        // 校验产品是否重名
        validateProductNameExists(updateReqVO.getProductName(), updateReqVO.getResourceSpaceId(), updateReqVO.getId());
        // 更新
        ProductDO updateObj = BeanUtils.toBean(updateReqVO, ProductDO.class);
        if (StringUtils.isEmpty(updateReqVO.getTemplateIds())) {
            updateObj.setTemplateId(null);
        } else {
            String[] str = updateReqVO.getTemplateIds().split(",");
            updateObj.setTemplateId(Long.parseLong(str[2]));
        }
        productMapper.updateInfo(updateObj);
        //todo 通知大坝平台进行产品信息修改
        //数据转发
        try {
            updateObj.setProductCode(product.getProductCode());
            transportSourceService.dataForwarding(TransportSourceTypeEnum.PRODUCT_UPDATE, BeanUtils.toBean(updateObj, ProductRelayVO.class));
        } catch (Exception e) {
            log.error(TRANSPORT_EXCEPTIOM.getMsg() + ":", e);
        }
    }

    /**
     * 更新产品的设备限额
     * @param id 产品id
     * @param deviceLimit 设备限额
     */
    @Override
    public void updateDeviceLimit(Long id, Long deviceLimit) {
        ProductDO productDO = Optional.ofNullable(productMapper.selectById(id)).orElse(new ProductBO());
        if (Objects.nonNull(productDO.getId())) {
            Long usedQuota = productDO.getUsedQuota();
            if (deviceLimit>=usedQuota) {
                productDO.setDeviceLimit(deviceLimit);
                productMapper.updateById(productDO);
            } else {
                throw exception(PRODUCT_DEVICE_LIMIT_LESS_USED_QUOTA);
            }
        } else {
            throw exception(PRODUCT_NOT_EXISTS);
        }
    }

    @Override
    public void deleteProduct(Long id) {
        // 校验存在
        validateProductExists(id);
        ProductDO productDO = productMapper.selectById(id);
        // 校验产品是否已关联设备，关联设备后无法删除
        validateDeviceRelation(productDO.getProductCode());
        // 逻辑删除
        productMapper.deleteById(id);
        //场景失效
        this.invalidSceneRule(productDO.getProductCode());

        // 删除产品对应的mqtt认证信息
        mqttAuthService.deleteByUserName(productDO.getProductCode());

        //数据转发
        try {
            transportSourceService.dataForwarding(TransportSourceTypeEnum.PRODUCT_DELETE, BeanUtils.toBean(productDO, ProductRelayVO.class));
        } catch (Exception e) {
            log.error(TRANSPORT_EXCEPTIOM.getMsg() + ":", e);
        }
    }

    private ProductDO validateProductExists(Long id) {
        ProductDO product = productMapper.selectById(id);
        if (product == null) {
            throw exception(PRODUCT_NOT_EXISTS);
        }
        return product;
    }

    private void validateProductNodeType(ProductSaveReqVO updateReqVO) {
        ProductDO productDO = productMapper.selectById(updateReqVO.getId());
        if (productDO == null) {
            throw exception(PRODUCT_NOT_EXISTS);
        }
        if (productDO.getNodeType().equals(updateReqVO.getNodeType())) {
            return;
        }
        // 类型不一致，如果关联了设备则不允许修改
        QueryWrapper<DeviceDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(DeviceDO::getProductCode, productDO.getProductCode()).eq(DeviceDO::getDeleted, Boolean.FALSE);
        if (deviceMapper.selectCount(queryWrapper) > 0) {
            throw exception(PRODUCT_NODE_TYPE_UPDATE);
        }
    }

    /**
     * 根据产品名称校验产品是否存在
     * 新增名称唯一
     *
     * @param productName     产品名称
     * @param resourceSpaceId
     */
    private void validateProductNameExists(String productName, Long resourceSpaceId) {

        QueryWrapper<ProductDO> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(ProductDO::getProductName, productName).eq(ProductDO::getResourceSpaceId, resourceSpaceId).eq(ProductDO::getDeleted, Boolean.FALSE);
        if (productMapper.exists(wrapper)) {
            throw exception(PRODUCT_NAME_EXITS);
        }
    }

    /**
     * 根据产品名称校验产品是否存在
     * 根据产品名称查记录，如果不存在，则可修改；
     * 如果存在，正常情况是两种情况：
     * 1）修改了产品名称
     * 2）未修改产品名称
     * 校验ID是否相等，相等则可以修改，否则不可修改；
     * 不正常情况（基本不会出现）有多条相同名称的记录，拒绝修改
     *
     * @param productName 产品名称
     */
    private void validateProductNameExists(String productName, Long resourceSpaceId, Long id) {

        QueryWrapper<ProductDO> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(ProductDO::getProductName, productName).eq(ProductDO::getResourceSpaceId, resourceSpaceId).eq(ProductDO::getDeleted, Boolean.FALSE);
        List<ProductDO> productDOList = productMapper.selectList(wrapper);
        // 为空，说明改了产品名，且产品名未出现过
        if (CollectionUtils.isAnyEmpty(productDOList)) {
            return;
        }
        // 不为空，且size不为1，则说明有多个相同的产品名，不可更新
        if (productDOList.size() != 1) {
            throw exception(PRODUCT_NAME_EXITS);
        }
        // 只有一条记录，则校验是否为记录自身
        if (!productDOList.get(0).getId().equals(id)) {
            throw exception(PRODUCT_NAME_EXITS);
        }
    }

    /**
     * 校验产品是否已关联设备
     *
     * @param productCode 产品编码
     */
    private void validateDeviceRelation(String productCode) {
        QueryWrapper<DeviceDO> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(DeviceDO::getProductCode, productCode).eq(DeviceDO::getDeleted, Boolean.FALSE);
        Long deviceDOS = deviceMapper.selectCount(wrapper);
        if (deviceDOS > 0L) {
            throw exception(PRODUCT_REL_DEVICE);
        }
    }

    @Override
    public ProductDO getProduct(Long id) {
        return Objects.isNull(id) ? null : productMapper.selectById(id);
    }

    @Override
    public ProductBO getProductBO(Long id) {
        ProductDO productDO = getProduct(id);
        if (Objects.nonNull(productDO)) {
            ProductBO productBO = BeanUtils.toBean(productDO, ProductBO.class);
            setSpaceName(productBO);
            return productBO;
        }
        return null;
    }

    @ProductDataPermissionCheck
    @Override
    public PageResult<ProductBO> getProductPage(ProductPageReqVO pageReqVO) {
        PageResult<ProductDO> doPageResult = productMapper.selectPage(pageReqVO);
        PageResult<ProductBO> bOPageResult = BeanUtils.toBean(doPageResult, ProductBO.class);
        if (Objects.nonNull(bOPageResult) && !CollectionUtils.isAnyEmpty(bOPageResult.getList())) {
            bOPageResult.getList().stream().forEach(item -> {
                setSpaceName(item);
                if (item.getTemplateId() != null) {
                    ModelTemplateDO modelTemplateDO = modelTemplateMapper.selectById(item.getTemplateId());
                    StringBuffer sb = new StringBuffer();
                    if (modelTemplateDO != null) {
                        sb.append(modelTemplateDO.getCategorizeOneId());
                        sb.append(",");
                        sb.append(modelTemplateDO.getCategorizeTwoId());
                        sb.append(",");
                        sb.append(modelTemplateDO.getId());
                    }
                    item.setTemplateIds(sb.toString());
                }

            });
        }
        return bOPageResult;
    }

    @Override
    public ProductDO getProductByCode(String productCode) {
        LambdaQueryWrapperX<ProductDO> wrapperX = new LambdaQueryWrapperX<>();
        wrapperX.eq(ProductDO::getProductCode, productCode);
        return productMapper.selectOne(wrapperX);
    }

    @Override
    public ProductBO getProductBOByCode(String productCode) {
        LambdaQueryWrapperX<ProductDO> wrapperX = new LambdaQueryWrapperX<>();
        wrapperX.eq(ProductDO::getProductCode, productCode);
        ProductDO productDO = productMapper.selectOne(wrapperX);
        ProductBO productBO = BeanUtils.toBean(productDO, ProductBO.class);
        setSpaceName(productBO);
        return productBO;
    }

    @Override
    public Long countProduct(ProductCountReqVO reqVO) {
        Long count = productMapper.selectCount(new LambdaQueryWrapperX<ProductDO>()
                .geIfPresent(ProductDO::getCreateTime, reqVO.getStartDate())
                .leIfPresent(ProductDO::getCreateTime, reqVO.getEndDate()));
        return Objects.isNull(count) ? 0L : count;
    }

    @Override
    public List<ProductDO> getProductByCodes(List<String> productCodeList) {
        return productMapper.selectList(new LambdaQueryWrapperX<ProductDO>()
                .in(ProductDO::getProductCode, productCodeList));
    }

    /**
     * 设置资源空间名称
     *
     * @param bo
     */
    private void setSpaceName(ProductBO bo) {
        if (Objects.nonNull(bo) && Objects.nonNull(bo.getResourceSpaceId())) {
            if (Objects.equals(bo.getResourceSpaceId(), 0L)) {
                bo.setSpaceName("全部");
            } else {
                ResourceSpaceDO spaceDO = resourceSpaceService.getResourceSpace(bo.getResourceSpaceId());
                bo.setSpaceName(Objects.isNull(spaceDO) ? null : spaceDO.getSpaceName());
            }
        }
    }

//    /**
//     * 校验产品编辑时不能修改品类(模板ID)
//     *
//     * @param updateReqVO 更新请求VO
//     */
//    private void validateProductTemplateNotModified(ProductSaveReqVO updateReqVO) {
//        // 获取原产品数据
//        ProductDO existingProduct = productMapper.selectById(updateReqVO.getId());
//        if (existingProduct == null) {
//            throw exception(PRODUCT_NOT_EXISTS);
//        }
//
//        // 检查前端传入的模板ID是否与数据库中的一致
//        Long updateTemplateId = StringUtils.isNotEmpty(updateReqVO.getTemplateId())
//            ? Long.parseLong(updateReqVO.getTemplateId())
//            : null;
//
//        // 如果原产品有模板ID，且前端传入的与原来的不一致，则不允许修改
//        if (existingProduct.getTemplateId() != null) {
//            if (updateTemplateId == null || !existingProduct.getTemplateId().equals(updateTemplateId)) {
//                throw exception(PRODUCT_TEMPLATE_CANNOT_MODIFY);
//            }
//        }
//        // 如果原产品没有模板ID，但前端传入了，也不允许修改
//        else if (updateTemplateId != null) {
//            throw exception(PRODUCT_TEMPLATE_CANNOT_MODIFY);
//        }
//    }

    @Override
    public List<ProductDO> searchProductsByKeyword(String keyword, Long resourceSpaceId) {
        QueryWrapper<ProductDO> productQueryWrapper = new QueryWrapper<>();
        if (StringUtils.isNotBlank(keyword)) {
            productQueryWrapper.like("product_name", keyword);
        }
        productQueryWrapper.eq("deleted", false);
        productQueryWrapper.orderByDesc("create_time");
        if (Objects.nonNull(resourceSpaceId)) {
            productQueryWrapper.eq("resource_space_id", resourceSpaceId);
        }
        List<Long> resourceSpaceIds = roleCommon.getResourceSpaceIds();
        if (!CollectionUtils.isAnyEmpty(resourceSpaceIds)) {
            productQueryWrapper.in("resource_space_id", resourceSpaceIds);
        }
        return productMapper.selectList(productQueryWrapper);            // 按创建时间倒序
    }

    private void invalidSceneRule(String productCode) {
        //触发条件、限制条件场景失效
        List<Long> sceneRuleTriggerList = sceneRuleTriggerService.deleteSceneRuleTrigger(productCode, null);
        //执行动作失效
        List<Long> sceneRuleActionList = sceneRuleActionService.deleteSceneRuleAction(productCode, null);
        //场景联动失效
        List<Long> allRuleList = new ArrayList<>();
        allRuleList.addAll(sceneRuleTriggerList);
        allRuleList.addAll(sceneRuleActionList);
        List<Long> ruleList = allRuleList.stream().distinct().collect(Collectors.toList());
        sceneRuleService.invalidSceneRule(ruleList, RuleStateEnum.INVALID.getType());
    }

    @Override
    public void updateDynamicRegister(Long id, Integer dynamicRegister) {
        ProductDO updateObj = validateProductExists(id);
        updateObj.setDynamicRegister(dynamicRegister);
        productMapper.updateById(updateObj);
    }

    @Override
    public void updatePreRegister(Long id, Integer preRegister) {
        ProductDO updateObj = validateProductExists(id);
        updateObj.setPreRegister(preRegister);
        productMapper.updateById(updateObj);
    }
}

