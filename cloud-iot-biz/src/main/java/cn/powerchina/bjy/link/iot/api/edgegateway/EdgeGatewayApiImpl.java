package cn.powerchina.bjy.link.iot.api.edgegateway;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.link.iot.service.edgegateway.EdgeGatewayService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

/**
 * 边缘网关
 */
@RestController
@Validated
public class EdgeGatewayApiImpl implements EdgeGatewayApi {

    @Autowired
    private EdgeGatewayService edgeGatewayService;

    @Override
    public CommonResult<Boolean> detectEdgeGatewayOnlineStatus() {
        edgeGatewayService.detectEdgeGatewayOnlineStatus();
        return CommonResult.success(true);
    }
}
