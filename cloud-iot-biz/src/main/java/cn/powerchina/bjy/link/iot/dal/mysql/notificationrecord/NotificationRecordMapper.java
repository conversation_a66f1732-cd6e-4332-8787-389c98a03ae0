package cn.powerchina.bjy.link.iot.dal.mysql.notificationrecord;


import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.iot.controller.admin.notificationrecord.vo.NotificationRecordPageReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.notificationrecord.NotificationRecordDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 消息记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface NotificationRecordMapper extends BaseMapperX<NotificationRecordDO> {

    default PageResult<NotificationRecordDO> selectPage(NotificationRecordPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<NotificationRecordDO>()
                .likeIfPresent(NotificationRecordDO::getSubject, reqVO.getSubject())
                .likeIfPresent(NotificationRecordDO::getInbox, reqVO.getInbox())
                .eqIfPresent(NotificationRecordDO::getSendStatus, reqVO.getSendStatus())
                .eqIfPresent(NotificationRecordDO::getOutbox, reqVO.getOutbox())
                .betweenIfPresent(NotificationRecordDO::getSendTime, reqVO.getSendTime())
                .eqIfPresent(NotificationRecordDO::getEmailContent, reqVO.getEmailContent())
                .eqIfPresent(NotificationRecordDO::getRemark, reqVO.getRemark())
                .betweenIfPresent(NotificationRecordDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(NotificationRecordDO::getId));
    }

}