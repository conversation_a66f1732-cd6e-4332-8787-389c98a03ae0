package cn.powerchina.bjy.link.iot.util;

import cn.powerchina.bjy.link.iot.controller.admin.alarmtemplate.vo.AlarmMsgRespVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.notificationmethod.NotificationMethodDO;
import cn.powerchina.bjy.link.iot.enums.ErrorCodeConstants;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import java.util.Objects;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;

/**
 * @Description: 邮件发送工具类
 * @Author: handl
 * @CreateDate: 2025/6/03
 */
@Slf4j
public class DingTalkUtils {

    public static void send(NotificationMethodDO notificationMethoDo, AlarmMsgRespVO alarmMsgRespVO) {

        String dingWebhook = notificationMethoDo.getNotificationAccount();
        try {
            if (Objects.isNull(dingWebhook)) {
                throw exception(ErrorCodeConstants.NOTIFICATION_ACCOUNT_NOT_EXISTS);
            }
        } catch (Exception e) {
            log.error("钉钉账号错误 {}", e.getMessage());
        }
        String content = buildContent(alarmMsgRespVO);
        CloseableHttpClient httpClient = HttpClients.createDefault();
        try {
            HttpPost httpPost = new HttpPost(dingWebhook);
            httpPost.addHeader("Content-Type", "application/json; charset=utf-8");

            String jsonMsg = "{\"msgtype\": \"text\",\"text\": {\"content\":\"%s\"}}";
            StringEntity entity = new StringEntity(String.format(jsonMsg, content), "utf-8");
            httpPost.setEntity(entity);

            HttpResponse response = httpClient.execute(httpPost);
            String result = EntityUtils.toString(response.getEntity());
            log.info("钉钉告警消息发送成功 result：{}", result);
        } catch (Exception e) {
            log.error("钉钉告警消息发送失败 {}", e.getMessage());
        }
    }

    public static String buildContent(AlarmMsgRespVO alarmMsgRespVO) {
        StringBuilder sb = new StringBuilder("告警信息：\n")
                .append("【告警等级】").append(alarmMsgRespVO.getAlarmLevel()).append("\n");
        if (StringUtil.isNotBlank(alarmMsgRespVO.getDeviceName())) {
            sb.append("【触发设备】").append(alarmMsgRespVO.getDeviceName()).append("\n");
        }else {
            sb.append("【触发设备】").append("无设备信息").append("\n");
        }
        return sb.append("【告警名称】").append(alarmMsgRespVO.getAlarmContent())
                .toString();
    }


}
