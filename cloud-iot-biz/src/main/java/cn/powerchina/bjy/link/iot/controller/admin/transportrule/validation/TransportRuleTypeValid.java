package cn.powerchina.bjy.link.iot.controller.admin.transportrule.validation;

import com.alibaba.nacos.api.grpc.auto.Payload;
import jakarta.validation.Constraint;

import java.lang.annotation.*;

@Documented
@Constraint(validatedBy = TransportRuleTypeValidator.class)
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@interface TransportRuleTypeValid {
    String message() default "转发规则参数校验失败";
    Class<?>[] groups() default {};
    Class<? extends Payload>[] payload() default {};
}
