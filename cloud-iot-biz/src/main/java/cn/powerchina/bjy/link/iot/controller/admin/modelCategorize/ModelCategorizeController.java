package cn.powerchina.bjy.link.iot.controller.admin.modelCategorize;

import cn.powerchina.bjy.cloud.framework.common.enums.CommonStatusEnum;
import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.link.iot.controller.admin.modelCategorize.bo.ModelCategorizeBO;
import cn.powerchina.bjy.link.iot.controller.admin.modelCategorize.vo.ModelCategorizeListReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.modelCategorize.vo.ModelCategorizePageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.modelCategorize.vo.ModelCategorizeRespVO;
import cn.powerchina.bjy.link.iot.controller.admin.modelCategorize.vo.ModelCategorizeSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.modelCategorize.ModelCategorizeDO;
import cn.powerchina.bjy.link.iot.model.TreeSelect;
import cn.powerchina.bjy.link.iot.service.modelCategorize.ModelCategorizeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;


/**
 * 物模型分类信息表
 *
 * <AUTHOR>
 * @date 2025-03-25 11:00:21
 */
@Tag(name = "管理后台 - 物模板分类")
@RestController
@RequestMapping("/iot/modelCategorize")
@Validated
public class ModelCategorizeController {
    @Autowired
    private ModelCategorizeService modelCategorizeService;

    @GetMapping("/page")
    @Operation(summary = "获得物模板分类分页")
    public CommonResult<PageResult<ModelCategorizeRespVO>> getModelCategorizePage(@Valid ModelCategorizePageReqVO pageReqVO) {
        PageResult<ModelCategorizeBO> pageResult = modelCategorizeService.getModelCategorizePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ModelCategorizeRespVO.class));
    }
    @GetMapping("/getAllList")
    @Operation(summary = "获取物模板分类列表", description = "只包含被开启的部门，主要用于前端的下拉选项")
    public CommonResult<List<ModelCategorizeRespVO>> getAllList(ModelCategorizeListReqVO reqVO) {
        List<ModelCategorizeDO> list = modelCategorizeService.getAllList(reqVO);
        return success(BeanUtils.toBean(list, ModelCategorizeRespVO.class));
    }
    @GetMapping("/treeselect")
    @Operation(summary = "获取分类下拉树列表", description = "获取分类下拉树列表")
    public CommonResult<List<TreeSelect>> treeselect(ModelCategorizeListReqVO reqVO) {
        List<ModelCategorizeDO> list = modelCategorizeService.getAllList(reqVO);
        List<ModelCategorizeRespVO> modelCategorizeRespVOList=BeanUtils.toBean(list, ModelCategorizeRespVO.class);
        return success(modelCategorizeService.buildMenuTreeSelect(modelCategorizeRespVOList));
    }
    @GetMapping("/treeselectList")
    @Operation(summary = "获取产品品类分类列表", description = "获取产品品类分类列表")
    public CommonResult<List<TreeSelect>> treeselectList(ModelCategorizeListReqVO reqVO) {
        List<ModelCategorizeDO> list = modelCategorizeService.getAllLists(reqVO);
        List<ModelCategorizeRespVO> modelCategorizeRespVOList=BeanUtils.toBean(list, ModelCategorizeRespVO.class);
        return success(modelCategorizeService.buildMenuTreeSelect(modelCategorizeRespVOList));
    }
    @GetMapping("/getListByCategorizeId")
    @Operation(summary = "根据一级分类ID获取二级物模板分类列表", description = "只包含被开启的部门，主要用于前端的下拉选项")
    public CommonResult<List<ModelCategorizeRespVO>> getListByCategorizeId(@RequestParam("id") Long id) {
        List<ModelCategorizeDO> list = modelCategorizeService.getListByCategorizeId(id);
        return success(BeanUtils.toBean(list, ModelCategorizeRespVO.class));
    }
    @GetMapping("/get")
    @Operation(summary = "获得物模板分类")
    @Parameter(name = "id", description = "编号", required = true)
    public CommonResult<ModelCategorizeRespVO> getModelCategorize(@RequestParam("id") Long id) {
        ModelCategorizeBO modelCategorizeBO = modelCategorizeService.getModelCategorizeBO(id);
        ModelCategorizeRespVO result = BeanUtils.toBean(modelCategorizeBO, ModelCategorizeRespVO.class);
        return success(result);
    }
    @PostMapping("/create")
    @Operation(summary = "创建物模板分类")
    public CommonResult<Long> createModelCategorize(@Valid @RequestBody ModelCategorizeSaveReqVO createReqVO) {
        return success(modelCategorizeService.createModelCategorize(createReqVO));
    }
    @PutMapping("/update")
    @Operation(summary = "更新物模板分类")
    public CommonResult<Boolean> updateModelCategorize(@Valid @RequestBody ModelCategorizeSaveReqVO updateReqVO) {
        modelCategorizeService.updateModelCategorize(updateReqVO);
        return success(true);
    }
    @DeleteMapping("/delete")
    @Operation(summary = "删除物模板分类")
    @Parameter(name = "id", description = "编号", required = true)
    public CommonResult<Boolean> deleteModelCategorize(@RequestParam("id") Long id) {
        modelCategorizeService.deleteModelCategorize(id);
        return success(true);
    }

}
