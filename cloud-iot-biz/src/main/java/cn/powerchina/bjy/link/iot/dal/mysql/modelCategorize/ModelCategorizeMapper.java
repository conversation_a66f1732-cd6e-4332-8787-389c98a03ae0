package cn.powerchina.bjy.link.iot.dal.mysql.modelCategorize;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.iot.controller.admin.modelCategorize.vo.ModelCategorizeListReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.modelCategorize.vo.ModelCategorizePageReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.modelCategorize.ModelCategorizeDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface ModelCategorizeMapper extends BaseMapperX<ModelCategorizeDO> {
    default PageResult<ModelCategorizeDO> selectPage(ModelCategorizePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ModelCategorizeDO>()
                .likeIfPresent(ModelCategorizeDO::getCategorizeName, reqVO.getCategorizeName())
                .eqIfPresent(ModelCategorizeDO::getState, reqVO.getState())
                .betweenIfPresent(ModelCategorizeDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ModelCategorizeDO::getId));
    }
    default ModelCategorizeDO selectByParentIdAndName(Long parentId, String name) {
        return selectOne(ModelCategorizeDO::getParentId, parentId, ModelCategorizeDO::getCategorizeName, name);
    }
    default Long selectCountByParentId(Long parentId) {
        return selectCount(ModelCategorizeDO::getParentId, parentId);
    }
    default List<ModelCategorizeDO> selectList(ModelCategorizeListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<ModelCategorizeDO>()
                .likeIfPresent(ModelCategorizeDO::getCategorizeName, reqVO.getCategorizeName())
                .eqIfPresent(ModelCategorizeDO::getLevel, reqVO.getLevel())
                .eqIfPresent(ModelCategorizeDO::getState, reqVO.getState())
                .betweenIfPresent(ModelCategorizeDO::getCreateTime, reqVO.getCreateTime()).orderByDesc(ModelCategorizeDO::getId));
    }
    default List<ModelCategorizeDO> selectListByCategorizeId(Long id) {
        return selectList(new LambdaQueryWrapperX<ModelCategorizeDO>()
                .eqIfPresent(ModelCategorizeDO::getParentId, id));
    }

}
