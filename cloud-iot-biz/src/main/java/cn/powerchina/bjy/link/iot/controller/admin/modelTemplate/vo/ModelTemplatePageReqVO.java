package cn.powerchina.bjy.link.iot.controller.admin.modelTemplate.vo;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.models.security.SecurityScheme;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 物模板分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ModelTemplatePageReqVO extends PageParam {

    /**
     * 物模板分类父ID
     */
    @Schema(description = "物模板二级分类父ID")
    private Long categorizeTwoId;

    @Schema(description = "物模板一级分类父ID")
    private Long categorizeOneId;

    @Schema(description = "物模板分类等级")
    private Integer level;
    /**
     * 物模板名称
     */
    @Schema(description = "物模板名称")
    private String templateName;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;
}
