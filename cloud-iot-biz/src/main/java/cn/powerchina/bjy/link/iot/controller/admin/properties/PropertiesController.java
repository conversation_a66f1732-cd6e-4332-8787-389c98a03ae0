package cn.powerchina.bjy.link.iot.controller.admin.properties;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.link.iot.service.properties.PropertiesService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

/**
 * 属性采集
 * 主要用于测试
 *
 * <AUTHOR>
 **/
@Tag(name = "管理后台 - 产品物模型属性获取")
@RestController
@RequestMapping("/iot/product-property")
@Validated
public class PropertiesController {

    @Resource
    private PropertiesService propertiesService;

    @GetMapping("/get")
    @Operation(summary = "获得产品物模型属性值")
    @Parameter(name = "deviceCode", description = "设备编号", required = true)
    @Parameter(name = "mcuChannelList", description = "通道集合", required = false)
//    @PreAuthorize("@ss.hasPermission('iot:product-property:query')")
    public CommonResult<Boolean> getProductModel(@RequestParam("deviceCode") String deviceCode,
                                                 @RequestParam(value = "mcuChannelList", required = false) List<String> mcuChannelList) {
        propertiesService.fetchEdgeProperties(deviceCode, mcuChannelList);
        return success(true);
    }
}
