package cn.powerchina.bjy.link.iot.service.productmodel;

import cn.powerchina.bjy.cloud.framework.common.util.json.JsonUtils;
import cn.powerchina.bjy.link.iot.controller.admin.productmodel.vo.ProductModelSaveReqVO;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static cn.powerchina.bjy.link.iot.enums.ErrorCodeConstants.PRODUCT_MODEL_PARAM_IDENTIFY_EXISTS;
import static cn.powerchina.bjy.link.iot.enums.ErrorCodeConstants.PRODUCT_MODEL_PARAM_NAME_EXISTS;
import org.springframework.stereotype.Service;
import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
/**
 * 事件部分判重校验
 *
 * <AUTHOR>
 **/
@Component
public class EventRepeatCheck extends RepeatCheck {

    /**
     * 物模型名称key
     */
    private final static String NAME_KEY = "thingName";
    /**
     * 物模型标识符key
     */
    private final static String IDENTIFY_KEY = "thingIdentity";

    @Override
    protected void validateCustom(ProductModelSaveReqVO reqVO) {
        String input = reqVO.getInputParams();
        if (StringUtils.isNotBlank(input)) {
            List<JSONObject> jsonObjectList = JsonUtils.parseObject(input, new TypeReference<>() {
            });
            paramCheck(jsonObjectList);
        }
        String output = reqVO.getOutputParams();
        if (StringUtils.isNotBlank(output)) {
            List<JSONObject> jsonObjectList = JsonUtils.parseObject(output, new TypeReference<>() {
            });
            paramCheck(jsonObjectList);
        }
    }

    /**
     * 内存操作
     *
     * @param jsonObjectList 参数列表
     */
    private void paramCheck(List<JSONObject> jsonObjectList) {

        Set<String> name = new HashSet<>(64);
        Set<String> identify = new HashSet<>(64);
        jsonObjectList.forEach(item -> {
            String modelName = item.getString(NAME_KEY);
            if (StringUtils.isNotBlank(modelName) && name.contains(modelName)) {
                throw exception(PRODUCT_MODEL_PARAM_NAME_EXISTS);
            } else {
                name.add(modelName);
            }
            String modelIdentify = item.getString(IDENTIFY_KEY);
            if (StringUtils.isNotBlank(modelIdentify) && identify.contains(modelIdentify)) {
                throw exception(PRODUCT_MODEL_PARAM_IDENTIFY_EXISTS);
            } else {
                identify.add(modelIdentify);
            }
        });
    }
}
