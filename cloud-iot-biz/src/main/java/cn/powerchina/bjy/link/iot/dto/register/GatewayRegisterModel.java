package cn.powerchina.bjy.link.iot.dto.register;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 设备上下线
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GatewayRegisterModel implements Serializable {

    private String deviceId;
    private String deviceName;
    private String productId;
    private Long reportTime;
    private String edgeCode;
    private List<GatewayRegisterModel> subDevices;

}
