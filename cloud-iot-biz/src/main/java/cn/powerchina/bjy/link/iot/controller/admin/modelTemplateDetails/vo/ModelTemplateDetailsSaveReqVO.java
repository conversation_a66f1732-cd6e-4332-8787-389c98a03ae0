package cn.powerchina.bjy.link.iot.controller.admin.modelTemplateDetails.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(description = "管理后台 - 物模板明细新增/修改 Request VO")
@Data
public class ModelTemplateDetailsSaveReqVO {

    @Schema(description = "主键", example = "3695")
    private Long id;

    @Schema(description = "物模板ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "物模板ID不能为空")
    private String templateId;

    @Schema(description = "物模板明细名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String templateDetailsName;

    @Schema(description = "物模板标识符", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "物模板标识符不能为空")
    private String templateIdentity;

    @Schema(description = "物模板类型，1-属性；2-服务；3-事件；", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "物模板类型，1-属性；2-服务；3-事件；不能为空")
    private Integer templateType;

    @Schema(description = "数据类型（integer、decimal、string、bool、array、enum）", example = "2")
    private String datatype;

    @Schema(description = "读写类型，thing_type为1时必填，1-读写；2-只读；3-只写", example = "1")
    private Integer readWriteType;

    @Schema(description = "事件类型，thing_type为3时必填，1-信息；2告警；3-故障", example = "2")
    private Integer eventType;

    @Schema(description = "输入参数")
    private String inputParams;

    @Schema(description = "输出参数")
    private String outputParams;

    @Schema(description = "属性扩展信息")
    private String extra;

    @Schema(description = "描述", example = "")
    private String remark;
}
