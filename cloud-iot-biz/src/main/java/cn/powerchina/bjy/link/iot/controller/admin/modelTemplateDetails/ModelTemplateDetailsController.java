package cn.powerchina.bjy.link.iot.controller.admin.modelTemplateDetails;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.link.iot.controller.admin.modelTemplateDetails.bo.ModelTemplateDetailsBO;
import cn.powerchina.bjy.link.iot.controller.admin.modelTemplateDetails.vo.ModelTemplateDetailsPageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.modelTemplateDetails.vo.ModelTemplateDetailsRespVO;
import cn.powerchina.bjy.link.iot.controller.admin.modelTemplateDetails.vo.ModelTemplateDetailsSaveReqVO;
import cn.powerchina.bjy.link.iot.service.modelTemplateDetails.ModelTemplateDetailsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;


/**
 * 物模板信息表
 *
 * <AUTHOR>
 * @date 2025-03-25 15:52:13
 */
@Tag(name = "管理后台 - 物模板明细")
@RestController
@RequestMapping("/iot/modelTemplateDetails")
@Validated
public class ModelTemplateDetailsController {
    @Autowired
    private ModelTemplateDetailsService modelTemplateDetailsService;

    @GetMapping("/page")
    @Operation(summary = "获得物模板明细分页")
    public CommonResult<PageResult<ModelTemplateDetailsRespVO>> getModelTemplatePage(@Valid ModelTemplateDetailsPageReqVO pageReqVO) {
        PageResult<ModelTemplateDetailsBO> pageResult = modelTemplateDetailsService.getModelTemplateDetailsPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ModelTemplateDetailsRespVO.class));
    }
    @GetMapping("/get")
    @Operation(summary = "获得物模板明细")
    @Parameter(name = "id", description = "编号", required = true)
    public CommonResult<ModelTemplateDetailsRespVO> getModelTemplate(@RequestParam("id") Long id) {
        ModelTemplateDetailsBO modelTemplateDetailsBO = modelTemplateDetailsService.getModelTemplateDetailsBO(id);
        ModelTemplateDetailsRespVO result = BeanUtils.toBean(modelTemplateDetailsBO, ModelTemplateDetailsRespVO.class);
        return success(result);
    }
    @PostMapping("/create")
    @Operation(summary = "创建物模板明细")
    public CommonResult<Long> createModelTemplate(@Valid @RequestBody ModelTemplateDetailsSaveReqVO createReqVO) {
        return success(modelTemplateDetailsService.createModelTemplateDetails(createReqVO));
    }
    @PutMapping("/update")
    @Operation(summary = "更新物模板明细")
    public CommonResult<Boolean> updateModelTemplate(@Valid @RequestBody ModelTemplateDetailsSaveReqVO updateReqVO) {
        modelTemplateDetailsService.updateModelTemplateDetails(updateReqVO);
        return success(true);
    }
    @DeleteMapping("/delete")
    @Operation(summary = "删除物模板明细")
    @Parameter(name = "id", description = "编号", required = true)
    public CommonResult<Boolean> deleteModelTemplate(@RequestParam("id") Long id) {
        modelTemplateDetailsService.deleteModelTemplateDetails(id);
        return success(true);
    }

}
