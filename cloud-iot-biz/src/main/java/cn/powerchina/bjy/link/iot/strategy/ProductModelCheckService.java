package cn.powerchina.bjy.link.iot.strategy;

import cn.powerchina.bjy.cloud.framework.common.util.json.JsonUtils;
import cn.powerchina.bjy.link.iot.dto.up.EdgeReadPropertyValue;
import com.fasterxml.jackson.core.type.TypeReference;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 物模型相关校验 service
 *
 * <AUTHOR>
 **/
@Slf4j
@Service
public class ProductModelCheckService {

    private final static String BEAN_SUF = "Strategy";

    @Value("${bjylink.dict-to-bean:{\"INT\":\"int\",\"STRING\":\"string\", \"DATE\":\"date\", \"ENUM\":\"enum\", \"FLOAT\":\"float\", \"DOUBLE\":\"double\", \"BOOL\":\"bool\"}}")
    private String dictToBeanName;

    @Resource
    private Map<String, ProductModelStrategy> strategyMap;

    /**
     * 校验context，如果不符合规则则忽略（返回true），符合规则则不能忽略（返回false）
     * 入参context需提前进行类型转换，如int类型，入参为int值而非字符串,shouldIgnore(2, "");
     *
     * @param type     物模型数据类型
     * @param context  具体内容
     * @param strategy 规则
     * @return true - 忽略
     * false - 正常数据
     */
    public boolean shouldIgnore(String type, Object context, String strategy) {
        ProductModelStrategy productModelStrategy = strategyMap.get(getBeanName(type));
        if (null == productModelStrategy) {
            return false;
        }
        return productModelStrategy.shouldIgnore(context, strategy);
    }

    /**
     * 校验context，如果不符合规则则忽略（返回true），符合规则则不能忽略（返回false）
     * 入参context无需提前进行类型转换，如int类型，入参为字符串而非int值，shouldIgnore("2", "");
     *
     * @param type     物模型数据类型
     * @param context  具体内容
     * @param strategy 规则
     * @return true - 忽略
     * false - 正常数据
     */
    public boolean shouldIgnore(String type, String context, String strategy, EdgeReadPropertyValue.EdgeDevicePropertyValueDTO valueDTO) {
        ProductModelStrategy productModelStrategy = strategyMap.get(getBeanName(type));
        if (null == productModelStrategy) {
            return false;
        }
        return productModelStrategy.shouldIgnore(context, strategy, valueDTO);
    }

    private String getBeanName(String type) {
        if (StringUtils.isBlank(dictToBeanName)) {
            log.warn("没有数据类型相关配置【bjylink.dict-to-bean】");
            return "";
        }
        try {
            Map<String, String> relation = JsonUtils.parseObject(dictToBeanName, new TypeReference<Map<String, String>>() {
            });

            return relation.get(type) + BEAN_SUF;
        } catch (Exception e) {
            log.error("数据类型相关配置转换异常，配置【{}】", dictToBeanName, e);
        }
        return "";
    }
}
