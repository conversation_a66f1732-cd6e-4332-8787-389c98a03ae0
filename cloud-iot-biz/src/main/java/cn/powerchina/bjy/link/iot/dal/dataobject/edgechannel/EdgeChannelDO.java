package cn.powerchina.bjy.link.iot.dal.dataobject.edgechannel;

import cn.powerchina.bjy.cloud.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.*;

/**
 * 通道 DO
 *
 * <AUTHOR>
 */
@TableName("iot_edge_channel")
@KeySequence("iot_edge_channel_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EdgeChannelDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 关联的驱动code
     */
    private String driverCode;
    /**
     * 关联的边缘实例code
     */
    private String edgeCode;
    /**
     * 连接方式（1-RTU；2-TCP）
     */
    private Integer connectType;
    /**
     * 通道编码
     */
    private String channelCode;
    /**
     * 通道名称
     */
    private String channelName;
    /**
     * 差异化扩展
     */
    private String extra;

}