package cn.powerchina.bjy.link.iot.service.modelTemplate;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.link.iot.controller.admin.modelTemplate.bo.ModelTemplateBO;
import cn.powerchina.bjy.link.iot.controller.admin.modelTemplate.vo.ModelTemplatePageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.modelTemplate.vo.ModelTemplateSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.modelTemplate.ModelTemplateDO;
import jakarta.validation.Valid;

/**
 * 物模型分类信息表
 *
 * <AUTHOR>
 * @date 2025-03-25 11:00:21
 */
public interface ModelTemplateService {

    /**
     * 获得产品分页
     *
     * @param pageReqVO 分页查询
     * @return 产品分页
     */
    PageResult<ModelTemplateBO> getModelTemplatePage(ModelTemplatePageReqVO pageReqVO);

    /**
     * 获得产品
     *
     * @param id 编号
     * @return 产品
     */
    ModelTemplateDO getModelTemplate(Long id);

    /**
     * 获得产品
     *
     * @param id 编号
     * @return 产品
     */
    ModelTemplateBO getModelTemplateBO(Long id);
    /**
     * 创建产品
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createModelTemplate(@Valid ModelTemplateSaveReqVO createReqVO);

    /**
     * 更新产品
     *
     * @param updateReqVO 更新信息
     */
    void updateModelTemplate(@Valid ModelTemplateSaveReqVO updateReqVO);

    void updateModelTemplate(ModelTemplateDO updateModelCategorizeDO);

    /**
     * 删除产品
     *
     * @param id 编号
     */
    void deleteModelTemplate(Long id);

}

