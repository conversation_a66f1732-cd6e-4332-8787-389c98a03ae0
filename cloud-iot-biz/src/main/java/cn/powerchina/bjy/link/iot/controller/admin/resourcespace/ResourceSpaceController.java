package cn.powerchina.bjy.link.iot.controller.admin.resourcespace;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.link.iot.controller.admin.resourcespace.vo.ResourceSpacePageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.resourcespace.vo.ResourceSpaceRespVO;
import cn.powerchina.bjy.link.iot.controller.admin.resourcespace.vo.ResourceSpaceSaveReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.resourcespace.vo.ResourceSpaceStateReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.resourcespace.ResourceSpaceDO;
import cn.powerchina.bjy.link.iot.service.resourcespace.ResourceSpaceService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 资源空间")
@RestController
@RequestMapping("/iot/resource-space")
@Validated
public class ResourceSpaceController {

    @Resource
    private ResourceSpaceService resourceSpaceService;

    @PostMapping("/create")
    @Operation(summary = "创建资源空间")
//    @PreAuthorize("@ss.hasPermission('iot:resource-space:create')")
    public CommonResult<Long> createResourceSpace(@Valid @RequestBody ResourceSpaceSaveReqVO createReqVO) {
        return success(resourceSpaceService.createResourceSpace(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新资源空间")
//    @PreAuthorize("@ss.hasPermission('iot:resource-space:update')")
    public CommonResult<Boolean> updateResourceSpace(@Valid @RequestBody ResourceSpaceSaveReqVO updateReqVO) {
        resourceSpaceService.updateResourceSpace(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除资源空间")
    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('iot:resource-space:delete')")
    public CommonResult<Boolean> deleteResourceSpace(@RequestParam("id") Long id) {
        resourceSpaceService.deleteResourceSpace(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得资源空间")
    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('iot:resource-space:query')")
    public CommonResult<ResourceSpaceRespVO> getResourceSpace(@RequestParam("id") Long id) {
        ResourceSpaceDO resourceSpace = resourceSpaceService.getResourceSpace(id);
        return success(BeanUtils.toBean(resourceSpace, ResourceSpaceRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得资源空间分页")
//    @PreAuthorize("@ss.hasPermission('iot:resource-space:query')")
    public CommonResult<PageResult<ResourceSpaceRespVO>> getResourceSpacePage(@Valid ResourceSpacePageReqVO pageReqVO) {
        PageResult<ResourceSpaceDO> pageResult = resourceSpaceService.getResourceSpacePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ResourceSpaceRespVO.class));
    }

    @PostMapping("/switch/state")
    @Operation(summary = "切换资源空间启用状态")
//    @PreAuthorize("@ss.hasPermission('iot:resource-space:switch')")
    public CommonResult<Boolean> switchResourceSpaceState(@Valid @RequestBody ResourceSpaceStateReqVO switchStateReqVO) {
        return success(resourceSpaceService.switchResourceSpaceState(switchStateReqVO.getId(), switchStateReqVO.getState()));
    }

}