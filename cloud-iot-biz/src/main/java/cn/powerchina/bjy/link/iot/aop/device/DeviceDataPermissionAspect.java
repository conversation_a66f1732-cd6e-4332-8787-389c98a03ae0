package cn.powerchina.bjy.link.iot.aop.device;

import cn.powerchina.bjy.cloud.framework.common.exception.enums.GlobalErrorCodeConstants;
import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.cloud.framework.web.core.util.WebFrameworkUtils;
import cn.powerchina.bjy.cloud.system.api.permission.PermissionApi;
import cn.powerchina.bjy.cloud.system.api.permission.dto.RolePageRespDTO;
import cn.powerchina.bjy.cloud.system.enums.permission.DataScopeEnum;
import cn.powerchina.bjy.link.iot.common.role.RoleCommon;
import cn.powerchina.bjy.link.iot.controller.admin.device.vo.DeviceAndProductVO;
import cn.powerchina.bjy.link.iot.controller.admin.device.vo.DevicePageReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.datapermissions.DataPermissionsDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.device.DeviceDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.product.ProductDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.roledatapermissions.RoleDataPermissionsDO;
import cn.powerchina.bjy.link.iot.dal.mysql.datapermissions.DataPermissionsMapper;
import cn.powerchina.bjy.link.iot.dal.mysql.device.DeviceMapper;
import cn.powerchina.bjy.link.iot.dal.mysql.product.ProductMapper;
import cn.powerchina.bjy.link.iot.dal.mysql.roledatapermissions.RoleDataPermissionsMapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import jakarta.annotation.Resource;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Aspect
@Component
public class DeviceDataPermissionAspect {

    @Resource
    private RoleCommon roleCommon;

    @Resource
    private PermissionApi permissionApi;

    @Resource
    private RoleDataPermissionsMapper roleDataPermissionsMapper;

    @Resource
    private DataPermissionsMapper dataPermissionsMapper;

    @Resource
    private ProductMapper productMapper;

    @Resource
    private DeviceMapper deviceMapper;


    @Around("@annotation(deviceDataPermissionCheck)")
    public Object checkDeviceDataPermission(ProceedingJoinPoint joinPoint, DeviceDataPermissionCheck deviceDataPermissionCheck) throws Throwable {
        //获取方法参数
        Object[] args = joinPoint.getArgs();
        DevicePageReqVO pageReqVO = (DevicePageReqVO) args[0];

        //检查是否是超级管理员
        boolean superAdmin = roleCommon.checkIfSuperAdmin();
        if (!superAdmin) {
            //处理设备数据权限
            List<String> ids = processDataPermissions(pageReqVO);
            if (CollectionUtils.isEmpty(ids)) {
                return new PageResult<>(Collections.emptyList(), 0L);
            }
            pageReqVO.setCodes(ids);
        }
        //继续执行原方法
        return joinPoint.proceed(args);
    }

    private List<String> processDataPermissions(DevicePageReqVO pageReqVO) {
        List<String> ids = new ArrayList<>();
        CommonResult<List<RolePageRespDTO>> result = permissionApi.getPermissionRoleByUserId(WebFrameworkUtils.getLoginUserId());
        if (GlobalErrorCodeConstants.SUCCESS.getCode().equals(result.getCode())) {
            List<RolePageRespDTO> rolePageRespDTOList = result.getData();
            rolePageRespDTOList.forEach(role -> {
                RoleDataPermissionsDO roleDataPermissionsDO = roleDataPermissionsMapper.selectAllByRoleId(role.getId());
                if (roleDataPermissionsDO != null) {
                    if (DataScopeEnum.DEPT_CUSTOM.getScope().equals(roleDataPermissionsDO.getDataScope())) {
                        List<DataPermissionsDO> doList = dataPermissionsMapper.selectListByRoleId(roleDataPermissionsDO.getRoleId(), 1);
                        if (!CollectionUtils.isEmpty(doList)) {
                            doList.forEach(resource -> {
                                List<DataPermissionsDO> doList1 = dataPermissionsMapper.selectByRoleAndParentId(roleDataPermissionsDO.getRoleId(), resource.getDataId(), 2);
                                if (!CollectionUtils.isEmpty(doList1)) {
                                    QueryWrapper<ProductDO> queryWrapper = new QueryWrapper<>();
                                    queryWrapper.eq("resource_space_id", resource.getDataId());
                                    List<ProductDO> productList = productMapper.selectList(queryWrapper);
                                    if (!CollectionUtils.isEmpty(productList)) {
                                        productList.forEach(product -> {
                                            List<DeviceDO> deviceList = deviceMapper.selectList(new LambdaQueryWrapperX<DeviceDO>().eqIfPresent(DeviceDO::getProductCode, product.getProductCode()));
                                            if (!CollectionUtils.isEmpty(deviceList)) {
                                                deviceList.forEach(item -> ids.add(item.getId().toString()));
                                            }
                                        });
                                    }
                                } else {
                                    List<DataPermissionsDO> doList2 = dataPermissionsMapper.selectByRoleAndParentId(roleDataPermissionsDO.getRoleId(), resource.getDataId(), 3);
                                    if (!CollectionUtils.isEmpty(doList2)) {
                                        doList2.forEach(item -> {
                                            List<DataPermissionsDO> doList6 = dataPermissionsMapper.selectByRoleAndParentId(roleDataPermissionsDO.getRoleId(), item.getDataId(), 4);
                                            ProductDO productDO = productMapper.selectById(item.getDataId());
                                            if (!CollectionUtils.isEmpty(doList6) && productDO != null) {
                                                List<DeviceDO> list1 = deviceMapper.selectList(new LambdaQueryWrapperX<DeviceDO>().eqIfPresent(DeviceDO::getProductCode, productDO.getProductCode()));
                                                if (!CollectionUtils.isEmpty(list1)) {
                                                    list1.forEach(items -> ids.add(items.getId().toString()));
                                                }
                                            } else {
                                                List<DataPermissionsDO> doList3 = dataPermissionsMapper.selectByRoleAndParentId(roleDataPermissionsDO.getRoleId(), item.getDataId(), 5);
                                                if (!CollectionUtils.isEmpty(doList3)) {
                                                    doList3.forEach(items -> ids.add(items.getDataId()));
                                                }
                                            }
                                        });
                                    }
                                }
                                if (pageReqVO.getIsSceneRule() != null) {
                                    List<DataPermissionsDO> edgeList1 = dataPermissionsMapper.selectByRoleAndParentId(roleDataPermissionsDO.getRoleId(), resource.getDataId(), 6);
                                    if (!CollectionUtils.isEmpty(edgeList1)) {
                                        edgeList1.forEach(edge -> {
                                            List<DataPermissionsDO> edgeList2 = dataPermissionsMapper.selectByRoleAndParentId(roleDataPermissionsDO.getRoleId(), edge.getDataId(), 7);
                                            if (!CollectionUtils.isEmpty(edgeList2)) {
                                                //查询当前资源空间下的所有实例
                                                DevicePageReqVO reqVO = new DevicePageReqVO();
                                                reqVO.setResourceSpaceId(Long.valueOf(resource.getDataId()));
                                                List<DeviceAndProductVO> edgeGatewayList = deviceMapper.selectDeviceAndProductList(reqVO);
                                                if (!CollectionUtils.isEmpty(edgeGatewayList)) {
                                                    edgeGatewayList.forEach(item -> ids.add(item.getId().toString()));
                                                }
                                            } else {
                                                List<DataPermissionsDO> edgeList3 = dataPermissionsMapper.selectByRoleAndParentId(roleDataPermissionsDO.getRoleId(), edge.getDataId(), 8);
                                                edgeList3.forEach(item -> ids.add(item.getDataId()));
                                            }
                                        });
                                    }
                                }
                            });
                        }
                    }
                } else {
                    ids.add("-1");
                }
            });
        }
        return ids;
    }

}