package cn.powerchina.bjy.link.iot.controller.admin.productmodel;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.link.iot.controller.admin.productmodel.vo.ProductModelPageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.productmodel.vo.ProductModelRespVO;
import cn.powerchina.bjy.link.iot.controller.admin.productmodel.vo.ProductModelSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.productmodel.ProductModelDO;
import cn.powerchina.bjy.link.iot.enums.ThingModeTypeEnum;
import cn.powerchina.bjy.link.iot.service.productmodel.ProductModelService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 产品物模型")
@RestController
@RequestMapping("/iot/product-model")
@Validated
public class ProductModelController {

    @Resource
    private ProductModelService productModelService;

    @PostMapping("/create")
    @Operation(summary = "创建产品物模型")
//    @PreAuthorize("@ss.hasPermission('iot:product-model:create')")
    public CommonResult<Long> createProductModel(@Valid @RequestBody ProductModelSaveReqVO createReqVO) {
        return success(productModelService.createProductModel(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新产品物模型")
//    @PreAuthorize("@ss.hasPermission('iot:product-model:update')")
    public CommonResult<Boolean> updateProductModel(@Valid @RequestBody ProductModelSaveReqVO updateReqVO) {
        productModelService.updateProductModel(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除产品物模型")
    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('iot:product-model:delete')")
    public CommonResult<Boolean> deleteProductModel(@RequestParam("id") Long id) {
        productModelService.deleteProductModel(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得产品物模型")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('iot:product-model:query')")
    public CommonResult<ProductModelRespVO> getProductModel(@RequestParam("id") Long id) {
        ProductModelDO productModel = productModelService.getProductModel(id);
        return success(BeanUtils.toBean(productModel, ProductModelRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得产品物模型分页")
//    @PreAuthorize("@ss.hasPermission('iot:product-model:query')")
    public CommonResult<PageResult<ProductModelRespVO>> getProductModelPage(@Valid ProductModelPageReqVO pageReqVO) {
        PageResult<ProductModelDO> pageResult = productModelService.getProductModelPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ProductModelRespVO.class));
    }


    @GetMapping("/getProductModelServiceTypePage")
    @Operation(summary = "根据产品code获取服务类物模型数据")
//    @PreAuthorize("@ss.hasPermission('iot:product-model:query')")
    public CommonResult<PageResult<ProductModelRespVO>> getProductModelServiceTypePage(@RequestParam("productCode") String productCode, @Valid PageParam pageParam) {
        PageResult<ProductModelDO> pageResult = productModelService.getProductModelTypePage(productCode, ThingModeTypeEnum.FUNCTION.getType(), pageParam);
        return success(BeanUtils.toBean(pageResult, ProductModelRespVO.class));
    }


}