package cn.powerchina.bjy.link.iot.service.tdengine;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.powerchina.bjy.link.iot.controller.admin.productmodel.vo.ThingModelDateOrTextDataSpecs;
import cn.powerchina.bjy.link.iot.dal.dataobject.product.ProductDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.productmodel.ProductModelDO;
import cn.powerchina.bjy.link.iot.dal.tdengine.IotDevicePropertyMapper;
import cn.powerchina.bjy.link.iot.framework.tdengine.core.TDengineTableField;
import cn.powerchina.bjy.link.iot.service.productmodel.ProductModelService;
import cn.powerchina.bjy.link.iot.thingmodel.IotDataSpecsDataTypeEnum;
import cn.powerchina.bjy.link.iot.thingmodel.IotThingModelTypeEnum;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static cn.powerchina.bjy.cloud.framework.common.util.collection.CollectionUtils.convertList;


/**
 * IoT 设备【属性】数据 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class IotDevicePropertyServiceImpl implements IotDevicePropertyService {

    /**
     * 物模型的数据类型，与 TDengine 数据类型的映射关系
     */
    public static final Map<String, String> TYPE_MAPPING = MapUtil.<String, String>builder()
            .put(IotDataSpecsDataTypeEnum.INT.getDataType(), TDengineTableField.TYPE_INT)
            .put(IotDataSpecsDataTypeEnum.FLOAT.getDataType(), TDengineTableField.TYPE_FLOAT)
            .put(IotDataSpecsDataTypeEnum.DOUBLE.getDataType(), TDengineTableField.TYPE_DOUBLE)
            .put(IotDataSpecsDataTypeEnum.ENUM.getDataType(), TDengineTableField.TYPE_TINYINT)
            .put(IotDataSpecsDataTypeEnum.BOOL.getDataType(), TDengineTableField.TYPE_TINYINT)
            .put(IotDataSpecsDataTypeEnum.TEXT.getDataType(), TDengineTableField.TYPE_NCHAR)
            .put(IotDataSpecsDataTypeEnum.DATE.getDataType(), TDengineTableField.TYPE_TIMESTAMP)
            .build();

    @Resource
    private IotDevicePropertyMapper devicePropertyMapper;
    @Resource
    private ProductModelService thingModelService;

    // ========== 设备属性相关操作 ==========
//        创建一个空的超级表
    @Override
    public void defineDevicePropertyData(ProductDO product) {
        // 1.1 查询产品和物模型
        List<ProductModelDO> thingModels = thingModelService.getProductModelByProductCodeAndThingType(product.getProductCode(), IotThingModelTypeEnum.PROPERTY.getType());
        // 2.1 情况一：如果是新增的时候，需要创建表
        List<TDengineTableField> newFields = buildTableFieldList(thingModels);
        if (CollUtil.isEmpty(newFields)) {
            devicePropertyMapper.createProductPropertySTable(product.getProductCode());
        } else {
            devicePropertyMapper.createProductPropertySTableByField(product.getProductCode(), newFields);
        }

    }

    private List<TDengineTableField> buildTableFieldList(List<ProductModelDO> thingModels) {
        List<TDengineTableField> fields = new ArrayList<>();
        if (CollUtil.isEmpty(thingModels)) {
            return fields;
        }
        for (ProductModelDO thingModel : thingModels) {
            TDengineTableField field = new TDengineTableField();
            field.setType(TYPE_MAPPING.get(thingModel.getDatatype()));
            field.setField(thingModel.getThingIdentity());
            field.setLength(null);
            if (IotDataSpecsDataTypeEnum.TEXT.getDataType().equals(thingModel.getDatatype())) {
                ObjectMapper mapper = new ObjectMapper();
                try {
                    ThingModelDateOrTextDataSpecs thingModelDateOrTextDataSpecs = mapper.readValue(thingModel.getInputParams(), ThingModelDateOrTextDataSpecs.class);
                    field.setLength(thingModelDateOrTextDataSpecs.getLength());
                } catch (JsonProcessingException e) {
                    log.error("JSON解析失败：{}", thingModel.getInputParams(), e);
                }
            }
            fields.add(field);
        }
        return fields;
    }

//    @Override
//    @TenantIgnore
//    public void saveDeviceProperty(IotDeviceMessage message) {
//        if (!(message.getData() instanceof Map)) {
//            log.error("[saveDeviceProperty][消息内容({}) 的 data 类型不正确]", message);
//            return;
//        }
//        // 1. 获得设备信息
//        IotDeviceDO device = deviceService.getDeviceByProductKeyAndDeviceNameFromCache(message.getProductKey(), message.getDeviceName());
//        if (device == null) {
//            log.error("[saveDeviceProperty][消息({}) 对应的设备不存在]", message);
//            return;
//        }
//
//        // 2. 根据物模型，拼接合法的属性
//        // TODO 【待定 004】赋能后，属性到底以 thingModel 为准（ik），还是 db 的表结构为准（tl）？
//        List<IotThingModelDO> thingModels = thingModelService.getThingModelListByProductKeyFromCache(device.getProductKey());
//        Map<String, Object> properties = new HashMap<>();
//        ((Map<?, ?>) message.getData()).forEach((key, value) -> {
//            if (CollUtil.findOne(thingModels, thingModel -> thingModel.getIdentifier().equals(key)) == null) {
//                log.error("[saveDeviceProperty][消息({}) 的属性({}) 不存在]", message, key);
//                return;
//            }
//            properties.put((String) key, value);
//        });
//        if (CollUtil.isEmpty(properties)) {
//            log.error("[saveDeviceProperty][消息({}) 没有合法的属性]", message);
//            return;
//        }
//
//        // 3.1 保存设备属性【数据】
//        devicePropertyMapper.insert(device, properties,
//                LocalDateTimeUtil.toEpochMilli(message.getReportTime()));
//
//
//    }


}