package cn.powerchina.bjy.link.iot.controller.admin.notificationmethod.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.*;

@Schema(description = "管理后台 - 通知方式新增/修改 Request VO")
@Data
public class NotificationMethodSaveReqVO {

    @Schema(description = "主键", example = "25671")
    private Long id;

    @Schema(description = "关联的告警模板ID", example = "23110")
    private Long alarmTemplateId;

    @Schema(description = "通知方式（1:钉钉 2：邮件 3：短信）")
    @NotNull(message = "通知方式不能为空")
    private Integer notificationMethod;

    @Schema(description = "通知账号/webhook地址", example = "22183")
    private String notificationAccount;

    @Schema(description = "通知内容")
    private String notificationContent;

}
