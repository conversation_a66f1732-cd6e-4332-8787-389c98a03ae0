package cn.powerchina.bjy.link.iot.controller.admin.productmodel.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 产品物模型 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ProductModelRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "3695")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "产品编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("产品编码")
    private String productCode;

    @Schema(description = "物模型标识符", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("物模型标识符")
    private String thingIdentity;

    @Schema(description = "物模型名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "")
    @ExcelProperty("物模型名称")
    private String thingName;

    @Schema(description = "物模型类型，1-属性；2-服务；3-事件；", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("物模型类型，1-属性；2-服务；3-事件；")
    private Integer thingType;

    @Schema(description = "数据类型（integer、decimal、string、bool、array、enum）", example = "2")
    @ExcelProperty("数据类型（integer、decimal、string、bool、array、enum）")
    private String datatype;

    @Schema(description = "读写类型，thing_type为1时必填", example = "1")
    @ExcelProperty("读写类型，thing_type为1时必填")
    private Integer readWriteType;

    @Schema(description = "事件类型，thing_type为3时必填", example = "2")
    @ExcelProperty("事件类型，thing_type为3时必填")
    private Integer eventType;

    @Schema(description = "输入参数", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("输入参数")
    private String inputParams;

    @Schema(description = "输出参数", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("输出参数")
    private String outputParams;

    @Schema(description = "属性扩展信息", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("属性扩展信息")
    private String extra;

    @Schema(description = "描述", requiredMode = Schema.RequiredMode.REQUIRED, example = "你说的对")
    @ExcelProperty("描述")
    private String remark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}