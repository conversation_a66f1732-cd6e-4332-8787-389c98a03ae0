package cn.powerchina.bjy.link.iot.controller.admin.alarmtemplate;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.excel.core.util.ExcelUtils;
import cn.powerchina.bjy.link.iot.controller.admin.alarmtemplate.vo.AlarmTemplatePageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.alarmtemplate.vo.AlarmTemplateRespVO;
import cn.powerchina.bjy.link.iot.controller.admin.alarmtemplate.vo.AlarmTemplateSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.alarmtemplate.AlarmTemplateDO;
import cn.powerchina.bjy.link.iot.service.alarmtemplate.AlarmTemplateService;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 告警模板")
@RestController
@RequestMapping("/iot/alarm-template")
@Validated
public class AlarmTemplateController {

    @Resource
    private AlarmTemplateService alarmTemplateService;

    @PostMapping("/create")
    @Operation(summary = "创建告警模板")
//    @PreAuthorize("@ss.hasPermission('iot:alarm-template:create')")
    public CommonResult<Long> createAlarmTemplate(@Valid @RequestBody AlarmTemplateSaveReqVO createReqVO) {
        return success(alarmTemplateService.createAlarmTemplate(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新告警模板")
//    @PreAuthorize("@ss.hasPermission('iot:alarm-template:update')")
    public CommonResult<Boolean> updateAlarmTemplate(@Valid @RequestBody AlarmTemplateSaveReqVO updateReqVO) {
        alarmTemplateService.updateAlarmTemplate(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除告警模板")
    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('iot:alarm-template:delete')")
    public CommonResult<Boolean> deleteAlarmTemplate(@RequestParam("id") Long id) {
        alarmTemplateService.deleteAlarmTemplate(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得告警模板")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('iot:alarm-template:query')")
    public CommonResult<AlarmTemplateRespVO> getAlarmTemplate(@RequestParam("id") Long id) {
        AlarmTemplateDO alarmTemplate = alarmTemplateService.getAlarmTemplate(id);
        return success(BeanUtils.toBean(alarmTemplate, AlarmTemplateRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得告警模板分页")
//    @PreAuthorize("@ss.hasPermission('iot:alarm-template:query')")
    public CommonResult<PageResult<AlarmTemplateRespVO>> getAlarmTemplatePage(@Valid AlarmTemplatePageReqVO pageReqVO) {
        PageResult<AlarmTemplateDO> pageResult = alarmTemplateService.getAlarmTemplatePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, AlarmTemplateRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出告警模板 Excel")
//    @PreAuthorize("@ss.hasPermission('iot:alarm-template:export')")
//    @OperateLog(type = EXPORT)
    public void exportAlarmTemplateExcel(@Valid AlarmTemplatePageReqVO pageReqVO,
                                         HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<AlarmTemplateDO> list = alarmTemplateService.getAlarmTemplatePage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "告警模板.xls", "数据", AlarmTemplateRespVO.class,
                BeanUtils.toBean(list, AlarmTemplateRespVO.class));
    }

}
