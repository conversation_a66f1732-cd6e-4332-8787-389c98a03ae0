package cn.powerchina.bjy.link.iot.controller.admin.devicegroupdetail.bo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Description: 描述
 * @Author: yhx
 * @CreateDate: 2024/8/19
 */
@Data
public class DeviceGroupDetailBO {

    @Schema(description = "主键id-iot_device_group_detail")
    @ExcelProperty("主键id")
    private Long id;

    @Schema(description = "设备id")
    @ExcelProperty("设备id")
    private Long deviceId;

    @Schema(description = "设备组关联id")
    @ExcelProperty("设备组关联id")
    private Long deviceRefGroupId;

    @Schema(description = "设备名称")
    @ExcelProperty("设备名称")
    private String deviceName;

    @Schema(description = "设备分组id")
    @ExcelProperty("设备分组id")
    private Long deviceGroupId;

    @Schema(description = "设备编号")
    @ExcelProperty("设备编号")
    private String deviceCode;

    @Schema(description = "父设备号（网关）")
    @ExcelProperty("父设备号（网关）")
    private String parentCode;

    @Schema(description = "设备唯一标识")
    @ExcelProperty("设备唯一标识")
    private String deviceSerial;

    @Schema(description = "mcu通道号")
    @ExcelProperty("mcu通道号")
    private String mcuChannel;

    @Schema(description = "产品编码")
    @ExcelProperty("产品编码")
    private String productCode;

    @Schema(description = "产品名称")
    @ExcelProperty("产品名称")
    private String productName;

    @Schema(description = "节点类型(0直连，1网关，2网关子设备）")
    @ExcelProperty("节点类型(0直连，1网关，2网关子设备）")
    private Integer nodeType;

    @Schema(description = "连接状态（0-离线；1-在线；）")
    @ExcelProperty("连接状态（0-离线；1-在线；）")
    private Integer linkState;

    @Schema(description = "设备描述")
    @ExcelProperty("设备描述")
    private String remark;

    @Schema(description = "最后上线时间")
    @ExcelProperty("最后上线时间")
    private LocalDateTime lastUpTime;

    @Schema(description = "注册时间")
    private LocalDateTime registerTime;

}
