package cn.powerchina.bjy.link.iot.listener.device;

import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.link.iot.controller.admin.device.vo.DeviceSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.device.DeviceDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.devicecurrentattribute.DeviceShadowDO;
import cn.powerchina.bjy.link.iot.dto.message.DeviceStatusModel;
import cn.powerchina.bjy.link.iot.enums.DeviceTypeEnum;
import cn.powerchina.bjy.link.iot.enums.LinkStateEnum;
import cn.powerchina.bjy.link.iot.enums.ShadowTypeEnum;
import cn.powerchina.bjy.link.iot.enums.TransportSourceTypeEnum;
import cn.powerchina.bjy.link.iot.framework.rule.RuleActionService;
import cn.powerchina.bjy.link.iot.service.device.DeviceService;
import cn.powerchina.bjy.link.iot.service.devicecurrentattribute.DeviceShadowService;
import cn.powerchina.bjy.link.iot.service.transportsource.TransportSourceService;
import com.alibaba.fastjson.JSON;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.jeasy.rules.api.Facts;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @Description: 设备状态变更
 * @Author: handl
 * @CreateDate: 2025/07/23
 */
@Slf4j
@Component
public class DeviceStatusChangeHandler {


    @Resource
    private RuleActionService ruleActionService;

    @Resource
    private DeviceShadowService deviceShadowService;

    @Resource
    private TransportSourceService transportSourceService;

    @Resource
    private DeviceService deviceService;

    public void handler(DeviceStatusModel deviceStatusModel, DeviceDO device) {
        changeStatus(deviceStatusModel, device);

        // 如果是边缘实例并且是离线状态，要把当前边缘实例下所有在线设备变成离线
        if (Objects.equals(device.getDeviceType(), DeviceTypeEnum.EDGE.getType()) && Objects.equals(deviceStatusModel.getStatus(), LinkStateEnum.OFF_LINE.getType())) {
            List<DeviceDO> deviceDOList = deviceService.listOnlineDevicesByEdgeCode(device.getEdgeCode());
            deviceDOList.forEach(tempDeviceDO -> {
                DeviceStatusModel tempDeviceStatusModel = JSON.parseObject(JSON.toJSONString(deviceStatusModel), DeviceStatusModel.class);
                tempDeviceStatusModel.setDeviceCode(tempDeviceDO.getDeviceCode());
                tempDeviceStatusModel.setProductCode(tempDeviceDO.getProductCode());
                changeStatus(tempDeviceStatusModel, tempDeviceDO);
            });
        }
    }

    private void changeStatus(DeviceStatusModel deviceStatusModel, DeviceDO device) {
        //变更设备状态
        device.setLinkState(deviceStatusModel.getStatus() == LinkStateEnum.ON_LINE.getType()
                ? LinkStateEnum.ON_LINE.getType()
                : LinkStateEnum.OFF_LINE.getType());
        device.setLastUpTime(LocalDateTime.now());
        deviceService.updateDevice(BeanUtils.toBean(device, DeviceSaveReqVO.class));
        //更新设备影子
        LocalDateTime reportTime = deviceStatusModel.getCurrentTime() == null ? LocalDateTime.now() : Instant.ofEpochMilli(deviceStatusModel.getCurrentTime())
                .atZone(ZoneId.systemDefault())
                .toLocalDateTime();
        List<DeviceShadowDO> shadowDOList = new ArrayList<>();
        DeviceShadowDO deviceShadowDO = deviceShadowService.getShadowByDeviceCodeAndThingIdentity(deviceStatusModel.getDeviceCode(), "status");
        if (deviceShadowDO == null) {
            deviceShadowDO = new DeviceShadowDO();
            deviceShadowDO.setDeviceCode(deviceStatusModel.getDeviceCode());
            deviceShadowDO.setProductCode(deviceStatusModel.getProductCode());
            deviceShadowDO.setReportTime(reportTime);
            deviceShadowDO.setThingIdentity("status");
            deviceShadowDO.setThingValue(String.valueOf(deviceStatusModel.getStatus()));
            deviceShadowDO.setShadowType(ShadowTypeEnum.EVENT.getType());
            shadowDOList.add(deviceShadowDO);
            deviceShadowService.createShadow(shadowDOList);
        } else {
            deviceShadowDO.setReportTime(reportTime);
            deviceShadowDO.setThingValue(String.valueOf(deviceStatusModel.getStatus()));
            shadowDOList.add(deviceShadowDO);
            deviceShadowService.updateShadow(shadowDOList);
        }
        try {
            Facts facts = new Facts();
            facts.put("productCode", deviceStatusModel.getProductCode());
            facts.put("deviceCode", deviceStatusModel.getDeviceCode());
            facts.put("deviceStatus", deviceStatusModel.getStatus());
            ruleActionService.matchRuleAndAction(facts);
        } catch (Exception e) {
            log.error("根据上报的属性，匹配规则异常：{}", e.getMessage());
        }
        //设备在线状态变更
        transportSourceService.dataForwarding(TransportSourceTypeEnum.DEVICE_STATUS_CHANGE, deviceStatusModel);
    }

}
