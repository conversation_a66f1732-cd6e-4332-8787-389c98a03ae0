package cn.powerchina.bjy.link.iot.aop.devicegroupdetail;

import cn.powerchina.bjy.cloud.framework.common.exception.enums.GlobalErrorCodeConstants;
import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.cloud.system.api.permission.PermissionApi;
import cn.powerchina.bjy.cloud.system.api.permission.dto.RolePageRespDTO;
import cn.powerchina.bjy.cloud.system.enums.permission.DataScopeEnum;
import cn.powerchina.bjy.link.iot.common.role.RoleCommon;
import cn.powerchina.bjy.link.iot.controller.admin.devicegroupdetail.vo.DeviceGroupDetailPageReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.datapermissions.DataPermissionsDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.device.DeviceDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.product.ProductDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.roledatapermissions.RoleDataPermissionsDO;
import cn.powerchina.bjy.link.iot.dal.mysql.datapermissions.DataPermissionsMapper;
import cn.powerchina.bjy.link.iot.dal.mysql.device.DeviceMapper;
import cn.powerchina.bjy.link.iot.dal.mysql.product.ProductMapper;
import cn.powerchina.bjy.link.iot.dal.mysql.roledatapermissions.RoleDataPermissionsMapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import jakarta.annotation.Resource;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

import static cn.powerchina.bjy.cloud.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

@Aspect
@Component
public class DeviceGroupDetailPermissionAspect {

    @Resource
    private RoleCommon roleCommon;

    @Resource
    private PermissionApi permissionApi;

    @Resource
    private RoleDataPermissionsMapper roleDataPermissionsMapper;

    @Resource
    private DataPermissionsMapper dataPermissionsMapper;

    @Resource
    private ProductMapper productMapper;

    @Resource
    private DeviceMapper deviceMapper;


    @Around("@annotation(deviceGroupDetailPermissionCheck)")
    public Object checkEdgeGatewayPermission(ProceedingJoinPoint joinPoint, DeviceGroupDetailPermissionCheck deviceGroupDetailPermissionCheck) throws Throwable {
        //获取方法参数
        Object[] args = joinPoint.getArgs();
        DeviceGroupDetailPageReqVO pageReqVO = (DeviceGroupDetailPageReqVO) args[0];
        //检查是否是超级管理员
        boolean superAdmin = roleCommon.checkIfSuperAdmin();
        if (!superAdmin) {
            //处理产品数据权限
            List<String> ids = processDataPermissions();
            if (CollectionUtils.isEmpty(ids)) {
                return new PageResult<>(Collections.emptyList(), 0L);
            }
            QueryWrapper<DeviceDO> deviceQueryWrapper = new QueryWrapper<>();
            deviceQueryWrapper.select("device_code");
            deviceQueryWrapper.in("id", ids);
            List<DeviceDO> deviceList = deviceMapper.selectList(deviceQueryWrapper);
            List<String> deviceCodes = deviceList.stream()
                    .filter(Objects::nonNull)
                    .map(DeviceDO::getDeviceCode)
                    .filter(Objects::nonNull)
                    .toList();
            pageReqVO.setCodes(deviceCodes);

        }
        //继续执行原方法
        return joinPoint.proceed(args);
    }

    private List<String> processDataPermissions() {
        CommonResult<List<RolePageRespDTO>> result = permissionApi.getPermissionRoleByUserId(getLoginUserId());
        List<String> ids = new ArrayList<>();
        //判断响应是否成功
        if (GlobalErrorCodeConstants.SUCCESS.getCode().equals(result.getCode())) {
            List<RolePageRespDTO> rolePageRespDTOList = result.getData();
            rolePageRespDTOList.forEach(role -> {
                //查询用户角色信息
                RoleDataPermissionsDO roleDataPermissionsDO = roleDataPermissionsMapper.selectAllByRoleId(role.getId());
                if (roleDataPermissionsDO != null) {
                    //判断是否是指定数据权限
                    if (DataScopeEnum.DEPT_CUSTOM.getScope().equals(roleDataPermissionsDO.getDataScope())) {
                        //查询所有资源空间
                        List<DataPermissionsDO> doList = dataPermissionsMapper.selectListByRoleId(roleDataPermissionsDO.getRoleId(), 1);
                        if (!CollectionUtils.isEmpty(doList)) {
                            doList.forEach(resource -> {
                                List<DataPermissionsDO> doList1 = dataPermissionsMapper.selectByRoleAndParentId(roleDataPermissionsDO.getRoleId(), resource.getDataId(), 2);
                                if (!CollectionUtils.isEmpty(doList1)) {
                                    QueryWrapper<ProductDO> queryWrapper = new QueryWrapper<>();
                                    queryWrapper.eq("resource_space_id", resource.getDataId());
                                    List<ProductDO> list = productMapper.selectList(queryWrapper);
                                    if (!CollectionUtils.isEmpty(list)) {
                                        list.forEach(product -> {
                                            List<DataPermissionsDO> doList2 = dataPermissionsMapper.selectByRoleAndParentId(roleDataPermissionsDO.getRoleId(), product.getId().toString(), 4);
                                            if (!CollectionUtils.isEmpty(doList2)) {
                                                QueryWrapper<DeviceDO> queryWrappers = new QueryWrapper<>();
                                                queryWrappers.eq("product_code", product.getProductCode());
                                                List<DeviceDO> list1 = deviceMapper.selectList(queryWrappers);
                                                if (!CollectionUtils.isEmpty(list1)) {
                                                    list1.forEach(item -> ids.add(item.getId().toString()));
                                                }
                                            } else {
                                                List<DataPermissionsDO> doList3 = dataPermissionsMapper.selectByRoleAndParentId(roleDataPermissionsDO.getRoleId(), product.getId().toString(), 5);
                                                if (!CollectionUtils.isEmpty(doList3)) {
                                                    doList3.forEach(item -> ids.add(item.getDataId()));
                                                }
                                            }
                                        });
                                    }
                                } else {
                                    List<DataPermissionsDO> doList2 = dataPermissionsMapper.selectByRoleAndParentId(roleDataPermissionsDO.getRoleId(), resource.getDataId(), 3);
                                    if (!CollectionUtils.isEmpty(doList2)) {
                                        doList2.forEach(item -> {
                                            List<DataPermissionsDO> doList6 = dataPermissionsMapper.selectByRoleAndParentId(roleDataPermissionsDO.getRoleId(), item.getDataId(), 4);
                                            ProductDO productDO = productMapper.selectById(item.getDataId());
                                            if (!CollectionUtils.isEmpty(doList6) && productDO != null) {
                                                List<DeviceDO> list1 = deviceMapper.selectList(new LambdaQueryWrapperX<DeviceDO>().eqIfPresent(DeviceDO::getProductCode, productDO.getProductCode()));
                                                if (!CollectionUtils.isEmpty(list1)) {
                                                    list1.forEach(items -> ids.add(items.getId().toString()));
                                                }
                                            } else {
                                                List<DataPermissionsDO> doList3 = dataPermissionsMapper.selectByRoleAndParentId(roleDataPermissionsDO.getRoleId(), item.getDataId(), 5);
                                                if (!CollectionUtils.isEmpty(doList3)) {
                                                    doList3.forEach(items -> ids.add(items.getDataId()));
                                                }
                                            }
                                        });
                                    }
                                }
                            });
                        }
                    }
                } else {
                    ids.add("-1");
                }
            });
        }
        return ids;
    }

}