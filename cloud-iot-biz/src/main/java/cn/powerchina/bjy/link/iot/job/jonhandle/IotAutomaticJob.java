package cn.powerchina.bjy.link.iot.job.jonhandle;

import cn.powerchina.bjy.link.iot.dal.dataobject.scenerule.SceneRuleDO;
import cn.powerchina.bjy.link.iot.framework.rule.RuleActionService;
import cn.powerchina.bjy.link.iot.framework.rule.RuleServer;
import cn.powerchina.bjy.link.iot.listener.rule.TimeWindowValidator;
import cn.powerchina.bjy.link.iot.service.sceneruletimetrigger.SceneRuleTimeTriggerService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeasy.rules.api.Facts;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Component
@Slf4j
public class IotAutomaticJob {

    @Resource
    private RuleActionService ruleActionService;

    @Resource
    private SceneRuleTimeTriggerService timeTriggerService;

    @Resource
    private RuleServer ruleServer;

    @XxlJob("iotAutomaticHandle")
    public ReturnT<String> iotAutomaticHandle(Integer number) {
        int jobId = Math.toIntExact(XxlJobHelper.getJobId());
        log.info("当前执行的任务ID：" + jobId);
        //根据jobId找到ruleId;定时触发的不会抑制
        SceneRuleDO ruleDO = timeTriggerService.getSceneRuleByJobId(jobId);
        if (ruleDO != null) {
            if (StringUtils.isEmpty(ruleDO.getRuleExpression())) {
                ruleActionService.executeActionByRuleId(ruleDO.getId(),null);
            } else {
                //只匹配当前规则
                Facts facts = new Facts();
                List<Long> ruleIdList = new ArrayList<>();
                boolean inTimeWindow = TimeWindowValidator.isInTimeWindow(ruleDO, LocalDateTime.now());
                //有效时间内的规则
                if (inTimeWindow) {
                    ruleIdList.add(ruleDO.getId());
                    facts.put("executeRuleList", new ArrayList<>());
                    ruleServer.setFacts(facts);
                    ruleServer.executeRules(facts, ruleIdList);
                    List<Long> executeRuleIds = facts.get("executeRuleList");
                    log.info("message 匹配成功的规则{}", executeRuleIds);
                    ruleActionService.executeActionByRuleId(ruleDO.getId(), facts.get("deviceCode"));
                }
            }
        }
        return ReturnT.ofSuccess();
    }

}
