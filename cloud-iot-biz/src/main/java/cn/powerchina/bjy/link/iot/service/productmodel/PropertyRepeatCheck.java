package cn.powerchina.bjy.link.iot.service.productmodel;

import cn.powerchina.bjy.link.iot.controller.admin.productmodel.vo.ProductModelSaveReqVO;
import org.springframework.stereotype.Component;

/**
 * PropertyRepeatCheck
 *
 * <AUTHOR>
 **/
@Component
public class PropertyRepeatCheck extends RepeatCheck {
    @Override
    protected void validateCustom(ProductModelSaveReqVO updateReqVO) {
        // 无特殊校验，不需要实现
    }
}
