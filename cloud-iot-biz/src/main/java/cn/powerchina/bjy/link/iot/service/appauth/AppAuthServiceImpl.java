package cn.powerchina.bjy.link.iot.service.appauth;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.iot.controller.admin.appauth.vo.*;
import cn.powerchina.bjy.link.iot.controller.admin.mqttauth.vo.MqttAuthSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.appauth.AppAuthDO;
import cn.powerchina.bjy.link.iot.dal.mysql.appauth.AppAuthMapper;
import cn.powerchina.bjy.link.iot.enums.AuthTypeEnum;
import cn.powerchina.bjy.link.iot.enums.EnableStateEnum;
import cn.powerchina.bjy.link.iot.service.mqttauth.MqttAuthService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.List;
import java.util.UUID;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.link.iot.enums.ErrorCodeConstants.APP_AUTH_NAME_EXISTS;
import static cn.powerchina.bjy.link.iot.enums.ErrorCodeConstants.APP_AUTH_NOT_EXISTS;


/**
 * 应用管理认证 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AppAuthServiceImpl implements AppAuthService {

    @Resource
    private AppAuthMapper appAuthMapper;

    @Autowired
    private MqttAuthService mqttAuthService;

    @Override
    public Long createAppAuth(AppAuthCreateReqVO createReqVO) {
        List<AppAuthDO> appAuthDOList = appAuthMapper.selectList(new LambdaQueryWrapperX<AppAuthDO>().eq(AppAuthDO::getAppName, createReqVO.getAppName())
                .eq(AppAuthDO::getDeleted, Boolean.FALSE));
        if (CollectionUtils.isNotEmpty(appAuthDOList)) {
            throw exception(APP_AUTH_NAME_EXISTS);
        }
        // 保存应用
        AppAuthDO appAuth = BeanUtils.toBean(createReqVO, AppAuthDO.class);
        appAuth.setAppCode(UUID.randomUUID().toString().replaceAll("-", ""));
        appAuth.setSecret(UUID.randomUUID().toString().replaceAll("-", ""));
        appAuth.setStatus(EnableStateEnum.YES.getType());
        appAuthMapper.insert(appAuth);

        // 保存应用对应的mqtt认证信息
        MqttAuthSaveReqVO mqttAuthSaveReqVO = new MqttAuthSaveReqVO();
        mqttAuthSaveReqVO.setUserName(appAuth.getAppCode());
        mqttAuthSaveReqVO.setSecret(appAuth.getSecret());
        mqttAuthSaveReqVO.setType(AuthTypeEnum.APP_TYPE.getType());
        mqttAuthSaveReqVO.setStatus(appAuth.getStatus());
        mqttAuthService.createMqttAuth(mqttAuthSaveReqVO);

        // 返回
        return appAuth.getId();
    }

    @Override
    public void updateAppAuth(AppAuthUpdateReqVO updateReqVO) {
        // 校验存在
        validateAppAuthExists(updateReqVO.getId());

        // 校验名称
        List<AppAuthDO> appAuthDOList = appAuthMapper.selectList(new LambdaQueryWrapperX<AppAuthDO>().eq(AppAuthDO::getAppName, updateReqVO.getAppName())
                .eq(AppAuthDO::getDeleted, Boolean.FALSE).ne(AppAuthDO::getId, updateReqVO.getId()));
        if (CollectionUtils.isNotEmpty(appAuthDOList)) {
            throw exception(APP_AUTH_NAME_EXISTS);
        }

        // 更新
        AppAuthDO updateObj = BeanUtils.toBean(updateReqVO, AppAuthDO.class);
        appAuthMapper.updateById(updateObj);
    }

    @Override
    public void deleteAppAuth(Long id) {
        // 校验存在
        validateAppAuthExists(id);

        AppAuthDO appAuthDO = appAuthMapper.selectById(id);

        // 删除
        appAuthMapper.deleteById(id);

        mqttAuthService.deleteByUserName(appAuthDO.getAppCode());
    }

    private void validateAppAuthExists(Long id) {
        if (appAuthMapper.selectById(id) == null) {
            throw exception(APP_AUTH_NOT_EXISTS);
        }
    }

    @Override
    public AppAuthDO getAppAuth(Long id) {
        return appAuthMapper.selectById(id);
    }

    @Override
    public PageResult<AppAuthDO> getAppAuthPage(AppAuthPageReqVO pageReqVO) {
        return appAuthMapper.selectPage(pageReqVO);
    }

    @Override
    public void switchStatus(AppAuthSwitchReqVO appAuthSwitchReqVO) {
        validateAppAuthExists(appAuthSwitchReqVO.getId());
        AppAuthDO updateObj = BeanUtils.toBean(appAuthSwitchReqVO, AppAuthDO.class);
        appAuthMapper.updateById(updateObj);
    }

    @Override
    public void resetSecret(Long id) {
        validateAppAuthExists(id);
        AppAuthDO updateObj = appAuthMapper.selectById(id);
        updateObj.setId(id);
        updateObj.setSecret(UUID.randomUUID().toString().replaceAll("-", ""));
        appAuthMapper.updateById(updateObj);

        mqttAuthService.updateMqttAuth(updateObj.getAppCode(), updateObj.getSecret());
    }

}