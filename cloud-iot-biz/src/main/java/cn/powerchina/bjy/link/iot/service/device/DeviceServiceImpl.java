package cn.powerchina.bjy.link.iot.service.device;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.ObjectUtil;
import cn.powerchina.bjy.cloud.framework.common.exception.ErrorCode;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.iot.aop.device.DeviceDataPermissionCheck;
import cn.powerchina.bjy.link.iot.controller.admin.device.vo.DeviceAndProductVO;
import cn.powerchina.bjy.link.iot.controller.admin.device.vo.DeviceCountReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.device.vo.DevicePageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.device.vo.DeviceSaveReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.devicegroupdetail.vo.DeviceGroupDetailPageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.mqttauth.vo.MqttAuthSaveReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.transportsource.vo.DeviceRelayVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.device.DeviceDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.devicegroup.DeviceGroupDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.devicegroupdetail.DeviceGroupDetailDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.product.ProductDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.resourcespace.ResourceSpaceDO;
import cn.powerchina.bjy.link.iot.dal.mysql.device.DeviceMapper;
import cn.powerchina.bjy.link.iot.dal.mysql.product.ProductMapper;
import cn.powerchina.bjy.link.iot.dto.up.EdgeCheckOnlineStatus;
import cn.powerchina.bjy.link.iot.enums.*;
import cn.powerchina.bjy.link.iot.model.DeviceCheckOnlineModel;
import cn.powerchina.bjy.link.iot.mq.RocketMQv5Client;
import cn.powerchina.bjy.link.iot.service.devicegroup.DeviceGroupService;
import cn.powerchina.bjy.link.iot.service.devicegroupdetail.DeviceGroupDetailService;
import cn.powerchina.bjy.link.iot.service.mqttauth.MqttAuthService;
import cn.powerchina.bjy.link.iot.service.resourcespace.ResourceSpaceService;
import cn.powerchina.bjy.link.iot.service.scenerule.SceneRuleService;
import cn.powerchina.bjy.link.iot.service.sceneruleaction.SceneRuleActionService;
import cn.powerchina.bjy.link.iot.service.sceneruletrigger.SceneRuleTriggerService;
import cn.powerchina.bjy.link.iot.service.transportsource.TransportSourceService;
import cn.powerchina.bjy.link.iot.util.CodeGenerator;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.link.iot.enums.ErrorCodeConstants.*;

/**
 * 设备 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class DeviceServiceImpl implements DeviceService {

    @Resource
    private DeviceMapper deviceMapper;

    @Resource
    private ProductMapper productMapper;

    @Autowired
    @Lazy
    private ResourceSpaceService resourceSpaceService;

    @Autowired
    @Lazy
    private DeviceGroupDetailService deviceGroupDetailService;

    @Resource
    private DeviceGroupService deviceGroupService;

    @Resource
    private RocketMQv5Client rocketMQv5Client;

    @Resource
    private TransportSourceService transportSourceService;

    @Resource
    private SceneRuleTriggerService sceneRuleTriggerService;
    @Resource
    @Lazy
    private SceneRuleActionService sceneRuleActionService;
    @Resource
    @Lazy
    private SceneRuleService sceneRuleService;
    @Resource
    MqttAuthService mqttAuthService;

    @Autowired
    private RedissonClient redissonClient;

    @Override
    public Long createDevice(DeviceSaveReqVO createReqVO) {

        RLock rLock = redissonClient.getLock(RedisLockKeyConstant.PRODUCT_DEVICE_LIMIT_KEY_PREFIX + createReqVO.getProductCode());
        rLock.lock();

        try {
            // 校验存在
            validateSerialExists(createReqVO.getDeviceSerial());

            // 对网关设备从站号去重
            if (NodeTypeEnum.EDGE.getType().equals(createReqVO.getNodeType())) {
                validateSlaveIdExists(createReqVO.getEdgeCode(), createReqVO.getSlaveId(), createReqVO.getId());
            }

            // 对网关子设备MCU通道号去重
            if (NodeTypeEnum.EDGE_SUB.getType().equals(createReqVO.getNodeType())) {
                validateMcuChannelExists(createReqVO.getEdgeCode(), createReqVO.getParentCode(), createReqVO.getMcuChannel(), createReqVO.getId());
            }

            // 获取产品信息
            ProductDO productDO = productMapper.selectByCode(createReqVO.getProductCode());

            // 产品限额-已使用额度>0代表还可以创建设备；否则就是不能再创建设备
            if (productDO.getDeviceLimit() - productDO.getUsedQuota() > 0) {
                // 生成设备编码
                if (StringUtils.isBlank(createReqVO.getDeviceCode())) {
                    createReqVO.setDeviceCode(CodeGenerator.createCode(SceneTypeEnum.DEVICE.getPrefix()));
                }

                // VO转DO
                DeviceDO device = BeanUtils.toBean(createReqVO, DeviceDO.class);

                //device.setLinkState(LinkStateEnum.NO_ACTIVE.getType());

                // 生成设备密钥
                if (StringUtils.isBlank(device.getDeviceSecret())) {
                    device.setDeviceSecret(UUID.randomUUID().toString().replaceAll("-", ""));
                }

                // 设置设备类型
                if (Objects.nonNull(productDO.getNodeType())) {
                    device.setNodeType(productDO.getNodeType());
                }

                // 入库
                deviceMapper.insert(device);

                // 更新产品已使用额度
                Long usedQuota = getDeviceCountByProductCode(productDO.getProductCode());
                productDO.setUsedQuota(usedQuota);
                productMapper.updateById(productDO);


                // 保存认证信息
                MqttAuthSaveReqVO mqttAuthSaveReqVO = new MqttAuthSaveReqVO();
                mqttAuthSaveReqVO.setUserName(device.getDeviceCode());
                mqttAuthSaveReqVO.setSecret(device.getDeviceSecret());
                mqttAuthSaveReqVO.setType(AuthTypeEnum.DEVICE_TYPE.getType());
                if (Objects.equals(productDO.getDynamicRegister(), DynamicRegisterEnum.YES.getType())) {
                    mqttAuthSaveReqVO.setStatus(MqttAuthStatusEnum.DISABLE.getType());
                } else {
                    mqttAuthSaveReqVO.setStatus(MqttAuthStatusEnum.ENABLE.getType());
                }
                mqttAuthService.createMqttAuth(mqttAuthSaveReqVO);

                //数据转发
                try {
                    deviceDataForwarding(TransportSourceTypeEnum.DEVICE_CREATE, device, productDO.getResourceSpaceId());
                } catch (Exception e) {
                    log.error("设备数据转发失败：{}", device, e);
                }

                // 返回
                return device.getId();
            } else {
                throw exception(PRODUCT_USED_QUOTA_MORE_DEVICE_LIMIT);
            }
        } finally {
            rLock.unlock();
        }
    }

    /**
     * 校验设备唯一标识
     *
     * @param deviceSerial
     */
    private void validateSerialExists(String deviceSerial) {
        LambdaQueryWrapperX<DeviceDO> wrapperX = new LambdaQueryWrapperX<>();
        wrapperX.eq(DeviceDO::getDeviceSerial, deviceSerial);
        if (deviceMapper.selectCount(wrapperX) > 0) {
            throw exception(DEVICE_SERIAL_EXISTS);
        }
    }

    @Override
    public void updateDevice(DeviceSaveReqVO updateReqVO) {
        // 校验存在
        DeviceDO validateDeviceDO = validateDeviceExists(updateReqVO.getId());
        // 对网关设备从站号去重
        if (NodeTypeEnum.EDGE.getType().equals(updateReqVO.getNodeType())) {
            validateSlaveIdExists(updateReqVO.getEdgeCode(), updateReqVO.getSlaveId(), updateReqVO.getId());
        }
        //对网关子设备MCU通道号去重
        if (NodeTypeEnum.EDGE_SUB.getType().equals(updateReqVO.getNodeType())) {
            validateMcuChannelExists(updateReqVO.getEdgeCode(), updateReqVO.getParentCode(), updateReqVO.getMcuChannel(), updateReqVO.getId());
        }
        // 更新
        DeviceDO updateObj = BeanUtils.toBean(updateReqVO, DeviceDO.class);
        deviceMapper.updateById(updateObj);

        //数据转发
        ProductDO productDO = productMapper.selectByCode(updateObj.getProductCode());
        try {
            updateObj.setRegisterTime(validateDeviceDO.getRegisterTime());
            deviceDataForwarding(TransportSourceTypeEnum.DEVICE_UPDATE, updateObj, productDO.getResourceSpaceId());
        } catch (Exception e) {
            log.error("设备数据转发失败：{}", updateObj, e);
        }

        //todo 更细设备信息，通知大坝平台
    }

    @Override
    public void updateDeviceRegisterState(String deviceCode, Integer state) {
        deviceMapper.updateRegisterState(deviceCode, state, new DateTime());

        DeviceDO deviceDO = deviceMapper.selectOne(DeviceDO::getDeviceCode, deviceCode);
        //数据转发
        ProductDO productDO = productMapper.selectByCode(deviceDO.getProductCode());
        try {
            deviceDataForwarding(TransportSourceTypeEnum.DEVICE_UPDATE, deviceDO, productDO.getResourceSpaceId());
        } catch (Exception e) {
            log.error("设备数据转发失败：{}", deviceDO, e);
        }
    }

    @Override
    public void updateLinkState(Long id, Integer linkState) {
        deviceMapper.updateLinkStateWithRegisterTime(id, linkState, new DateTime());
    }

    @Transactional
    @Override
    public void deleteDevice(Long id) {
        // 记录要删除的设备
        List<DeviceDO> deviceDeleteList = new ArrayList<>();
        DeviceDO deviceDO = validateDeviceExists(id);
        collectAllChildDevices(deviceDO.getDeviceCode(), deviceDeleteList);
        deviceDeleteList.add(deviceDO);

        // 记录要更新的产品
        Set<String> productCodeSet = deviceDeleteList.stream().map(DeviceDO::getProductCode).collect(Collectors.toSet());
        List<ProductDO> productDOList = productMapper.selectList(new LambdaQueryWrapperX<ProductDO>().in(ProductDO::getProductCode, productCodeSet));
        Map<String, ProductDO> productDOMap = productDOList.stream().collect(Collectors.toMap(ProductDO::getProductCode, productDO -> productDO, (k1, k2)->k1));

        //校验网关设备是否挂载子设备
        validateSubDeviceExists(deviceDO.getDeviceCode());

        // 删除设备
        deviceMapper.deleteBatchIds(deviceDeleteList.stream().map(DeviceDO::getId).collect(Collectors.toList()));

        List<String> deviceCodeDeleteList = deviceDeleteList.stream().map(DeviceDO::getDeviceCode).collect(Collectors.toList());

        //删除设备管理-分组下的设备
        if (!CollectionUtils.isEmpty(deviceCodeDeleteList)) {
            deviceGroupDetailService.deleteDeviceGroupDetailByDeviceCode(deviceCodeDeleteList);
        }

        // 删除mqtt认证信息
        mqttAuthService.deleteByUserNameList(deviceCodeDeleteList);

        // 更新产品已使用数量
        productDOList.forEach(productDO -> {
            Long usedQuota = getDeviceCountByProductCode(productDO.getProductCode());
            productDO.setUsedQuota(usedQuota);
            productMapper.updateById(productDO);
        });

        for (DeviceDO temp : deviceDeleteList) {
            //场景失效
            this.invalidSceneRule(temp.getDeviceCode());
            //数据转发
            try {
                ProductDO productDO = productDOMap.get(temp.getProductCode());
                deviceDataForwarding(TransportSourceTypeEnum.DEVICE_DELETE, temp, productDO.getResourceSpaceId());
            } catch (Exception e) {
                log.error("设备数据转发失败：{}", temp, e);
            }
        }

    }

    private void invalidSceneRule(String deviceCode) {
        //触发条件、限制条件场景失效
        List<Long> sceneRuleTriggerList = sceneRuleTriggerService.deleteSceneRuleTrigger(null, deviceCode);
        //执行动作失效
        List<Long> sceneRuleActionList = sceneRuleActionService.deleteSceneRuleAction(null, deviceCode);
        //场景联动失效
        List<Long> allRuleList = new ArrayList<>();
        allRuleList.addAll(sceneRuleTriggerList);
        allRuleList.addAll(sceneRuleActionList);
        List<Long> ruleList = allRuleList.stream().distinct().collect(Collectors.toList());
        sceneRuleService.invalidSceneRule(ruleList, RuleStateEnum.INVALID.getType());
    }

    private DeviceDO validateDeviceExists(Long id) {
        DeviceDO result = deviceMapper.selectById(id);
        if (result == null) {
            throw exception(DEVICE_NOT_EXISTS);
        }
        return result;
    }

    /**
     * 根据网关设备编码删除子设备
     *
     * @param parentCode
     */
    private void deleteSubDevice(String parentCode) {
        if (StringUtils.isBlank(parentCode)) {
            return;
        }

        deviceMapper.deleteByParentCode(parentCode, Boolean.TRUE);
    }

    /**
     * 递归收集所有子设备（包括子设备的子设备）
     *
     * @param parentCode 父设备编码
     * @param deviceList 设备列表（会添加所有子设备）
     */
    private void collectAllChildDevices(String parentCode, List<DeviceDO> deviceList) {
        if (StringUtils.isBlank(parentCode)) {
            return;
        }

        // 查找直接子设备
        List<DeviceDO> childDevices = findDeviceByParentCode(Collections.singletonList(parentCode));
        
        if (CollectionUtils.isEmpty(childDevices)) {
            return;
        }
        
        deviceList.addAll(childDevices);

        // 递归处理每个子设备的子设备
        for (DeviceDO childDevice : childDevices) {
            collectAllChildDevices(childDevice.getDeviceCode(), deviceList);
        }
    }

    private void validateSubDeviceExists(String parentCode) {
        QueryWrapper<DeviceDO> wrapper = new QueryWrapper<>();
        wrapper.lambda()
                .eq(DeviceDO::getParentCode, parentCode)
                .eq(DeviceDO::getDeleted, Boolean.FALSE);
        if (deviceMapper.selectCount(wrapper) > 0L) {
            throw exception(DEVICE_HAVE_SUB);
        }
    }

    /**
     * 网关实例从站号判重
     * 新增时判断(网关编码 且 从站号)数量是否为零，为零则可以新增；
     * 更新时判断(网关编码 且 从站号)：
     * 1）无记录，可更新；
     * 2）数量不为1，不可更新；
     * 3）数量为1，id一致可更新；
     *
     * @param edgeCode 网关编码
     * @param slaveId  从站号
     * @param id       设备id，可为null
     */
    private void validateSlaveIdExists(String edgeCode, String slaveId, Long id) {
        QueryWrapper<DeviceDO> wrapper = new QueryWrapper<>();
        wrapper.lambda()
                .eq(DeviceDO::getEdgeCode, edgeCode)
                .eq(DeviceDO::getSlaveId, slaveId)
                .eq(DeviceDO::getDeleted, Boolean.FALSE);
        if (null == id) {
            if (deviceMapper.exists(wrapper)) {
                throw exception(EDGE_GATEWAY_SLAVE_ID_EXISTS);
            }
        } else {
            List<DeviceDO> deviceDOS = deviceMapper.selectList(wrapper);
            if (CollectionUtil.isEmpty(deviceDOS)) {
                return;
            }
            if (deviceDOS.size() != 1) {
                throw exception(EDGE_GATEWAY_SLAVE_ID_EXISTS);
            }
            if (!deviceDOS.get(0).getId().equals(id)) {
                throw exception(EDGE_GATEWAY_SLAVE_ID_EXISTS);
            }
        }

    }

    /**
     * 校验网关子设备MCU通道号唯一
     *
     * @param edgeCode
     * @param parentCode
     * @param mcuChannel
     * @param id
     */
    private void validateMcuChannelExists(String edgeCode, String parentCode, String mcuChannel, Long id) {
        QueryWrapper<DeviceDO> wrapper = new QueryWrapper<>();
        wrapper.lambda()
                .eq(DeviceDO::getEdgeCode, edgeCode)
                .eq(DeviceDO::getParentCode, parentCode)
                .eq(DeviceDO::getMcuChannel, mcuChannel)
                .eq(DeviceDO::getDeleted, Boolean.FALSE);
        if (null == id) {
            if (deviceMapper.exists(wrapper)) {
                throw exception(EDGE_GATEWAY_SUB_MCU_EXISTS);
            }
        } else {
            List<DeviceDO> deviceDOS = deviceMapper.selectList(wrapper);
            if (CollectionUtil.isEmpty(deviceDOS)) {
                return;
            }
            if (deviceDOS.size() != 1) {
                throw exception(EDGE_GATEWAY_SUB_MCU_EXISTS);
            }
            if (!deviceDOS.get(0).getId().equals(id)) {
                throw exception(EDGE_GATEWAY_SUB_MCU_EXISTS);
            }
        }
    }

    @Override
    public DeviceDO getDevice(Long id) {
        return deviceMapper.selectById(id);
    }

    @Override
    public Map<Long, DeviceDO> getDevices(Collection<Long> ids) {
        return deviceMapper.selectBatchIds(ids).stream().collect(Collectors.toMap(DeviceDO::getId, Function.identity()));
    }

    @Override
    public DeviceDO getDevice(String deviceCode) {
        return deviceMapper.selectByCode(deviceCode);
    }

    @DeviceDataPermissionCheck
    @Override
    public PageResult<DeviceAndProductVO> getDevicePage(DevicePageReqVO pageReqVO) {
        //查询已分组的设备code集合
        if (Objects.nonNull(pageReqVO.getDeviceGroupId())) {
            DeviceGroupDetailPageReqVO detailPageReqVO = new DeviceGroupDetailPageReqVO();
            detailPageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
            detailPageReqVO.setDeviceGroupId(pageReqVO.getDeviceGroupId());
            PageResult<DeviceGroupDetailDO> deviceGroupDetailPage = deviceGroupDetailService.getDeviceGroupDetailPage(detailPageReqVO);
            if (CollectionUtil.isNotEmpty(deviceGroupDetailPage.getList())) {
                List<String> deviceCodeList = deviceGroupDetailPage.getList().stream().map(DeviceGroupDetailDO::getDeviceCode).collect(Collectors.toList());
                pageReqVO.setDeviceCodeList(deviceCodeList);
            }
        }
        return selectDeviceAndProductPage(pageReqVO);
    }

    @Override
    public PageResult<DeviceAndProductVO> getAllDevicePage(DevicePageReqVO pageReqVO) {
        return deviceMapper.selectDeviceAndProductPage(pageReqVO);
    }

    /**
     * 设置资源空间名称
     *
     * @param bo
     */
    private void setSpaceName(DeviceAndProductVO bo) {
        if (Objects.nonNull(bo) && Objects.nonNull(bo.getResourceSpaceId())) {
            if (Objects.equals(bo.getResourceSpaceId(), 0L)) {
                bo.setSpaceName("全部");
            } else {
                ResourceSpaceDO spaceDO = resourceSpaceService.getResourceSpace(bo.getResourceSpaceId());
                bo.setSpaceName(Objects.isNull(spaceDO) ? null : spaceDO.getSpaceName());
            }
        }
    }


    @Override
    public Long getDeviceCountByProductCode(String productCode) {
        QueryWrapper<DeviceDO> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(DeviceDO::getProductCode, productCode).eq(DeviceDO::getDeleted, Boolean.FALSE);
        return deviceMapper.selectCount(wrapper);
    }

    @DeviceDataPermissionCheck
    @Override
    public Map<String, Object> getDeviceCountByISOnline(DevicePageReqVO pageReqVO) {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        PageResult<DeviceAndProductVO> pageResult = getDevicePage(pageReqVO);
        List<DeviceAndProductVO> voList = pageResult.getList();
        long total = voList.size();
        Map<String, Object> map = new HashMap<>();
        if (CollectionUtil.isEmpty(voList)) {
            map.put("online", 0);
            map.put("offline", 0);
            map.put("all", 0);
        } else {
            long onlineCount = voList.stream().filter(DeviceAndProductVO -> DeviceAndProductVO.getLinkState() == 2).count();
            long offlineCount = voList.stream().filter(DeviceAndProductVO -> DeviceAndProductVO.getLinkState() == 1).count();
            map.put("online", onlineCount);
            map.put("offline", offlineCount);
            map.put("all", total);
        }
        return map;
    }

    @Override
    public DeviceDO getDeviceByCode(String deviceCode) {
        LambdaQueryWrapperX<DeviceDO> wrapperX = new LambdaQueryWrapperX<>();
        return deviceMapper.selectOne(wrapperX.eq(DeviceDO::getDeviceCode, deviceCode));
    }

    @Override
    public List<DeviceDO> getDeviceByCodeList(List<String> deviceCodeList) {
        LambdaQueryWrapperX<DeviceDO> wrapperX = new LambdaQueryWrapperX<>();
        return deviceMapper.selectList(wrapperX.in(DeviceDO::getDeviceCode, deviceCodeList));
    }

    @Override
    public void edgeOnlineCheck(EdgeCheckOnlineStatus edgeCheckOnlineStatus) {
        // 更新设备状态
        deviceMapper.updateLinkState(edgeCheckOnlineStatus.getDeviceCode(), edgeCheckOnlineStatus.getCheckResult());
        if (CollUtil.isNotEmpty(edgeCheckOnlineStatus.getChildDeviceStatus())) {
            edgeCheckOnlineStatus.getChildDeviceStatus().forEach(item -> {
                deviceMapper.updateLinkState(item.getDeviceCode(), item.getCheckResult());
            });
        }
        // 通知大坝设备在线离线
        DeviceCheckOnlineModel deviceCheckOnlineModel = new DeviceCheckOnlineModel();
        BeanUtil.copyProperties(edgeCheckOnlineStatus, deviceCheckOnlineModel);
        rocketMQv5Client.syncSendFifoMessage(IotTopicConstant.TOPIC_DEVICE_STATUS_CHANGE, deviceCheckOnlineModel, IotTopicConstant.GROUP_DEVICE_STATUS_CHANGE);
    }

    @Override
    public DeviceDO getDeviceBySlaveAndEdgeCode(String slaveId, String edgeCode) {
        LambdaQueryWrapperX<DeviceDO> wrapperX = new LambdaQueryWrapperX<>();
        wrapperX.eq(DeviceDO::getSlaveId, slaveId).eq(DeviceDO::getEdgeCode, edgeCode);
        return deviceMapper.selectOne(wrapperX);
    }

    @Override
    public DeviceDO getDeviceByParentCodeAndMcuChannel(String parentCode, String mcuChannel) {
        LambdaQueryWrapperX<DeviceDO> wrapperX = new LambdaQueryWrapperX<>();
        wrapperX.eq(DeviceDO::getParentCode, parentCode)
                .eq(DeviceDO::getMcuChannel, mcuChannel);
        return deviceMapper.selectOne(wrapperX);
    }

    @Override
    public List<DeviceDO> findDeviceByParentCode(Collection<String> parentCodes) {
        return deviceMapper.selectList(new LambdaQueryWrapperX<DeviceDO>().in(DeviceDO::getParentCode, parentCodes));
    }

    /**
     * 获取边缘实例下所有在线状态的设备
     * @param edgeCode 边缘实例编码
     * @return 在线状态的设备
     */
    @Override
    public List<DeviceDO> listOnlineDevicesByEdgeCode(String edgeCode) {
        return deviceMapper.selectList(new LambdaQueryWrapperX<DeviceDO>().eq(DeviceDO::getEdgeCode, edgeCode)
                .eq(DeviceDO::getDeviceType, DeviceTypeEnum.DEVICE.getType())
                .eq(DeviceDO::getLinkState, LinkStateEnum.ON_LINE.getType()));
    }

    @Override
    public Long countDevice(DeviceCountReqVO reqVO) {
        Long count = deviceMapper.selectCount(new LambdaQueryWrapperX<DeviceDO>()
                .geIfPresent(DeviceDO::getCreateTime, reqVO.getStartDate())
                .leIfPresent(DeviceDO::getCreateTime, reqVO.getEndDate())
                .eqIfPresent(DeviceDO::getNodeType, reqVO.getNodeType())
                .eqIfPresent(DeviceDO::getLinkState, reqVO.getLinkState())
        );
        return Objects.isNull(count) ? 0L : count;
    }

    @Override
    public PageResult<DeviceAndProductVO> getGroupDevicePage(DevicePageReqVO pageReqVO) {
        //查询已分组的设备code集合
        if (Objects.nonNull(pageReqVO.getDeviceGroupId())) {
            DeviceGroupDetailPageReqVO detailPageReqVO = new DeviceGroupDetailPageReqVO();
            detailPageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
            detailPageReqVO.setDeviceGroupId(pageReqVO.getDeviceGroupId());
            PageResult<DeviceGroupDetailDO> deviceGroupDetailPage = deviceGroupDetailService.getDeviceGroupDetailPage(detailPageReqVO);
            if (CollectionUtil.isNotEmpty(deviceGroupDetailPage.getList())) {
                List<String> deviceCodeList = deviceGroupDetailPage.getList().stream().map(DeviceGroupDetailDO::getDeviceCode).collect(Collectors.toList());
                pageReqVO.setDeviceCodeList(deviceCodeList);
            }
            //查询资源空间id
            DeviceGroupDO deviceGroup = deviceGroupService.getDeviceGroup(pageReqVO.getDeviceGroupId());
            pageReqVO.setResourceSpaceId(deviceGroup.getResourceSpaceId());
        }
        return selectDeviceAndProductPage(pageReqVO);
    }

    /**
     * 设备更换
     *
     * @param updateReqVO 更新信息
     */
    @Override
    public void replaceDevice(DeviceSaveReqVO updateReqVO) {
        List<DeviceDO> deviceDOList = deviceMapper.selectList(new LambdaQueryWrapperX<DeviceDO>().eqIfPresent(DeviceDO::getParentCode, updateReqVO.getReplaceDeviceCode()));
        deviceDOList.forEach(device -> {
            device.setParentCode(updateReqVO.getDeviceCode());
            device.setUpdateTime(LocalDateTime.now());
        });
        if (!CollectionUtils.isEmpty(deviceDOList)) {
            deviceMapper.updateBatch(deviceDOList);
        }
    }

    /**
     * 新增设备拓扑
     *
     * @param deviceCode  设备编码
     * @param deviceCodes 设备编码
     */
    @Override
    public void addChildDevice(String deviceCode, List<String> deviceCodes) {
        List<DeviceDO> deviceDOList = deviceMapper.selectList(new LambdaQueryWrapperX<DeviceDO>().inIfPresent(DeviceDO::getDeviceCode, deviceCodes));
        deviceDOList.forEach(device -> {
            device.setParentCode(deviceCode);
            device.setUpdateTime(LocalDateTime.now());
        });
        deviceMapper.updateBatch(deviceDOList);
    }

    @Override
    public void delChildDevice(List<String> deviceCodes) {
        List<DeviceDO> deviceDOList = deviceMapper.selectList(new LambdaQueryWrapperX<DeviceDO>().inIfPresent(DeviceDO::getDeviceCode, deviceCodes));
        deviceDOList.forEach(device -> {
            device.setParentCode("");
            device.setUpdateTime(LocalDateTime.now());
        });
        deviceMapper.updateBatch(deviceDOList);
    }

    @Override
    public void deviceBatchRegist(ProductDO productDO, List<DeviceDO> deviceList) {
        //校验文档中唯一标识是否重复
        List<String> duplicateSerials = deviceList.stream()
                .map(DeviceDO::getDeviceSerial)
                .toList();
        Set<String> seen = new HashSet<>();
        Set<String> duplicates = new HashSet<>();

        for (String item : duplicateSerials) {
            if (!seen.add(item)) {
                duplicates.add(item);
            }
        }
        List<String> list = new ArrayList<>(duplicates);
        if (CollectionUtil.isNotEmpty(list)) {
            throw exception(new ErrorCode(500, "文档中设备唯一标识：" + list.get(0) + "数据重复,请检查后重新上传！"));
        }
        //校验与系统中设备唯一标识是否重复
        LambdaQueryWrapperX<DeviceDO> wrapperX = new LambdaQueryWrapperX<>();
        wrapperX.select(DeviceDO::getDeviceSerial);
        wrapperX.in(DeviceDO::getDeviceSerial, duplicateSerials);
        List<DeviceDO> serials = deviceMapper.selectList(wrapperX);
        if (CollectionUtil.isNotEmpty(serials)) {
            List<String> duplicateSerial = serials.stream().map(DeviceDO::getDeviceSerial).toList();
            if (CollectionUtil.isNotEmpty(duplicateSerial)) {
                throw exception(new ErrorCode(500, "系统已存在设备唯一标识：" + duplicateSerial.get(0) + "请勿重复导入！"));
            }
        }
        // 产品限额-已使用额度>0代表还可以创建设备；否则就是不能再创建设备
        if (productDO.getDeviceLimit() - productDO.getUsedQuota() < deviceList.size()) {
            throw exception(new ErrorCode(500, "设备数量超过最大限额！"));
        }
        int batchSize = 1000; // 每批插入1000条
        for (int i = 0; i < deviceList.size(); i += batchSize) {
            int end = Math.min(i + batchSize, deviceList.size());
            List<DeviceDO> batchList = deviceList.subList(i, end);
            deviceMapper.insertBatch(batchList);
        }
    }

    @Override
    public PageResult<DeviceAndProductVO> getGatewayDevicePage(DevicePageReqVO pageReqVO) {
        return selectDeviceAndProductPage(pageReqVO);
    }

    public PageResult<DeviceAndProductVO> selectDeviceAndProductPage(DevicePageReqVO pageReqVO) {
        PageResult<DeviceAndProductVO> deviceAndProductVOPageResult = deviceMapper.selectDeviceAndProductPage(pageReqVO);
        //查网关名称(父设备名称)
        deviceAndProductVOPageResult.getList().forEach(deviceAndProductVO -> {
            setSpaceName(deviceAndProductVO);
            if (NodeTypeEnum.EDGE.getType().equals(deviceAndProductVO.getNodeType())) return;
            DeviceDO device = getDevice(deviceAndProductVO.getParentCode());
            if (ObjectUtil.isNotEmpty(device)) {
                deviceAndProductVO.setParentName(device.getDeviceName());
            }
        });
        return deviceAndProductVOPageResult;
    }

    private void deviceDataForwarding(TransportSourceTypeEnum dataType, DeviceDO deviceDO, Long resourceSpaceId) {
        //数据转发
        DeviceRelayVO deviceRelayVO = BeanUtils.toBean(deviceDO, DeviceRelayVO.class);
        deviceRelayVO.setResourceSpaceId(resourceSpaceId);
        transportSourceService.dataForwarding(dataType, deviceRelayVO);

    }


}