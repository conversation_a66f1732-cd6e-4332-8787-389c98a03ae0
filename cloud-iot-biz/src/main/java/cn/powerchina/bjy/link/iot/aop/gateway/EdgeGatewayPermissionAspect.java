package cn.powerchina.bjy.link.iot.aop.gateway;

import cn.powerchina.bjy.cloud.framework.common.exception.enums.GlobalErrorCodeConstants;
import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.cloud.system.api.permission.PermissionApi;
import cn.powerchina.bjy.cloud.system.api.permission.dto.RolePageRespDTO;
import cn.powerchina.bjy.cloud.system.enums.permission.DataScopeEnum;
import cn.powerchina.bjy.link.iot.common.role.RoleCommon;
import cn.powerchina.bjy.link.iot.controller.admin.device.vo.DeviceAndProductVO;
import cn.powerchina.bjy.link.iot.controller.admin.device.vo.DevicePageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.edgegateway.vo.EdgeGatewayPageReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.datapermissions.DataPermissionsDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.device.DeviceDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.edgegateway.EdgeGatewayDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.roledatapermissions.RoleDataPermissionsDO;
import cn.powerchina.bjy.link.iot.dal.mysql.datapermissions.DataPermissionsMapper;
import cn.powerchina.bjy.link.iot.dal.mysql.device.DeviceMapper;
import cn.powerchina.bjy.link.iot.dal.mysql.edgegateway.EdgeGatewayMapper;
import cn.powerchina.bjy.link.iot.dal.mysql.roledatapermissions.RoleDataPermissionsMapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import jakarta.annotation.Resource;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static cn.powerchina.bjy.cloud.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

@Aspect
@Component
public class EdgeGatewayPermissionAspect {

    @Resource
    private RoleCommon roleCommon;

    @Resource
    private PermissionApi permissionApi;

    @Resource
    private RoleDataPermissionsMapper roleDataPermissionsMapper;

    @Resource
    private DataPermissionsMapper dataPermissionsMapper;

    @Resource
    private EdgeGatewayMapper edgeGatewayMapper;

    @Resource
    private DeviceMapper deviceMapper;

    @Around("@annotation(edgeGatewayPermissionCheck)")
    public Object checkEdgeGatewayPermission(ProceedingJoinPoint joinPoint, EdgeGatewayPermissionCheck edgeGatewayPermissionCheck) throws Throwable {
        //获取方法参数
        Object[] args = joinPoint.getArgs();
        EdgeGatewayPageReqVO pageReqVO = (EdgeGatewayPageReqVO) args[0];

        //检查是否是超级管理员
        boolean superAdmin = roleCommon.checkIfSuperAdmin();
        if (!superAdmin) {
            //处理产品数据权限
            List<String> ids = processDataPermissions();
            if (CollectionUtils.isEmpty(ids)) {
                return new PageResult<>(Collections.emptyList(), 0L);
            }
            pageReqVO.setCodes(ids);
        }
        //继续执行原方法
        return joinPoint.proceed(args);
    }

    private List<String> processDataPermissions() {
        CommonResult<List<RolePageRespDTO>> result = permissionApi.getPermissionRoleByUserId(getLoginUserId());
        List<String> ids = new ArrayList<>();
        //判断响应是否成功
        if (GlobalErrorCodeConstants.SUCCESS.getCode().equals(result.getCode())) {
            List<RolePageRespDTO> rolePageRespDTOList = result.getData();
            rolePageRespDTOList.forEach(role -> {
                //查询用户角色信息
                RoleDataPermissionsDO roleDataPermissionsDO = roleDataPermissionsMapper.selectAllByRoleId(role.getId());
                if (roleDataPermissionsDO != null) {
                    //判断是否是指定数据权限
                    if (DataScopeEnum.DEPT_CUSTOM.getScope().equals(roleDataPermissionsDO.getDataScope())) {
                        //查询所有资源空间
                        List<DataPermissionsDO> doList = dataPermissionsMapper.selectListByRoleId(roleDataPermissionsDO.getRoleId(), 1);
                        if (!CollectionUtils.isEmpty(doList)) {
                            doList.forEach(resource -> {
                                List<DataPermissionsDO> doList1 = dataPermissionsMapper.selectByRoleAndParentId(roleDataPermissionsDO.getRoleId(), resource.getDataId(), 6);
                                if (!CollectionUtils.isEmpty(doList1)) {
                                    doList1.forEach(edge -> {
                                        List<DataPermissionsDO> doList2 = dataPermissionsMapper.selectByRoleAndParentId(roleDataPermissionsDO.getRoleId(), edge.getDataId(), 7);
                                        if (!CollectionUtils.isEmpty(doList2)) {
                                            //查询当前资源空间下的所有实例
                                            //QueryWrapper<EdgeGatewayDO> queryWrappers = new QueryWrapper<>();
                                            //queryWrappers.eq("resource_space_id", resource.getDataId());
                                            //List<EdgeGatewayDO> edgeGatewayList = edgeGatewayMapper.selectList(queryWrappers);
                                            DevicePageReqVO reqVO = new DevicePageReqVO();
                                            reqVO.setResourceSpaceId(Long.valueOf(resource.getDataId()));

                                            List<DeviceAndProductVO> edgeGatewayList = deviceMapper.selectDeviceAndProductList(reqVO);


                                            if (!CollectionUtils.isEmpty(edgeGatewayList)) {
                                                edgeGatewayList.forEach(item -> ids.add(item.getId().toString()));
                                            }
                                        } else {
                                            List<DataPermissionsDO> doList3 = dataPermissionsMapper.selectByRoleAndParentId(roleDataPermissionsDO.getRoleId(), edge.getDataId(), 8);
                                            doList3.forEach(item -> ids.add(item.getDataId()));
                                        }
                                    });
                                }
                            });
                        }
                    }
                } else {
                    ids.add("-1");
                }
            });
        }
        return ids;
    }

}