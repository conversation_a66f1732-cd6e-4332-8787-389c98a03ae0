package cn.powerchina.bjy.link.iot.dal.dataobject.modelTemplateDetails;

import cn.powerchina.bjy.cloud.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

@TableName("iot_model_template_details")
@KeySequence("iot_model_template_details_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ModelTemplateDetailsDO extends BaseDO {

    /**
     * 主键
     */
    @TableField(value = "id")
    private Long id;
    /**
     * 物模板Id
     */
    @TableField(value = "template_id")
    private Long templateId;

    /**
     * 物模板明细名称
     */
    @TableField(value = "template_details_name")
    private String templateDetailsName;
    /**
     * 物模板标识符
     */
    @TableField(value = "template_identity")
    private String templateIdentity;
    /**
     * 物模板类型，1-属性；2-服务；3-事件；
     */
    @TableField(value = "template_type")
    private Integer templateType;
    /**
     * 数据类型（integer、decimal、string、bool、array、enum）
     */
    @TableField(value = "datatype")
    private String datatype;
    /**
     * 读写类型，thing_type为1时必填；1-读写；2-只读；3-只写
     */
    @TableField(value = "read_write_type")
    private Integer readWriteType;
    /**
     * 事件类型，thing_type为3时必填,1-信息；2告警；3-故障
     */
    @TableField(value = "event_type")
    private Integer eventType;
    /**
     * 输入参数
     */
    @TableField(value = "input_params")
    private String inputParams;
    /**
     * 输出参数
     */
    @TableField(value = "output_params")
    private String outputParams;
    /**
     * 属性扩展信息
     */
    @TableField(value = "extra")
    private String extra;
    /**
     * 描述
     */
    @TableField(value = "remark")
    private String remark;
    /**
     * 租户编号
     */
    @TableField(value = "tenant_id")
    private Long tenantId;
    /**
     * 是否删除，默认为0
     */
    @TableField(value = "deleted")
    private Boolean deleted;
}
