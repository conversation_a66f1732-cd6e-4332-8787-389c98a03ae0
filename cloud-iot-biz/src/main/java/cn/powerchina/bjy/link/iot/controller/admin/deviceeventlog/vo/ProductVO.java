package cn.powerchina.bjy.link.iot.controller.admin.deviceeventlog.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "管理后台 - 产品精简信息 Response VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProductVO {

    @Schema(description = "产品编号", example = "1024")
    private Long id;

    @Schema(description = "产品编码", example = "P001")
    private String productCode;

    @Schema(description = "产品名称", example = "智能温控器")
    private String productName;

    @Schema(description = "产品描述", example = "智能温控器产品描述")
    private String productDesc;
}
