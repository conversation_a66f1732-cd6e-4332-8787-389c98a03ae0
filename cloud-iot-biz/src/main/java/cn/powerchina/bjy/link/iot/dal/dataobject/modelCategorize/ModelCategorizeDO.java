package cn.powerchina.bjy.link.iot.dal.dataobject.modelCategorize;

import cn.powerchina.bjy.cloud.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.util.ArrayList;
import java.util.List;

@TableName("iot_model_categorize")
@KeySequence("iot_model_categorize_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ModelCategorizeDO extends BaseDO {

    public static final Long PARENT_ID_ROOT = 0L;
    /**
     * 主键
     */
    @TableField(value = "id")
    private Long id;
    /**
     * 父ID
     */
    @TableField(value = "parent_id")
    private Long parentId;
    /**
     * 分类名称
     */
    @TableField(value = "categorize_name")
    private String categorizeName;
    /**
     * 分类层级
     */
    @TableField(value = "level")
    private Integer level;
    /**
     * 显示顺序
     */
    @TableField(value = "sort")
    private Integer sort;
    /**
     * 启用状态（0不启用，1启用，默认0）
     */
    @TableField(value = "state")
    private Integer state;
    /**
     * 租户编号
     */
    @TableField(value = "tenant_id")
    private Long tenantId;
    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;
    /**
     * 是否删除，默认为0
     */
    @TableField(value = "deleted")
    private Boolean deleted;
}
