package cn.powerchina.bjy.link.iot.service.edgechannel;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.iot.controller.admin.edgechannel.vo.EdgeChannelPageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.edgechannel.vo.EdgeChannelSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.edgechannel.EdgeChannelDO;
import cn.powerchina.bjy.link.iot.dal.mysql.edgechannel.EdgeChannelMapper;
import cn.powerchina.bjy.link.iot.enums.SceneTypeEnum;
import cn.powerchina.bjy.link.iot.util.CodeGenerator;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import org.springframework.validation.annotation.Validated;

import static cn.powerchina.bjy.link.iot.enums.ErrorCodeConstants.EDGE_CHANNEL_NAME_EXISTS;
import static cn.powerchina.bjy.link.iot.enums.ErrorCodeConstants.EDGE_CHANNEL_NOT_EXISTS;

/**
 * 通道 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class EdgeChannelServiceImpl implements EdgeChannelService {

    @Resource
    private EdgeChannelMapper edgeChannelMapper;

    @Override
    public Long createEdgeChannel(EdgeChannelSaveReqVO createReqVO) {
        // 校验通道名称存在
        validateChannelNameExists(createReqVO.getChannelName());
        // 插入
        EdgeChannelDO edgeChannel = BeanUtils.toBean(createReqVO, EdgeChannelDO.class);
        edgeChannel.setChannelCode(CodeGenerator.createCode(SceneTypeEnum.CHANNEL.getPrefix()));
        edgeChannelMapper.insert(edgeChannel);
        // 返回
        return edgeChannel.getId();
    }

    @Override
    public void updateEdgeChannel(EdgeChannelSaveReqVO updateReqVO) {
        // 校验存在
        validateEdgeChannelExists(updateReqVO.getId());
        // 更新
        EdgeChannelDO updateObj = BeanUtils.toBean(updateReqVO, EdgeChannelDO.class);
        edgeChannelMapper.updateById(updateObj);
    }

    @Override
    public void deleteEdgeChannel(Long id) {
        // 校验存在
        validateEdgeChannelExists(id);
        // 删除
        edgeChannelMapper.deleteById(id);
    }

    private void validateEdgeChannelExists(Long id) {
        if (edgeChannelMapper.selectById(id) == null) {
            throw exception(EDGE_CHANNEL_NOT_EXISTS);
        }
    }

    private void validateChannelNameExists(String channelName){
        if (edgeChannelMapper.selectCountByChannelName(channelName) > 0) {
            throw exception(EDGE_CHANNEL_NAME_EXISTS);
        }
    }

    @Override
    public EdgeChannelDO getEdgeChannel(Long id) {
        return edgeChannelMapper.selectById(id);
    }

    @Override
    public PageResult<EdgeChannelDO> getEdgeChannelPage(EdgeChannelPageReqVO pageReqVO) {
        return edgeChannelMapper.selectPage(pageReqVO);
    }

    @Override
    public EdgeChannelDO getEdgeChannelByCode(String channelCode) {
        LambdaQueryWrapperX<EdgeChannelDO> wrapperX = new LambdaQueryWrapperX<>();
        wrapperX.eq(EdgeChannelDO::getChannelCode,channelCode);
        return edgeChannelMapper.selectOne(wrapperX);
    }

}