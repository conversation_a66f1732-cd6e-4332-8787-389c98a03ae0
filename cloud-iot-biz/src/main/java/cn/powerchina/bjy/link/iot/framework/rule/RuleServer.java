package cn.powerchina.bjy.link.iot.framework.rule;

import cn.hutool.core.collection.CollectionUtil;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.link.iot.controller.admin.device.vo.DeviceAndProductVO;
import cn.powerchina.bjy.link.iot.controller.admin.device.vo.DevicePageReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.devicecurrentattribute.DeviceShadowDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.scenerule.SceneRuleDO;
import cn.powerchina.bjy.link.iot.service.device.DeviceService;
import cn.powerchina.bjy.link.iot.service.devicecurrentattribute.DeviceShadowService;
import cn.powerchina.bjy.link.iot.service.scenerule.SceneRuleService;
import com.alibaba.fastjson.JSONObject;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeasy.rules.api.Facts;
import org.jeasy.rules.api.Rules;
import org.jeasy.rules.api.RulesEngine;
import org.jeasy.rules.mvel.MVELRule;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Description: 描述
 * @Author: zhaoqiang
 * @CreateDate: 2025/5/27
 */
@Component
@Slf4j
public class RuleServer {

    @Resource
    private SceneRuleService sceneRuleService;

    @Resource
    private RulesEngine rulesEngine;

    @Resource
    private RuleRedisCache ruleRedisCache;

    @Resource
    private DeviceService deviceService;

    @Resource
    private DeviceShadowService deviceShadowService;

    /**
     * 执行时间窗口内的规则
     *
     * @param facts
     * @param timeWindowRuleList
     */
    public void executeRules(Facts facts, List<Long> timeWindowRuleList) {
        Rules timeRules = new Rules();
        List<SceneRuleDO> allRulesFromRedisOrDB = ruleRedisCache.getAllRulesFromRedisOrDB();
        for (SceneRuleDO ruleEntity : allRulesFromRedisOrDB) {
            if (timeWindowRuleList.contains(ruleEntity.getId())) {
                try {
                    if (StringUtils.isNotEmpty(ruleEntity.getRuleExpression())) {
                        MVELRule rule = new MVELRule()
                                .name(String.valueOf(ruleEntity.getId()))
                                .description(ruleEntity.getRuleDesc())
                                .priority(ruleEntity.getRulePriority())
                                .when(ruleEntity.getRuleExpression()) // MVEL 条件表达式
                                .then(ruleEntity.getRuleAction());
                        timeRules.register(rule);
                    }
                } catch (Exception e) {
                    log.error("加载规则{}失败{}", ruleEntity.getId(), e.getMessage());
                }
            }
        }
        log.info(" {} 条有效时间内规则从数据库中加载完成", timeRules.size());
        try {
            rulesEngine.fire(timeRules, facts);
        } catch (Exception e) {
            log.error("事实对象参数{}执行规则失败:{}", JSONObject.toJSON(facts), e.getMessage());
        }
    }

    /**
     * 执行抑制规则
     *
     * @param facts
     * @param timeWindowRuleList
     */
    public void executeInhibitionRules(Facts facts, List<Long> timeWindowRuleList) {
        Rules timeRules = new Rules();
        List<SceneRuleDO> allRulesFromRedisOrDB = ruleRedisCache.getAllRulesFromRedisOrDB();
        for (SceneRuleDO ruleEntity : allRulesFromRedisOrDB) {
            if (timeWindowRuleList.contains(ruleEntity.getId())) {
                try {
                    if (StringUtils.isNotEmpty(ruleEntity.getInhibitionExpression())) {
                        MVELRule rule = new MVELRule()
                                .name(String.valueOf(ruleEntity.getId()))
                                .description(ruleEntity.getRuleDesc())
                                .priority(ruleEntity.getRulePriority())
                                .when(ruleEntity.getInhibitionExpression())
                                .then(ruleEntity.getRuleAction());
                        timeRules.register(rule);
                    }
                } catch (Exception e) {
                    log.error("加载抑制规则{}失败{}", ruleEntity.getId(), e.getMessage());
                }
            }
        }
        log.info(" {} 条有效时间内规则从数据库中加载完成", timeRules.size());
        try {
            rulesEngine.fire(timeRules, facts);
        } catch (Exception e) {
            log.error("事实对象参数{}执行抑制规则失败:{}", JSONObject.toJSON(facts), e.getMessage());
        }
    }

    /**
     * facts中设置设备对象和设备属性
     *
     * @param facts
     */
    public void setFacts(Facts facts) {
        try {
            List<DeviceAndProductVO> allDevice = getAllDevice();
            if (CollectionUtil.isNotEmpty(allDevice)) {
                allDevice.forEach(item -> {
                    facts.put("deviceCode_" + item.getDeviceCode(), item.getDeviceCode());
                });
            }
            List<DeviceShadowDO> currentAttributeList = deviceShadowService.getShadowList();
            if (CollectionUtil.isNotEmpty(currentAttributeList)) {
                currentAttributeList.forEach(item -> {
                    facts.put(item.getDeviceCode() + "_" + item.getThingIdentity(), item.getThingValue());
                });
            }
        } catch (Exception e) {
            log.error("facts中设置设备对象和设备属性异常:{}", e.getMessage());
        }
    }

    public List<DeviceAndProductVO> getAllDevice() {
        //查询所有设备及其属性:缓存中查询
        DevicePageReqVO devicePageReqVO = new DevicePageReqVO();
        devicePageReqVO.setPageSize(-1);
        PageResult<DeviceAndProductVO> devicePage = deviceService.getAllDevicePage(devicePageReqVO);
        return devicePage.getList();
    }
}
