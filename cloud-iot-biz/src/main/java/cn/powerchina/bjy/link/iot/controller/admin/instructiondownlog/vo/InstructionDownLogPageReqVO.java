package cn.powerchina.bjy.link.iot.controller.admin.instructiondownlog.vo;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 指令下发操作记录分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class InstructionDownLogPageReqVO extends PageParam {

    @Schema(description = "设备编号")
    private String deviceCode;

    @Schema(description = "物模型类型", example = "1")
    private Integer thingType;

    @Schema(description = "物模型标识符")
    private String thingIdentity;

    @Schema(description = "物模型名称", example = "")
    private String thingName;

    @Schema(description = "控制源（0-web；1-ios；2-android）")
    private Integer operatorSource;

    @Schema(description = "messageid", example = "7321")
    private String messageId;

    @Schema(description = "输入参数")
    private String inputParams;

    @Schema(description = "输出参数")
    private String outputParams;

    @Schema(description = "上行消耗时间(ms)")
    private Integer upConsumeTime;

    @Schema(description = "下行消耗时间(ms)")
    private Integer downConsumeTime;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}