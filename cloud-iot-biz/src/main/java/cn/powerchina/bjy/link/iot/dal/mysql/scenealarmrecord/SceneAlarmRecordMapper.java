package cn.powerchina.bjy.link.iot.dal.mysql.scenealarmrecord;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.MPJLambdaWrapperX;
import cn.powerchina.bjy.link.iot.controller.admin.scenealarmrecord.vo.SceneAlarmRecordPageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.scenealarmrecord.vo.SceneAlarmRecordRespVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.device.DeviceDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.scenealarmrecord.SceneAlarmRecordDO;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 告警记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface SceneAlarmRecordMapper extends BaseMapperX<SceneAlarmRecordDO> {

    default PageResult<SceneAlarmRecordRespVO> selectPage(SceneAlarmRecordPageReqVO reqVO) {
        MPJLambdaWrapperX<SceneAlarmRecordDO> wrapper = (MPJLambdaWrapperX<SceneAlarmRecordDO>) new MPJLambdaWrapperX<SceneAlarmRecordDO>()
                .selectAll(SceneAlarmRecordDO.class)
                .selectAs(DeviceDO::getDeviceSerial, SceneAlarmRecordRespVO::getDeviceSerial)
                .eqIfPresent(SceneAlarmRecordDO::getResourceSpaceId, reqVO.getResourceSpaceId())
                .inIfPresent(SceneAlarmRecordDO::getResourceSpaceId, reqVO.getResourceSpaceIdList())
                .inIfPresent(SceneAlarmRecordDO::getDeviceCode, reqVO.getDeviceCodes())
                .eqIfPresent(SceneAlarmRecordDO::getRuleId, reqVO.getRuleId())
                .likeIfPresent(SceneAlarmRecordDO::getRuleName, reqVO.getRuleName())
                .eqIfPresent(SceneAlarmRecordDO::getAlarmTemplateId, reqVO.getAlarmTemplateId())
                .likeIfPresent(SceneAlarmRecordDO::getAlarmName, reqVO.getAlarmName())
                .eqIfPresent(SceneAlarmRecordDO::getAlarmContent, reqVO.getAlarmContent())
                .eqIfPresent(SceneAlarmRecordDO::getAlarmLevel, reqVO.getAlarmLevel())
                .eqIfPresent(SceneAlarmRecordDO::getAlarmStatus, reqVO.getAlarmStatus())
                .betweenIfPresent(SceneAlarmRecordDO::getTriggerTime, reqVO.getTriggerTime())
                .eqIfPresent(SceneAlarmRecordDO::getDeviceCode, reqVO.getDeviceCode())
                .eqIfPresent(SceneAlarmRecordDO::getProductCode, reqVO.getProductCode())
                .betweenIfPresent(SceneAlarmRecordDO::getProcessTime, reqVO.getProcessTime())
                .eqIfPresent(SceneAlarmRecordDO::getProcessRecord, reqVO.getProcessRecord())
                .eqIfPresent(SceneAlarmRecordDO::getProcessUserId, reqVO.getProcessUserId())
                .eqIfPresent(SceneAlarmRecordDO::getRecoveryType, reqVO.getRecoveryType())
                .betweenIfPresent(SceneAlarmRecordDO::getRecoveryTime, reqVO.getRecoveryTime())
                .eqIfPresent(SceneAlarmRecordDO::getRecoveryUserId, reqVO.getRecoveryUserId())
                .betweenIfPresent(SceneAlarmRecordDO::getCreateTime, reqVO.getCreateTime())
                // 动态添加or条件
                .or(CollectionUtils.isNotEmpty(reqVO.getRuleIds()), q -> q
                        .or(w -> w
                                .in(SceneAlarmRecordDO::getRuleId, reqVO.getRuleIds())
                        )
                )
                .leftJoin(DeviceDO.class, "p", on -> on.eq(DeviceDO::getDeviceCode, SceneAlarmRecordDO::getDeviceCode));
        if (StringUtils.isNotBlank(reqVO.getDeviceSerial())) {
            wrapper.like(DeviceDO::getDeviceSerial, reqVO.getDeviceSerial());
        }
        wrapper.orderByDesc(SceneAlarmRecordDO::getId);

        return selectJoinPage(reqVO, SceneAlarmRecordRespVO.class, wrapper);
    }

    List<SceneAlarmRecordRespVO> selectRecordList(SceneAlarmRecordPageReqVO reqVO);

    int selectRecordCount(SceneAlarmRecordPageReqVO reqVO);
}
