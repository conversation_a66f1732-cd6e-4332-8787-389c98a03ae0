package cn.powerchina.bjy.link.iot.controller.admin.modelTemplate;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.link.iot.controller.admin.modelTemplate.bo.ModelTemplateBO;
import cn.powerchina.bjy.link.iot.controller.admin.modelTemplate.vo.ModelTemplatePageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.modelTemplate.vo.ModelTemplateRespVO;
import cn.powerchina.bjy.link.iot.controller.admin.modelTemplate.vo.ModelTemplateSaveReqVO;
import cn.powerchina.bjy.link.iot.service.modelTemplate.ModelTemplateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;


/**
 * 物模板信息表
 *
 * <AUTHOR>
 * @date 2025-03-25 15:52:13
 */
@Tag(name = "管理后台 - 物模板")
@RestController
@RequestMapping("/iot/modelTemplate")
@Validated
public class ModelTemplateController {
    @Autowired
    private ModelTemplateService modelTemplateService;

    @GetMapping("/page")
    @Operation(summary = "获得物模板分页")
    public CommonResult<PageResult<ModelTemplateRespVO>> getModelTemplatePage(@Valid ModelTemplatePageReqVO pageReqVO) {
        PageResult<ModelTemplateBO> pageResult = modelTemplateService.getModelTemplatePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ModelTemplateRespVO.class));
    }
    @GetMapping("/get")
    @Operation(summary = "获得物模板")
    @Parameter(name = "id", description = "编号", required = true)
    public CommonResult<ModelTemplateRespVO> getModelTemplate(@RequestParam("id") Long id) {
        ModelTemplateBO modelTemplateBO = modelTemplateService.getModelTemplateBO(id);
        ModelTemplateRespVO result = BeanUtils.toBean(modelTemplateBO, ModelTemplateRespVO.class);
        return success(result);
    }
    @PostMapping("/create")
    @Operation(summary = "创建物模板")
    public CommonResult<Long> createModelTemplate(@Valid @RequestBody ModelTemplateSaveReqVO createReqVO) {
        return success(modelTemplateService.createModelTemplate(createReqVO));
    }
    @PutMapping("/update")
    @Operation(summary = "更新物模板")
    public CommonResult<Boolean> updateModelTemplate(@Valid @RequestBody ModelTemplateSaveReqVO updateReqVO) {
        modelTemplateService.updateModelTemplate(updateReqVO);
        return success(true);
    }
    @DeleteMapping("/delete")
    @Operation(summary = "删除物模板")
    @Parameter(name = "id", description = "编号", required = true)
    public CommonResult<Boolean> deleteModelTemplate(@RequestParam("id") Long id) {
        modelTemplateService.deleteModelTemplate(id);
        return success(true);
    }

}
