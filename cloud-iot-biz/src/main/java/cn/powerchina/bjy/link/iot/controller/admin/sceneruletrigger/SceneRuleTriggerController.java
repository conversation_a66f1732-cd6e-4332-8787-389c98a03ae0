package cn.powerchina.bjy.link.iot.controller.admin.sceneruletrigger;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.excel.core.util.ExcelUtils;
import cn.powerchina.bjy.link.iot.controller.admin.sceneruletrigger.vo.SceneRuleTriggerPageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.sceneruletrigger.vo.SceneRuleTriggerRespVO;
import cn.powerchina.bjy.link.iot.controller.admin.sceneruletrigger.vo.SceneRuleTriggerSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.sceneruletrigger.SceneRuleTriggerDO;
import cn.powerchina.bjy.link.iot.service.sceneruletrigger.SceneRuleTriggerService;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 规则触发/条件限制")
@RestController
@RequestMapping("/iot/scene-rule-trigger")
@Validated
public class SceneRuleTriggerController {

    @Resource
    private SceneRuleTriggerService sceneRuleTriggerService;

    @PostMapping("/create")
    @Operation(summary = "创建规则触发/条件限制")
//    @PreAuthorize("@ss.hasPermission('iot:scene-rule-trigger:create')")
    public CommonResult<Long> createSceneRuleTrigger(@Valid @RequestBody SceneRuleTriggerSaveReqVO createReqVO) {
        return success(sceneRuleTriggerService.createSceneRuleTrigger(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新规则触发/条件限制")
//    @PreAuthorize("@ss.hasPermission('iot:scene-rule-trigger:update')")
    public CommonResult<Boolean> updateSceneRuleTrigger(@Valid @RequestBody SceneRuleTriggerSaveReqVO updateReqVO) {
        sceneRuleTriggerService.updateSceneRuleTrigger(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除规则触发/条件限制")
    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('iot:scene-rule-trigger:delete')")
    public CommonResult<Boolean> deleteSceneRuleTrigger(@RequestParam("id") Long id) {
        sceneRuleTriggerService.deleteSceneRuleTrigger(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得规则触发/条件限制")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('iot:scene-rule-trigger:query')")
    public CommonResult<SceneRuleTriggerRespVO> getSceneRuleTrigger(@RequestParam("id") Long id) {
        SceneRuleTriggerDO sceneRuleTrigger = sceneRuleTriggerService.getSceneRuleTrigger(id);
        return success(BeanUtils.toBean(sceneRuleTrigger, SceneRuleTriggerRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得规则触发/条件限制分页")
//    @PreAuthorize("@ss.hasPermission('iot:scene-rule-trigger:query')")
    public CommonResult<PageResult<SceneRuleTriggerRespVO>> getSceneRuleTriggerPage(@Valid SceneRuleTriggerPageReqVO pageReqVO) {
        PageResult<SceneRuleTriggerDO> pageResult = sceneRuleTriggerService.getSceneRuleTriggerPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, SceneRuleTriggerRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出规则触发/条件限制 Excel")
//    @PreAuthorize("@ss.hasPermission('iot:scene-rule-trigger:export')")
    public void exportSceneRuleTriggerExcel(@Valid SceneRuleTriggerPageReqVO pageReqVO,
                                            HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<SceneRuleTriggerDO> list = sceneRuleTriggerService.getSceneRuleTriggerPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "规则触发/条件限制.xls", "数据", SceneRuleTriggerRespVO.class,
                BeanUtils.toBean(list, SceneRuleTriggerRespVO.class));
    }

}
