package cn.powerchina.bjy.link.iot.service.alarmtemplate;

import java.util.*;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.link.iot.controller.admin.alarmtemplate.vo.AlarmTemplatePageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.alarmtemplate.vo.AlarmTemplateSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.alarmtemplate.AlarmTemplateDO;
import jakarta.validation.*;
import org.jeasy.rules.api.Facts;

/**
 * 告警模板 Service 接口
 *
 * <AUTHOR>
 */
public interface AlarmTemplateService {

    void createAndUpdateAlarmTemplate(AlarmTemplateSaveReqVO saveOrUpdate, Long ruleId, Long actionId);

    List<AlarmTemplateDO> getAlarmTemplateByRuleId(Long ruleId);

    AlarmTemplateDO getAlarmTemplateByActionId(Long actionId);

    /**
     * 查询模板和通知方式
     * @param actionId
     * @return
     */
    AlarmTemplateSaveReqVO getTemplateAndNotificationByActionId(Long actionId);

    /**
     * 发送告警
     * @param actionId
     */
    void sendAlarm(Long actionId, String deviceCode);

    /**
     * 根据ruleId查询模板列表和通知方式
     * @param ruleId
     * @return
     */
    List<AlarmTemplateSaveReqVO> getAlarmTemplateAndNotificationByRuleId(Long ruleId);

    /**
     * 创建告警模板
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createAlarmTemplate(@Valid AlarmTemplateSaveReqVO createReqVO);

    /**
     * 更新告警模板
     *
     * @param updateReqVO 更新信息
     */
    void updateAlarmTemplate(@Valid AlarmTemplateSaveReqVO updateReqVO);

    /**
     * 删除告警模板
     *
     * @param id 编号
     */
    void deleteAlarmTemplate(Long id);

    /**
     * 获得告警模板
     *
     * @param id 编号
     * @return 告警模板
     */
    AlarmTemplateDO getAlarmTemplate(Long id);

    /**
     * 获得告警模板分页
     *
     * @param pageReqVO 分页查询
     * @return 告警模板分页
     */
    PageResult<AlarmTemplateDO> getAlarmTemplatePage(AlarmTemplatePageReqVO pageReqVO);

    void inviedAlarmTemplate(List<Long> ruleActionId);

}
