package cn.powerchina.bjy.link.iot.service.deviceeventlog;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.iot.aop.deviceEventLog.DeviceEventLogPermissionCheck;
import cn.powerchina.bjy.link.iot.common.role.RoleCommon;
import cn.powerchina.bjy.link.iot.controller.admin.deviceeventlog.vo.*;
import cn.powerchina.bjy.link.iot.controller.admin.productmodel.vo.ProductModelPageReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.device.DeviceDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.deviceeventlog.DeviceEventLogDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.product.ProductDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.productmodel.ProductModelDO;
import cn.powerchina.bjy.link.iot.dal.mysql.device.DeviceMapper;
import cn.powerchina.bjy.link.iot.dal.mysql.deviceeventlog.DeviceEventLogMapper;
import cn.powerchina.bjy.link.iot.dal.mysql.product.ProductMapper;
import cn.powerchina.bjy.link.iot.dal.mysql.productmodel.ProductModelMapper;
import cn.powerchina.bjy.link.iot.enums.ThingModeTypeEnum;
import cn.powerchina.bjy.link.iot.service.productmodel.ProductModelService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.link.iot.enums.ErrorCodeConstants.DEVICE_EVENT_LOG_NOT_EXISTS;

/**
 * 设备事件日志 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class DeviceEventLogServiceImpl implements DeviceEventLogService {

    @Resource
    private DeviceEventLogMapper deviceEventLogMapper;
    @Resource
    private ProductModelService productModelService;
    @Resource
    private DeviceMapper deviceMapper;
    @Resource
    private ProductMapper productMapper;
    @Resource
    private ProductModelMapper productModelMapper;
    @Resource
    private RoleCommon roleCommon;

    @Override
    public Long createDeviceEventLog(DeviceEventLogSaveReqVO createReqVO) {
        // 插入
        DeviceEventLogDO deviceEventLog = BeanUtils.toBean(createReqVO, DeviceEventLogDO.class);
        deviceEventLogMapper.insert(deviceEventLog);
        // 返回
        return deviceEventLog.getId();
    }

    @Override
    public void updateDeviceEventLog(DeviceEventLogSaveReqVO updateReqVO) {
        // 校验存在
        validateDeviceEventLogExists(updateReqVO.getId());
        // 更新
        DeviceEventLogDO updateObj = BeanUtils.toBean(updateReqVO, DeviceEventLogDO.class);
        deviceEventLogMapper.updateById(updateObj);
    }

    @Override
    public void deleteDeviceEventLog(Long id) {
        // 校验存在
        validateDeviceEventLogExists(id);
        // 删除
        deviceEventLogMapper.deleteById(id);
    }

    private void validateDeviceEventLogExists(Long id) {
        if (deviceEventLogMapper.selectById(id) == null) {
            throw exception(DEVICE_EVENT_LOG_NOT_EXISTS);
        }
    }

    @Override
    public DeviceEventLogDO getDeviceEventLog(Long id) {
        return deviceEventLogMapper.selectById(id);
    }

    @Override
    public PageResult<DeviceEventLogDO> getDeviceEventLogPage(DeviceEventLogPageReqVO pageReqVO) {
        return deviceEventLogMapper.selectPage(pageReqVO);
    }
    @Override
    @DeviceEventLogPermissionCheck
    public PageResult<DeviceEventLogRespVO> getAllDeviceEventLogPage(DeviceEventLogPageReqVO pageReqVO) {

        if (pageReqVO.getTriggerTime() != null) {
            pageReqVO.setCreateTime(pageReqVO.getTriggerTime());
        }
        PageResult<DeviceEventLogDO> pageResult = deviceEventLogMapper.selectPage(pageReqVO);
        PageResult<DeviceEventLogRespVO> logDOPageResult = BeanUtils.toBean(pageResult, DeviceEventLogRespVO.class);
        if (!CollectionUtils.isEmpty(logDOPageResult.getList())) {
            logDOPageResult.getList().forEach(item -> {
                //查询产品code
                DeviceDO deviceDO = deviceMapper.selectByCode(item.getDeviceCode());
                if (deviceDO != null) {
                    ProductDO productDO = productMapper.selectByCode(deviceDO.getProductCode());
                    if (productDO != null) {
                        item.setProductCode(productDO.getProductCode());
                        item.setProductName(productDO.getProductName());
                    }
                    item.setDeviceSerial(deviceDO.getDeviceSerial());
                }
            });
        }
        return logDOPageResult;
    }

    @Override
    public List<EventTypeVO> getEventTypesByProduct(String productCode) {
        List<ProductModelDO> list = productModelMapper.selectList(new LambdaQueryWrapperX<ProductModelDO>()
                //.eq(ProductModelDO::getProductCode, productCode)
                .eq(ProductModelDO::getThingType, 3)
                .orderByAsc(ProductModelDO::getEventType));
        return list.stream()
                .filter(pm -> pm.getEventType() != null)
                .map(ProductModelDO::getEventType)
                .distinct()
                .map(type -> new EventTypeVO(type, getEventTypeName(type)))
                .toList();
    }

    @Override
    public List<EventNameVO> getEventNamesByProductAndType(String productCode, Integer eventType) {
        List<ProductModelDO> list = productModelMapper.selectList(new LambdaQueryWrapperX<ProductModelDO>()
                .eq(ProductModelDO::getProductCode, productCode)
                .eq(ProductModelDO::getThingType, 3)
                .eq(ProductModelDO::getEventType, eventType));
        return list.stream()
                .map(pm -> new EventNameVO(pm.getThingIdentity(), pm.getThingName()))
                .toList();
    }


    @Override
    public PageResult<Map<String, Object>> getDeviceEventLogRunningState(String productCode, String deviceCode, String thingName, Integer eventType, PageParam pageParam) {
        //根据产品code 获取产品模型属性
        ProductModelPageReqVO productModelPageReqVO = new ProductModelPageReqVO();
        productModelPageReqVO.setProductCode(productCode);
        productModelPageReqVO.setThingType(ThingModeTypeEnum.EVENT.getType());//事件的物模型
        productModelPageReqVO.setThingName(thingName);
        productModelPageReqVO.setEventType(eventType);
        productModelPageReqVO.setPageNo(pageParam.getPageNo());
        productModelPageReqVO.setPageSize(pageParam.getPageSize());
        PageResult<ProductModelDO> productModelPage = productModelService.getProductModelPage(productModelPageReqVO);
        List<ProductModelDO> productModelPageList = productModelPage.getList();
        if (productModelPageList.size() == 0) return null;
        //产品模型标识符
        List<String> thingIdentityList = productModelPageList.stream().map(ProductModelDO::getThingIdentity).toList();
        //根据产品模型标识符获取事件日志
        List<DeviceEventLogDO> deviceEventLogDOS = getDeviceEventLogPage(thingIdentityList, deviceCode, eventType);
        //筛选最新值
        List<DeviceEventLogDO> deviceEventLogs = deviceEventLogDOS.stream().filter(deviceEventLogDO -> deviceEventLogDOS.stream().filter(deviceEventLog -> deviceEventLogDO.getThingIdentity().equals(deviceEventLog.getThingIdentity())).max(Comparator.comparing(DeviceEventLogDO::getCreateTime)).get().getCreateTime().equals(deviceEventLogDO.getCreateTime())).toList();
        //根据  产品模型标识符list 和 设备编码  查询 设备运行状态事件日志
        List<Map<String, Object>> deviceEventAndLogDOS = productModelPageList.stream().map(deviceEventDO -> {
            List<DeviceEventLogDO> tc = deviceEventLogs.stream().filter(t2 ->
                    t2.getThingIdentity().equals(deviceEventDO.getThingIdentity())
            ).toList();
            Map<String, Object> map = new HashMap<>();
            map.put("id", deviceEventDO.getId());
            map.put("thingName", deviceEventDO.getThingName());
            map.put("thingIdentity", deviceEventDO.getThingIdentity());
            map.put("eventType", deviceEventDO.getEventType());
            map.put("remark", deviceEventDO.getRemark());
            if (tc.size() == 1) {
                map.put("thingValue", tc.get(0).getThingValue());
                map.put("createTime", tc.get(0).getCreateTime());
                return map;
            } else {
                map.put("thingValue", "--");
                map.put("createTime", "--");
            }
            return map;
        }).toList();
        return new PageResult<>(deviceEventAndLogDOS, productModelPage.getTotal());
    }

    @Override
    public List<DeviceEventLogDO> getDeviceEventLogPage(List<String> thingIdentityList, String deviceCode, Integer eventType) {
        //查询设备属性
        QueryWrapper<DeviceEventLogDO> wrapper = new QueryWrapper<>();
        wrapper.lambda().select(DeviceEventLogDO::getId, DeviceEventLogDO::getThingIdentity, DeviceEventLogDO::getThingValue, DeviceEventLogDO::getCreateTime);
        wrapper.lambda().in(DeviceEventLogDO::getThingIdentity, thingIdentityList);
        wrapper.lambda().eq(DeviceEventLogDO::getDeviceCode, deviceCode);
        wrapper.lambda().eq(DeviceEventLogDO::getEventType, eventType);
        return deviceEventLogMapper.selectList(wrapper);
    }

    @Override
    public Boolean createDeviceEventLogSaveBatch(List<DeviceEventLogDO> deviceEventLogList) {
        return deviceEventLogMapper.insertBatch(deviceEventLogList);
    }

    private String getEventTypeName(Integer type) {
        if (type == null) return "";
        return switch (type) {
            case 1 -> "信息";
            case 2 -> "告警";
            case 3 -> "故障";
            default -> String.valueOf(type);
        };
    }

}