package cn.powerchina.bjy.link.iot.service.transporttarget;

import cn.hutool.core.collection.CollectionUtil;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.iot.controller.admin.transporttarget.vo.DataTransportTargetVO;
import cn.powerchina.bjy.link.iot.controller.admin.transporttarget.vo.TransportTargetPageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.transporttarget.vo.TransportTargetRespVO;
import cn.powerchina.bjy.link.iot.controller.admin.transporttarget.vo.TransportTargetSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.transportsource.TransportSourceDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.transporttarget.TransportTargetDO;
import cn.powerchina.bjy.link.iot.dal.mysql.transporttarget.TransportTargetMapper;
import cn.powerchina.bjy.link.iot.enums.StatisticImageTypeEnum;
import cn.powerchina.bjy.link.iot.enums.TransportSourceTypeEnum;
import cn.powerchina.bjy.link.iot.enums.TransportTypeEnum;
import cn.powerchina.bjy.link.iot.mq.MqttPublisher;
import cn.powerchina.bjy.link.iot.service.messagestatisticday.MessageStatisticDayService;
import cn.powerchina.bjy.link.iot.util.CryptoUtils;
import com.alibaba.fastjson.JSON;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.hc.client5.http.classic.methods.HttpPost;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.CloseableHttpResponse;
import org.apache.hc.core5.http.io.entity.EntityUtils;
import org.apache.hc.core5.http.io.entity.StringEntity;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.link.iot.enums.ErrorCodeConstants.*;

/**
 * 转发规则-转发目标 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class TransportTargetServiceImpl implements TransportTargetService {

    @Resource
    private TransportTargetMapper transportTargetMapper;

    @Autowired
    private MessageStatisticDayService messageStatisticDayService;

    @Resource
    private MqttPublisher mqttPublisher;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    private final CloseableHttpClient httpClient;


    @Autowired
    public TransportTargetServiceImpl(CloseableHttpClient httpClient) {
        this.httpClient = httpClient;
    }

    @Override
    public Long createTransportTarget(TransportTargetSaveReqVO createReqVO) {
        //转发目标名称校验
        validateNameExists(createReqVO.getRuleId(), createReqVO.getName());
        //转发目标数据量校验
        validateDataCount(createReqVO.getRuleId());
        // 插入
        TransportTargetDO transportTarget = BeanUtils.toBean(createReqVO, TransportTargetDO.class);
        transportTargetMapper.insert(transportTarget);
        // 返回
        return transportTarget.getId();
    }

    @Override
    public void updateTransportTarget(TransportTargetSaveReqVO updateReqVO) {
        // 校验存在
        validateTransportTargetExists(updateReqVO.getId());
        // 更新
        TransportTargetDO updateObj = BeanUtils.toBean(updateReqVO, TransportTargetDO.class);
        transportTargetMapper.updateById(updateObj);
    }

    @Override
    public void deleteTransportTarget(Long id) {
        // 校验存在
        validateTransportTargetExists(id);
        // 删除
        transportTargetMapper.deleteById(id);
    }

    private void validateTransportTargetExists(Long id) {
        if (transportTargetMapper.selectById(id) == null) {
            throw exception(TRANSPORT_TARGET_NOT_EXISTS);
        }
    }

    @Override
    public TransportTargetDO getTransportTarget(Long id) {
        return transportTargetMapper.selectById(id);
    }

    @Override
    public PageResult<TransportTargetRespVO> getTransportTargetPage(TransportTargetPageReqVO pageReqVO) {

        PageResult<TransportTargetDO> transportTargetPage = transportTargetMapper.selectPage(pageReqVO);
        List<TransportTargetRespVO> pageTargetList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(transportTargetPage.getList())) {
            transportTargetPage.getList().forEach(item -> {
                TransportTargetRespVO transportTargetRespVO = BeanUtils.toBean(item, TransportTargetRespVO.class);
                if (TransportTypeEnum.HTTP.getType().equals(transportTargetRespVO.getTransportType())) {
                    transportTargetRespVO.setConfigInfo(transportTargetRespVO.getUrlAddress());
                }
                if (TransportTypeEnum.MQTT.getType().equals(transportTargetRespVO.getTransportType())) {
                    transportTargetRespVO.setConfigInfo(transportTargetRespVO.getTopic());
                }
                transportTargetRespVO.setTransportTypeName(TransportTypeEnum.getDescByType(transportTargetRespVO.getTransportType()));
                pageTargetList.add(transportTargetRespVO);
            });
        }
        return new PageResult<>(pageTargetList, transportTargetPage.getTotal());

    }

    @Override
    public List<TransportTargetDO> getTransportTargetList() {
        LambdaQueryWrapperX<TransportTargetDO> wrapperX = new LambdaQueryWrapperX<>();
        wrapperX.eq(TransportTargetDO::getTransportType, TransportTypeEnum.MQTT.getType());
        return transportTargetMapper.selectList(wrapperX);
    }

    @Override
    public void dataTransportTarget(TransportSourceTypeEnum dataType, List<TransportSourceDO> transportSourceList, Object obj) {
        String messageId = UUID.randomUUID().toString();
        for (TransportSourceDO transportSourceDO : transportSourceList) {
            DataTransportTargetVO dataTransportTargetVO = new DataTransportTargetVO();
            dataTransportTargetVO.setDataType(dataType.getCode());
            dataTransportTargetVO.setMessage(obj);
            String jsonValue = JSON.toJSONString(dataTransportTargetVO);

            LambdaQueryWrapperX<TransportTargetDO> wrapperX = new LambdaQueryWrapperX<>();
            wrapperX.eq(TransportTargetDO::getRuleId, transportSourceDO.getRuleId());
            List<TransportTargetDO> list = transportTargetMapper.selectList(wrapperX);
            for (TransportTargetDO transportTargetDO : list) {
                if (TransportTypeEnum.HTTP.getType().equals(transportTargetDO.getTransportType())) {
                    boolean httpFlag = Boolean.TRUE.equals(
                            redisTemplate.opsForValue().setIfAbsent(messageId + "_" + transportTargetDO.getUrlAddress(), jsonValue, 1, TimeUnit.MINUTES)
                    );
                    if (httpFlag) {
                        try {
                            dataTransportTargetVO.setSecretKey(CryptoUtils.encryptAsymmetrically());
                        } catch (Exception ex) {
                            log.error("数据转发失败：http转发密钥生成失败");
                        }
                        //发送HTTP请求
                        String lockKey = "data:transport:lock";
                        RLock lock = redissonClient.getLock(lockKey);
                        try {
                            HttpPost httpPost = new HttpPost(transportTargetDO.getUrlAddress());
                            httpPost.addHeader("Content-Type", "application/json; charset=utf-8");
                            httpPost.addHeader("Authorization", "Bearer " + System.currentTimeMillis());
                            httpPost.addHeader("tenant-id", "100");
                            httpPost.setEntity(new StringEntity(jsonValue, StandardCharsets.UTF_8));
                            CloseableHttpResponse response = httpClient.execute(httpPost);
                            String result = EntityUtils.toString(response.getEntity());
                            log.info("数据转发http请求发送成功 {}", result);

                            lock.lock();
                            messageStatisticDayService.insertMessageStatisticDay(new Date(), StatisticImageTypeEnum.TRANSPORT.getType(), 1L);

                        } catch (Exception e) {
                            log.error("设备数据转发http请求发送失败 {}", e.getMessage());
                        } finally {
                            lock.unlock();
                        }
                    }
                }

                if (TransportTypeEnum.MQTT.getType().equals(transportTargetDO.getTransportType())) {
                    //发送MQTT消息
                    boolean mqttFlag = Boolean.TRUE.equals(
                            redisTemplate.opsForValue().setIfAbsent(messageId + "_" + transportTargetDO.getTopic(), jsonValue, 1, TimeUnit.MINUTES)
                    );
                    if (mqttFlag) {
                        String lockKey = "data:transport:lock";
                        RLock lock = redissonClient.getLock(lockKey);
                        try {
                            mqttPublisher.publish(transportTargetDO.getTopic(), jsonValue);
                            //
                            lock.lock();
                            messageStatisticDayService.insertMessageStatisticDay(new Date(), StatisticImageTypeEnum.TRANSPORT.getType(), 1L);

                        } catch (Exception e) {
                            log.error("设备数据转发mqtt请求发送失败 {}", e.getMessage());
                        } finally {
                            lock.unlock();
                        }
                    }
                }
            }
        }
    }


    private void validateDataCount(Long ruleId) {
        Long count = transportTargetMapper.selectCount(new LambdaQueryWrapperX<TransportTargetDO>().eq(TransportTargetDO::getRuleId, ruleId));
        if (count >= 10) {
            throw exception(TRANSPORT_TARGET_NOT_TEN);
        }
    }

    private void validateNameExists(Long ruleId, String name) {
        TransportTargetDO transportTargetDO = transportTargetMapper.selectOne(new LambdaQueryWrapperX<TransportTargetDO>()
                .eq(TransportTargetDO::getRuleId, ruleId).eq(TransportTargetDO::getName, name));
        if (Objects.nonNull(transportTargetDO)) {
            throw exception(TRANSPORT_TARGET_NAME_EXISTS);
        }
    }

}