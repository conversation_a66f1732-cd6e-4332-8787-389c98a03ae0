package cn.powerchina.bjy.link.iot.dal.mysql.productmodel;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.iot.controller.admin.productmodel.vo.ProductModelPageReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.productmodel.ProductModelDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 产品物模型 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ProductModelMapper extends BaseMapperX<ProductModelDO> {

    default PageResult<ProductModelDO> selectPage(ProductModelPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ProductModelDO>()
                .eqIfPresent(ProductModelDO::getProductCode, reqVO.getProductCode())
                .likeIfPresent(ProductModelDO::getThingIdentity, reqVO.getThingIdentity())
                .likeIfPresent(ProductModelDO::getThingName, reqVO.getThingName())
                .eqIfPresent(ProductModelDO::getThingType, reqVO.getThingType())
                .eqIfPresent(ProductModelDO::getDatatype, reqVO.getDatatype())
                .eqIfPresent(ProductModelDO::getReadWriteType, reqVO.getReadWriteType())
                .eqIfPresent(ProductModelDO::getEventType, reqVO.getEventType())
                .eqIfPresent(ProductModelDO::getInputParams, reqVO.getInputParams())
                .eqIfPresent(ProductModelDO::getOutputParams, reqVO.getOutputParams())
                .eqIfPresent(ProductModelDO::getExtra, reqVO.getExtra())
                .eqIfPresent(ProductModelDO::getRemark, reqVO.getRemark())
                .betweenIfPresent(ProductModelDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ProductModelDO::getId));
    }

    @Select("select * from iot_product_model where product_code = #{productCode} and thing_identity = #{identify} and deleted = 0")
    ProductModelDO selectByProductCodeAndIdentify(@Param("productCode") String productCode, @Param("identify") String identify);

}