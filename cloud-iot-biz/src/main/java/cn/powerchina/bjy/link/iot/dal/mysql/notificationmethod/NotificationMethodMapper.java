package cn.powerchina.bjy.link.iot.dal.mysql.notificationmethod;


import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.iot.controller.admin.notificationmethod.vo.NotificationMethodPageReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.notificationmethod.NotificationMethodDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 通知方式 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface NotificationMethodMapper extends BaseMapperX<NotificationMethodDO> {

    default PageResult<NotificationMethodDO> selectPage(NotificationMethodPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<NotificationMethodDO>()
                .eqIfPresent(NotificationMethodDO::getAlarmTemplateId, reqVO.getAlarmTemplateId())
                .eqIfPresent(NotificationMethodDO::getNotificationMethod, reqVO.getNotificationMethod())
                .eqIfPresent(NotificationMethodDO::getNotificationAccount, reqVO.getNotificationAccount())
                .eqIfPresent(NotificationMethodDO::getNotificationContent, reqVO.getNotificationContent())
                .betweenIfPresent(NotificationMethodDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(NotificationMethodDO::getId));
    }

}
