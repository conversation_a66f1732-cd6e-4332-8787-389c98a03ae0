package cn.powerchina.bjy.link.iot.controller.admin.instructiondownlog;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.link.iot.controller.admin.instructiondownlog.vo.InstructionDownLogPageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.instructiondownlog.vo.InstructionDownLogRespVO;
import cn.powerchina.bjy.link.iot.controller.admin.instructiondownlog.vo.InstructionDownLogSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.instructiondownlog.InstructionDownLogDO;
import cn.powerchina.bjy.link.iot.service.instructiondownlog.InstructionDownLogService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 指令下发操作记录")
@RestController
@RequestMapping("/iot/instruction-down-log")
@Validated
public class InstructionDownLogController {

    @Resource
    private InstructionDownLogService instructionDownLogService;

    @PostMapping("/create")
    @Operation(summary = "创建指令下发操作记录")
//    @PreAuthorize("@ss.hasPermission('iot:instruction-down-log:create')")
    public CommonResult<Long> createInstructionDownLog(@Valid @RequestBody InstructionDownLogSaveReqVO createReqVO) {
        return success(instructionDownLogService.createInstructionDownLog(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新指令下发操作记录")
//    @PreAuthorize("@ss.hasPermission('iot:instruction-down-log:update')")
    public CommonResult<Boolean> updateInstructionDownLog(@Valid @RequestBody InstructionDownLogSaveReqVO updateReqVO) {
        instructionDownLogService.updateInstructionDownLog(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除指令下发操作记录")
    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('iot:instruction-down-log:delete')")
    public CommonResult<Boolean> deleteInstructionDownLog(@RequestParam("id") Long id) {
        instructionDownLogService.deleteInstructionDownLog(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得指令下发操作记录")
    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('iot:instruction-down-log:query')")
    public CommonResult<InstructionDownLogRespVO> getInstructionDownLog(@RequestParam("id") Long id) {
        InstructionDownLogDO instructionDownLog = instructionDownLogService.getInstructionDownLog(id);
        return success(BeanUtils.toBean(instructionDownLog, InstructionDownLogRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得指令下发操作记录分页")
//    @PreAuthorize("@ss.hasPermission('iot:instruction-down-log:query')")
    public CommonResult<PageResult<InstructionDownLogRespVO>> getInstructionDownLogPage(@Valid InstructionDownLogPageReqVO pageReqVO) {
        return success(instructionDownLogService.getInstructionDownLogVOPage(pageReqVO));
    }

}