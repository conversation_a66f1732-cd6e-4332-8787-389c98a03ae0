package cn.powerchina.bjy.link.iot.controller.admin.edgegateway;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.link.iot.controller.admin.edgegateway.bo.EdgeGatewayBO;
import cn.powerchina.bjy.link.iot.controller.admin.edgegateway.vo.*;
import cn.powerchina.bjy.link.iot.dal.dataobject.edgegateway.EdgeGatewayDeviceDO;
import cn.powerchina.bjy.link.iot.service.edgegateway.EdgeGatewayService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 边缘网关")
@RestController
@RequestMapping("/iot/edge-gateway")
@Validated
public class EdgeGatewayController {

    @Resource
    private EdgeGatewayService edgeGatewayService;

    @PostMapping("/create")
    @Operation(summary = "创建边缘网关")
//    @PreAuthorize("@ss.hasPermission('iot:edge-gateway:create')")
    public CommonResult<Long> createEdgeGateway(@Valid @RequestBody EdgeGatewaySaveReqVO createReqVO) {
        return success(edgeGatewayService.createEdgeGateway(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新边缘网关")
//    @PreAuthorize("@ss.hasPermission('iot:edge-gateway:update')")
    public CommonResult<Boolean> updateEdgeGateway(@Valid @RequestBody EdgeGatewaySaveReqVO updateReqVO) {
        edgeGatewayService.updateEdgeGateway(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除边缘网关")
    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('iot:edge-gateway:delete')")
    public CommonResult<Boolean> deleteEdgeGateway(@RequestParam("id") Long id) {
        edgeGatewayService.deleteEdgeGateway(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得边缘网关")
    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('iot:edge-gateway:query')")
    public CommonResult<EdgeGatewayRespVO> getEdgeGateway(@RequestParam("id") Long id) {
        EdgeGatewayBO edgeGateway = edgeGatewayService.getEdgeGatewayBO(id);
        return success(BeanUtils.toBean(edgeGateway, EdgeGatewayRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得边缘网关分页")
//    @PreAuthorize("@ss.hasPermission('iot:edge-gateway:query')")
    public CommonResult<PageResult<EdgeGatewayRespVO>> getEdgeGatewayPage(@Valid EdgeGatewayPageReqVO pageReqVO) {
        PageResult<EdgeGatewayBO> pageResult = edgeGatewayService.getEdgeGatewayPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, EdgeGatewayRespVO.class));
    }

    @GetMapping("/page2")
    @Operation(summary = "获得边缘网关分页")
//    @PreAuthorize("@ss.hasPermission('iot:edge-gateway:query')")
    public CommonResult<PageResult<EdgeGatewayRespVO>> getEdgeGatewayDevicePage(@Valid EdgeGatewayPageReqVO pageReqVO) {
        PageResult<EdgeGatewayDeviceDO> pageResult = edgeGatewayService.getEdgeGatewayDevicePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, EdgeGatewayRespVO.class));
    }

    @GetMapping("/online-status")
    @Operation(summary = "边缘网关设备在线状态获取")
//    @PreAuthorize("@ss.hasPermission('iot:edge-gateway:query')")
    @Parameter(name = "edgeCode", description = "网关实例编码")
    public CommonResult<Boolean> onlineStatus(@RequestParam(value = "edgeCode") String edgeCode) {
        edgeGatewayService.edgeOnlineCheck(edgeCode);
        return success(true);
    }

    @GetMapping("/device/page")
    @Operation(summary = "获得边缘实例下一级设备分页")
    public CommonResult<PageResult<EdgeDevicePageResVO>> getEdgeDevicePage(@Valid EdgeDevicePageReqVO pageReqVO) {
        PageResult<EdgeDevicePageResVO> pageResult = edgeGatewayService.getEdgeDevicePage(pageReqVO);
        return success(pageResult);
    }

    @GetMapping("/device/child/page")
    @Operation(summary = "获得边缘实例下一级设备的子设备分页分页")
    public CommonResult<PageResult<EdgeChildDevicePageResVO>> getEdgeChildDevicePage(@Valid EdgeChildDevicePageReqVO pageReqVO) {
        PageResult<EdgeChildDevicePageResVO> pageResult = edgeGatewayService.getEdgeChildDevicePage(pageReqVO);
        return success(pageResult);
    }



    @PostMapping("/camera-move")
    @Operation(summary = "控制摄像头移动")
    public CommonResult<Boolean> cameraMove(@Valid @RequestBody CameraMoveReqVO moveReqVO) {
        edgeGatewayService.processCameraMove(moveReqVO);
        return success(true);
    }

    @PostMapping("/convert-video")
    @Operation(summary = "转换视频")
    public CommonResult<String> convertVideo(@Valid @RequestBody ConvertVideoReqVO convertVideoReqVO) throws Exception {
        String url = edgeGatewayService.convertVideo(convertVideoReqVO);
        return success(url);
    }
    @GetMapping("/close-video")
    public CommonResult<Boolean> closeVideo(@RequestParam("videoName") String videoName) {
        edgeGatewayService.closeVideo(videoName);
        return success(true);
    }
    @GetMapping("/keepAlive-video")
    public CommonResult<Boolean> keepAliveVideo(@RequestParam("videoName") String videoName) {
        edgeGatewayService.keepAliveVideo(videoName);
        return success(true);
    }
}