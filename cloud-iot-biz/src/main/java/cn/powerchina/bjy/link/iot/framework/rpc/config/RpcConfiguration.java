package cn.powerchina.bjy.link.iot.framework.rpc.config;

import cn.powerchina.bjy.cloud.infra.api.config.ConfigApi;
import cn.powerchina.bjy.cloud.system.api.permission.PermissionApi;
import cn.powerchina.bjy.cloud.system.api.permission.RoleApi;
import cn.powerchina.bjy.cloud.system.api.user.AdminUserApi;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Configuration;

//@Configuration(proxyBeanMethods = false)
//@EnableFeignClients(clients = {ConfigApi.class, AdminUserApi.class, RoleApi.class, PermissionApi.class})
public class RpcConfiguration {
}
