package cn.powerchina.bjy.link.iot.controller.admin.devicelog.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 设备日志 Response VO")
@Data
@ExcelIgnoreUnannotated
public class DeviceLogRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "17775")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "设备编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("设备编号")
    private String deviceCode;

    @Schema(description = "日志级别(error=0,warn=1,info=2,debug=3,other=4)", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer logLevel;

    @Schema(description = "日志大小", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("日志大小")
    private Integer fileSize;

    @Schema(description = "日志文件路径", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("日志文件路径")
    private String path;

    @Schema(description = "备注", example = "你说的对")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;



}