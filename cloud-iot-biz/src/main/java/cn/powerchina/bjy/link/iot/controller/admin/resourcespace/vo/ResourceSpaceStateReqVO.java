package cn.powerchina.bjy.link.iot.controller.admin.resourcespace.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(description = "管理后台 - 数据转发规则/切换数据转发启用状态 Request VO")
@Data
public class ResourceSpaceStateReqVO {

    @Schema(description = "主键")
    @NotNull(message = "id不能为空")
    private Long id;

    @Schema(description = "切换后的状态值")
    @NotNull(message = "切换后的状态值不能为空")
    private Integer state;
}
