package cn.powerchina.bjy.link.iot.api.devicegroupdetail;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.link.iot.api.devicegroup.dto.DeviceRespDTO;
import cn.powerchina.bjy.link.iot.dal.dataobject.device.DeviceDO;
import cn.powerchina.bjy.link.iot.service.device.DeviceService;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @Description: 设备分组
 * @Author: yhx
 * @CreateDate: 2024/9/2
 */
@RestController
@Validated
public class DeviceGroupDetailApiImpl implements DeviceGroupDetailApi {

    @Resource
    private DeviceService deviceService;

    @Override
    public CommonResult<List<DeviceRespDTO>> getDeviceByDeviceCodeList(List<String> deviceCodeList) {
        List<DeviceRespDTO> respDTOList = new ArrayList<>();
        List<DeviceDO> deviceList = deviceService.getDeviceByCodeList(deviceCodeList);
        if (!CollectionUtils.isEmpty(deviceList)) {
            respDTOList.addAll(deviceList.stream().map(item -> {
                DeviceRespDTO respDTO = new DeviceRespDTO();
                BeanUtils.copyProperties(item, respDTO);
                //设置父设备
                if (StringUtils.isNotBlank(respDTO.getParentCode())) {
                    DeviceDO parentDeviceDO = deviceService.getDevice(respDTO.getParentCode());
                    if (Objects.nonNull(parentDeviceDO)) {
                        respDTO.setParentName(parentDeviceDO.getDeviceName());
                        respDTO.setParentProductCode(parentDeviceDO.getProductCode());
                        respDTO.setParentSerial(parentDeviceDO.getDeviceSerial());
                    }
                }
                return respDTO;
            }).toList());
        }
        return CommonResult.success(respDTOList);
    }
}
