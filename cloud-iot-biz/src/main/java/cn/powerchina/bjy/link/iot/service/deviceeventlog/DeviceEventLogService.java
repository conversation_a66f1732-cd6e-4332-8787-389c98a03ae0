package cn.powerchina.bjy.link.iot.service.deviceeventlog;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.link.iot.controller.admin.deviceeventlog.vo.*;
import cn.powerchina.bjy.link.iot.dal.dataobject.deviceeventlog.DeviceEventLogDO;
import jakarta.validation.Valid;

import java.util.List;
import java.util.Map;

/**
 * 设备事件日志 Service 接口
 *
 * <AUTHOR>
 */
public interface DeviceEventLogService {

    /**
     * 创建设备事件日志
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createDeviceEventLog(@Valid DeviceEventLogSaveReqVO createReqVO);

    /**
     * 更新设备事件日志
     *
     * @param updateReqVO 更新信息
     */
    void updateDeviceEventLog(@Valid DeviceEventLogSaveReqVO updateReqVO);

    /**
     * 删除设备事件日志
     *
     * @param id 编号
     */
    void deleteDeviceEventLog(Long id);

    /**
     * 获得设备事件日志
     *
     * @param id 编号
     * @return 设备事件日志
     */
    DeviceEventLogDO getDeviceEventLog(Long id);

    /**
     * 获得设备事件日志分页
     *
     * @param pageReqVO 分页查询
     * @return 设备事件日志分页
     */
    PageResult<DeviceEventLogDO> getDeviceEventLogPage(DeviceEventLogPageReqVO pageReqVO);

    /**
     * 获得设备事件日志分页
     *
     * @param pageReqVO 分页查询
     * @return 设备事件日志分页
     */
    PageResult<DeviceEventLogRespVO> getAllDeviceEventLogPage(DeviceEventLogPageReqVO pageReqVO);

    List<EventTypeVO> getEventTypesByProduct(String productCode);

    List<EventNameVO> getEventNamesByProductAndType(String productCode, Integer eventType);

    /**
     * 根据设备code和产品code获取设备事件日志分页
     * @param productCode
     * @param deviceCode
     * @param thingName
     * @param eventType
     * @param pageParam
     * @return
     */
    PageResult<Map<String, Object>> getDeviceEventLogRunningState(String productCode, String deviceCode, String thingName, Integer eventType, PageParam pageParam);

    /**
     * 根据产品模型标识符和设备code获取设备每个类型最新的事件日志(分页)
     * @param thingIdentityList 产品模型标识符
     * @param deviceCode 设备code
     * @param eventType 事件类型
     * @return
     */
    List<DeviceEventLogDO> getDeviceEventLogPage(List<String> thingIdentityList, String deviceCode,Integer eventType);

    /**
     * 创建多个事件日志
     */
    Boolean createDeviceEventLogSaveBatch(List<DeviceEventLogDO> deviceEventLogList);
}