package cn.powerchina.bjy.link.iot.controller.admin.index.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 设备消息数按日统计 Response VO")
@Data
@ExcelIgnoreUnannotated
public class StatisticDayImageRespVO {

    @Schema(description = "统计时间")
    private List<String> statisticTime;

    @Schema(description = "统计数量")
    private List<Long> statisticCount;

}
