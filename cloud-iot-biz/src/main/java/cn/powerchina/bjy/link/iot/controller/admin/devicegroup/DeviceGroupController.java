package cn.powerchina.bjy.link.iot.controller.admin.devicegroup;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.link.iot.controller.admin.devicegroup.vo.DeviceGroupPageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.devicegroup.vo.DeviceGroupRespVO;
import cn.powerchina.bjy.link.iot.controller.admin.devicegroup.vo.DeviceGroupSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.devicegroup.DeviceGroupDO;
import cn.powerchina.bjy.link.iot.service.devicegroup.DeviceGroupService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 设备分组")
@RestController
@RequestMapping("/iot/device/group")
@Validated
public class DeviceGroupController {

    @Resource
    private DeviceGroupService deviceGroupService;

    @PostMapping("/create")
    @Operation(summary = "创建设备分组")
//    @PreAuthorize("@ss.hasPermission('iot:device-group:create')")
    public CommonResult<Long> createDeviceGroup(@Valid @RequestBody DeviceGroupSaveReqVO createReqVO) {
        return success(deviceGroupService.createDeviceGroup(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新设备分组")
//    @PreAuthorize("@ss.hasPermission('iot:device-group:update')")
    public CommonResult<Boolean> updateDeviceGroup(@Valid @RequestBody DeviceGroupSaveReqVO updateReqVO) {
        deviceGroupService.updateDeviceGroup(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除设备分组")
    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('iot:device-group:delete')")
    public CommonResult<Boolean> deleteDeviceGroup(@RequestParam("id") Long id) {
        deviceGroupService.deleteDeviceGroup(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得设备分组")
//    @PreAuthorize("@ss.hasPermission('iot:device-group:query')")
    public CommonResult<DeviceGroupRespVO> getDeviceGroup(@RequestParam("id") Long id) {
        DeviceGroupDO deviceGroup = deviceGroupService.getDeviceGroup(id);
        return success(BeanUtils.toBean(deviceGroup, DeviceGroupRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得设备分组列表")
//    @PreAuthorize("@ss.hasPermission('iot:device-group:query')")
    public CommonResult<List<DeviceGroupRespVO>> getDeviceGroupPage(@Valid DeviceGroupPageReqVO pageReqVO) {
        PageResult<DeviceGroupDO> pageResult = deviceGroupService.getDeviceGroupPage(pageReqVO);
        PageResult<DeviceGroupRespVO> pageResult2 = BeanUtils.toBean(pageResult, DeviceGroupRespVO.class);
        return success(pageResult2.getList());
    }

}