package cn.powerchina.bjy.link.iot.service.sceneruletrigger;


import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.link.iot.controller.admin.sceneruletrigger.vo.SceneRuleTriggerPageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.sceneruletrigger.vo.SceneRuleTriggerSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.datapermissions.DataPermissionsDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.sceneruletrigger.SceneRuleTriggerDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 规则触发/条件限制 Service 接口
 *
 * <AUTHOR>
 */
public interface SceneRuleTriggerService {

    void createAndUpdateSceneRuleTrigger(List<SceneRuleTriggerSaveReqVO> saveOrUpdateList, Long ruleId, Integer conditionType);

    List<SceneRuleTriggerDO> getSceneRuleTriggerByRuleId(Long ruleId, Integer conditionType);

    /**
     * 根据失效状态获取数据
     *
     * @param ruleId
     * @param invalid
     * @return
     */
    List<SceneRuleTriggerDO> getSceneRuleTriggerByInvalid(Long ruleId, Integer invalid);

    /**
     * 创建规则触发/条件限制
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createSceneRuleTrigger(@Valid SceneRuleTriggerSaveReqVO createReqVO);

    /**
     * 更新规则触发/条件限制
     *
     * @param updateReqVO 更新信息
     */
    void updateSceneRuleTrigger(@Valid SceneRuleTriggerSaveReqVO updateReqVO);

    /**
     * 删除规则触发/条件限制
     *
     * @param id 编号
     */
    void deleteSceneRuleTrigger(Long id);

    /**
     * 获得规则触发/条件限制
     *
     * @param id 编号
     * @return 规则触发/条件限制
     */
    SceneRuleTriggerDO getSceneRuleTrigger(Long id);

    /**
     * 获得规则触发/条件限制分页
     *
     * @param pageReqVO 分页查询
     * @return 规则触发/条件限制分页
     */
    PageResult<SceneRuleTriggerDO> getSceneRuleTriggerPage(SceneRuleTriggerPageReqVO pageReqVO);

    /**
     * 触发条件、限制条件场景删除
     *
     * @param deviceCode
     * @return
     */
    List<Long> deleteSceneRuleTrigger(String productCode, String deviceCode);

    /**
     * 触发条件、限制条件场景失效
     *
     * @param userId
     * @param dataPermissionsList
     * @return
     */
    List<Long> invalidSceneRuleTrigger(Long userId, List<DataPermissionsDO> dataPermissionsList);

    /**
     * 触发条件、限制条件场景生效
     *
     * @param userId
     * @param dataPermissionsList
     * @return
     */
    List<Long> effectiveSceneRuleTrigger(Long userId, List<DataPermissionsDO> dataPermissionsList);

}
