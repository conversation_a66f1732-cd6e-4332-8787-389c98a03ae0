package cn.powerchina.bjy.link.iot.controller.admin.sceneruletimetrigger.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDate;
import java.util.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 定时触发 Response VO")
@Data
@ExcelIgnoreUnannotated
public class SceneRuleTimeTriggerRespVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "26614")
    @ExcelProperty("主键id")
    private Long id;

    @Schema(description = "规则ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1259")
    @ExcelProperty("规则ID")
    private Long ruleId;

    @Schema(description = "执行时刻")
    @ExcelProperty("执行时刻")
    private String executionTime;

    @Schema(description = "重复类型:1-每天,2-指定日期,3-指定周期,4-自定义", example = "1")
    @ExcelProperty("重复类型:1-每天,2-指定日期,3-指定周期,4-自定义")
    private Integer repeatType;

    @Schema(description = "开始日期/指定日期")
    @ExcelProperty("开始日期/指定日期")
    private LocalDate repeatStartDate;

    @Schema(description = "结束日期")
    @ExcelProperty("结束日期")
    private LocalDate repeatEndDate;

    @Schema(description = "每周重复的星期几,如:1,2,3,4,5,6,7")
    @ExcelProperty("每周重复的星期几,如:1,2,3,4,5,6,7")
    private String repeatWeekDays;

    @Schema(description = "cron表达式")
    @ExcelProperty("cron表达式")
    private String cronExpression;

    @Schema(description = "排序", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("排序")
    private Integer sort;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
