package cn.powerchina.bjy.link.iot.listener;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.powerchina.bjy.cloud.framework.common.util.json.JsonUtils;
import cn.powerchina.bjy.link.iot.dto.up.EdgeCheckConfigStatus;
import cn.powerchina.bjy.link.iot.dto.up.EdgeReadPropertyValue;
import cn.powerchina.bjy.link.iot.enums.IotTopicConstant;
import cn.powerchina.bjy.link.iot.enums.StatisticImageTypeEnum;
import cn.powerchina.bjy.link.iot.service.devicepropertylog.DevicePropertyLogService;
import cn.powerchina.bjy.link.iot.service.messagestatisticday.MessageStatisticDayService;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.annotation.RocketMQMessageListener;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.apache.rocketmq.client.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.util.Date;

/**
 * 监听上报的属性
 *
 * <AUTHOR>
 **/

@Slf4j
@Component
@RocketMQMessageListener(topic = IotTopicConstant.TOPIC_DEVICE_PROPERTY_RESULT, consumerGroup = IotTopicConstant.GROUP_DEVICE_PROPERTY_RESULT, requestTimeout = 10, consumptionThreadCount = 10
)
public class PropertyReceiver implements RocketMQListener {

    @Resource
    private DevicePropertyLogService propertyLogService;

    @Autowired
    private MessageStatisticDayService messageStatisticDayService;


    @Override
    public ConsumeResult consume(MessageView messageView) {
        if (ObjectUtil.isNull(messageView)) {
            log.error("MessageView is null, skip consumption");
            return ConsumeResult.SUCCESS;
        }
        try {
            // 1. 解析消息体
            EdgeReadPropertyValue propertyValue = parseMessageBody(messageView);
            if (propertyValue == null) {
                log.error("MessageView is null, skip consumption");
                return ConsumeResult.SUCCESS; // 无效消息直接标记失败
            }

            // 解析属性
            if (CollectionUtil.isEmpty(propertyValue.getPropertyValueList())) {
                log.info("上报属性为空.");
                return ConsumeResult.SUCCESS;
            }
            propertyLogService.savePropertyLog(propertyValue.getPropertyValueList(), propertyValue.getEdgeCode());
            messageStatisticDayService.insertMessageStatisticDay(new Date(), StatisticImageTypeEnum.MESSAGE.getType(), 1L);
        } catch (Exception e) {
            log.error("上报属性解析异常--->error,entityDTO={}", JsonUtils.toJsonString(messageView), e);
        }
        return ConsumeResult.SUCCESS;
    }

    /**
     * 解析消息体为实体类
     */
    private EdgeReadPropertyValue parseMessageBody(MessageView messageView) {
        try {
            ByteBuffer body = messageView.getBody();
            if (body.remaining() == 0) {
                log.error("Message body is empty, messageId: {}", messageView.getMessageId());
                return null;
            }

            String message = StandardCharsets.UTF_8.decode(body).toString();
            return JSON.parseObject(message, EdgeReadPropertyValue.class);

        } catch (JSONException e) {
            log.error("Failed to parse JSON, messageId: {}, content: {}", messageView.getMessageId(), messageView.getBody(), e);
            return null;
        }
    }
}
