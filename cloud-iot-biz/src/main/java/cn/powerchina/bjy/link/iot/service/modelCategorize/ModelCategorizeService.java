package cn.powerchina.bjy.link.iot.service.modelCategorize;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.link.iot.controller.admin.modelCategorize.bo.ModelCategorizeBO;
import cn.powerchina.bjy.link.iot.controller.admin.modelCategorize.vo.ModelCategorizeListReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.modelCategorize.vo.ModelCategorizePageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.modelCategorize.vo.ModelCategorizeRespVO;
import cn.powerchina.bjy.link.iot.controller.admin.modelCategorize.vo.ModelCategorizeSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.modelCategorize.ModelCategorizeDO;
import cn.powerchina.bjy.link.iot.model.TreeSelect;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 物模型分类信息表
 *
 * <AUTHOR>
 * @date 2025-03-25 11:00:21
 */
public interface ModelCategorizeService {

    /**
     * 获得产品分页
     *
     * @param pageReqVO 分页查询
     * @return 产品分页
     */
    PageResult<ModelCategorizeBO> getModelCategorizePage(ModelCategorizePageReqVO pageReqVO);

    List<ModelCategorizeDO> getAllList(ModelCategorizeListReqVO pageReqVO);

    List<ModelCategorizeDO> getAllLists(ModelCategorizeListReqVO pageReqVO);

    /**
     * 构建前端所需要下拉树结构
     *
     * @param list 分类列表
     * @return 下拉树结构列表
     */
    public List<TreeSelect> buildMenuTreeSelect(List<ModelCategorizeRespVO> list);

    /**
     * 构建前端所需要树结构
     *
     * @param list 分类列表
     * @return 树结构列表
     */
    public List<ModelCategorizeRespVO> buildMenuTree(List<ModelCategorizeRespVO> list);

    List<ModelCategorizeDO> getListByCategorizeId(Long id);

    /**
     * 获得产品
     *
     * @param id 编号
     * @return 产品
     */
    ModelCategorizeDO getModelCategorize(Long id);

    /**
     * 获得产品
     *
     * @param id 编号
     * @return 产品
     */
    ModelCategorizeBO getModelCategorizeBO(Long id);
    /**
     * 创建产品
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createModelCategorize(@Valid ModelCategorizeSaveReqVO createReqVO);

    /**
     * 更新产品
     *
     * @param updateReqVO 更新信息
     */
    void updateModelCategorize(@Valid ModelCategorizeSaveReqVO updateReqVO);

    void updateModelCategorize(ModelCategorizeDO updateModelCategorizeDO);

    /**
     * 删除产品
     *
     * @param id 编号
     */
    void deleteModelCategorize(Long id);

}

