package cn.powerchina.bjy.link.iot.service.sceneruletimetrigger;


import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.link.iot.controller.admin.sceneruletimetrigger.vo.SceneRuleTimeTriggerPageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.sceneruletimetrigger.vo.SceneRuleTimeTriggerSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.scenerule.SceneRuleDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.sceneruletimetrigger.SceneRuleTimeTriggerDO;
import jakarta.validation.*;

import java.util.List;

/**
 * 定时触发 Service 接口
 *
 * <AUTHOR>
 */
public interface SceneRuleTimeTriggerService {

    /**
     * 批量新增或者编辑
     *
     * @param
     */
    void createAndUpdateSceneRuleTimeTrigger(SceneRuleTimeTriggerSaveReqVO saveOrUpdate, Long ruleId, Long triggerId);

    /**
     * 创建定时触发
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createSceneRuleTimeTrigger(@Valid SceneRuleTimeTriggerSaveReqVO createReqVO);

    /**
     * 更新定时触发
     *
     * @param updateReqVO 更新信息
     */
    void updateSceneRuleTimeTrigger(@Valid SceneRuleTimeTriggerSaveReqVO updateReqVO);

    /**
     * 删除定时触发
     *
     * @param id 编号
     */
    void deleteSceneRuleTimeTrigger(Long id);

    /**
     * 获得定时触发
     *
     * @param id 编号
     * @return 定时触发
     */
    SceneRuleTimeTriggerDO getSceneRuleTimeTrigger(Long id);

    /**
     * 获得定时触发分页
     *
     * @param pageReqVO 分页查询
     * @return 定时触发分页
     */
    PageResult<SceneRuleTimeTriggerDO> getSceneRuleTimeTriggerPage(SceneRuleTimeTriggerPageReqVO pageReqVO);

    List<SceneRuleTimeTriggerDO> getSceneRuleTimeTriggerByRuleId(Long ruleId);

    SceneRuleTimeTriggerDO getSceneRuleTimeTriggerByTriggerId(Long triggerId);

    /**
     * 根据定时任务id查询规则
     *
     * @param jobId
     * @return
     */
    SceneRuleDO getSceneRuleByJobId(Integer jobId);

}
