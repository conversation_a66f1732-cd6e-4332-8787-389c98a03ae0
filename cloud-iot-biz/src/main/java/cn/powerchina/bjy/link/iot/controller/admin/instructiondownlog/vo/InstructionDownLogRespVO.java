package cn.powerchina.bjy.link.iot.controller.admin.instructiondownlog.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 指令下发操作记录 Response VO")
@Data
@ExcelIgnoreUnannotated
public class InstructionDownLogRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "12779")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "设备编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("设备编号")
    private String deviceCode;

    @Schema(description = "物模型类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("物模型类型")
    private Integer thingType;

    @Schema(description = "物模型标识符", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("物模型标识符")
    private String thingIdentity;

    @Schema(description = "物模型名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "")
    @ExcelProperty("物模型名称")
    private String thingName;

    @Schema(description = "控制源（0-web；1-ios；2-android）", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("控制源（0-web；1-ios；2-android）")
    private Integer operatorSource;

    @Schema(description = "messageid", requiredMode = Schema.RequiredMode.REQUIRED, example = "7321")
    @ExcelProperty("messageid")
    private String messageId;

    @Schema(description = "输入参数", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("输入参数")
    private String inputParams;

    @Schema(description = "输出参数", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("输出参数")
    private String outputParams;

    @Schema(description = "上行消耗时间(ms)", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer upConsumeTime;

    @Schema(description = "下行消耗时间(ms)", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer downConsumeTime;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "创建人", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建人")
    private String creator;

    @Schema(description = "创建人名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建人名称")
    private String creatorName;

}