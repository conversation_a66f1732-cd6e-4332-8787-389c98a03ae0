package cn.powerchina.bjy.link.iot.util;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.RandomUtil;
import org.apache.commons.lang3.StringUtils;

/**
 * CodeGenerator
 *
 * <AUTHOR>
 **/
public class CodeGenerator {

    private final static int PAD_LENGTH = 3;
    private final static char PAD_CHAR = '0';

    /**
     * 根据类型生成编码
     * 生成规则：${前缀}${时间戳}${随机数}
     *
     * @param type 类型
     * @return 编码
     */
    public static String createCode(String type) {
        int number = RandomUtil.randomInt(1000);
        return type + System.currentTimeMillis() + StringUtils.leftPad(String.valueOf(number), PAD_LENGTH, PAD_CHAR);
    }


    /**
     * 生成日志编码
     * 生成规则：msg+uuid
     *
     * @return 编码
     */
    public static String createMsgUUID() {
        return "msg" + IdUtil.randomUUID().replace("-", "");
    }
}
