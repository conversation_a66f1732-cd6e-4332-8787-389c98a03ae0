package cn.powerchina.bjy.link.iot.dal.mysql.transportsource;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.iot.controller.admin.transportsource.vo.TransportSourcePageReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.transportsource.TransportSourceDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 转发规则-数据源 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TransportSourceMapper extends BaseMapperX<TransportSourceDO> {

    default PageResult<TransportSourceDO> selectPage(TransportSourcePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<TransportSourceDO>()
                .eqIfPresent(TransportSourceDO::getRuleId, reqVO.getRuleId())
                .eqIfPresent(TransportSourceDO::getDataType, reqVO.getDataType())
                .eqIfPresent(TransportSourceDO::getResourceSpaceId, reqVO.getResourceSpaceId())
                .eqIfPresent(TransportSourceDO::getProductCode, reqVO.getProductCode())
                .eqIfPresent(TransportSourceDO::getDeviceCode, reqVO.getDeviceCode())
                .likeIfPresent(TransportSourceDO::getResourceSpaceName, reqVO.getResourceSpaceName())
                .likeIfPresent(TransportSourceDO::getProductName, reqVO.getProductName())
                .likeIfPresent(TransportSourceDO::getDeviceName, reqVO.getDeviceName())
                .betweenIfPresent(TransportSourceDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(TransportSourceDO::getId));
    }

}