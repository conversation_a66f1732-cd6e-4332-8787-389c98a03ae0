package cn.powerchina.bjy.link.iot.dal.mysql.roledatapermissions;

import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.link.iot.dal.dataobject.datapermissions.DataPermissionsDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.roledatapermissions.RoleDataPermissionsDO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 角色与权限的关系 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface RoleDataPermissionsMapper extends BaseMapperX<RoleDataPermissionsDO> {

    @Select("SELECT x.* FROM iot_role_data_permissions x where x.role_id=#{roleId}")
    RoleDataPermissionsDO selectAllByRoleId(@Param("roleId") Long roleId);
    @Delete("DELETE from iot_role_data_permissions WHERE role_id=#{roleId}")
    int deleteByRoleId(@Param("roleId") Long roleId);
}
