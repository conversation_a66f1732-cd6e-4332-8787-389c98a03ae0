package cn.powerchina.bjy.link.iot.service.devicelog;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.link.iot.controller.admin.devicelog.vo.DeviceLogPageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.devicelog.vo.DeviceLogSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.devicelog.DeviceLogDO;
import jakarta.validation.Valid;

/**
 * 设备日志 Service 接口
 *
 * <AUTHOR>
 */
public interface DeviceLogService {

    /**
     * 创建设备日志
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createDeviceLog(@Valid DeviceLogSaveReqVO createReqVO);

    /**
     * 更新设备日志
     *
     * @param updateReqVO 更新信息
     */
    void updateDeviceLog(@Valid DeviceLogSaveReqVO updateReqVO);

    /**
     * 删除设备日志
     *
     * @param id 编号
     */
    void deleteDeviceLog(Long id);

    /**
     * 获得设备日志
     *
     * @param id 编号
     * @return 设备日志
     */
    DeviceLogDO getDeviceLog(Long id);

    /**
     * 获得设备日志分页
     *
     * @param pageReqVO 分页查询
     * @return 设备日志分页
     */
    PageResult<DeviceLogDO> getDeviceLogPage(DeviceLogPageReqVO pageReqVO);

}