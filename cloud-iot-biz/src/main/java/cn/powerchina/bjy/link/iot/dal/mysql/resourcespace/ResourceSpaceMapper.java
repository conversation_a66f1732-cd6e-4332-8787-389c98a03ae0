package cn.powerchina.bjy.link.iot.dal.mysql.resourcespace;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.iot.controller.admin.resourcespace.vo.ResourceSpacePageReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.product.ProductDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.resourcespace.ResourceSpaceDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;
import org.springframework.util.CollectionUtils;

/**
 * 资源空间 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ResourceSpaceMapper extends BaseMapperX<ResourceSpaceDO> {

    default PageResult<ResourceSpaceDO> selectPage(ResourceSpacePageReqVO reqVO) {
        PageResult<ResourceSpaceDO> result=new PageResult<>();

        if(CollectionUtils.isEmpty(reqVO.getIds()))
        {
            result=selectPage(reqVO, new LambdaQueryWrapperX<ResourceSpaceDO>()
                    .likeIfPresent(ResourceSpaceDO::getSpaceName, reqVO.getSpaceName())
                    .eqIfPresent(ResourceSpaceDO::getSpaceAppid, reqVO.getSpaceAppid())
                    .eqIfPresent(ResourceSpaceDO::getState, reqVO.getState())
                    .betweenIfPresent(ResourceSpaceDO::getCreateTime, reqVO.getCreateTime())
                    .orderByDesc(ResourceSpaceDO::getId));
        }else {
            result=selectPage(reqVO, new LambdaQueryWrapperX<ResourceSpaceDO>()
                    .likeIfPresent(ResourceSpaceDO::getSpaceName, reqVO.getSpaceName())
                    .in(ResourceSpaceDO::getId,reqVO.getIds())
                    .eqIfPresent(ResourceSpaceDO::getSpaceAppid, reqVO.getSpaceAppid())
                    .eqIfPresent(ResourceSpaceDO::getState, reqVO.getState())
                    .betweenIfPresent(ResourceSpaceDO::getCreateTime, reqVO.getCreateTime())
                    .orderByDesc(ResourceSpaceDO::getId));
        }
        return result;
    }

    /**
     * 切换数据转发启用状态
     *
     * @param id    主键id
     * @param state 切换后的状态值
     * @return
     */
    @Update("update iot_resource_space set state = #{state} where id = #{id} and state!=#{state}")
    Integer updateResourceSpaceState(@Param("id") Long id, @Param("state") Integer state);
}