package cn.powerchina.bjy.link.iot.listener.device;

import cn.powerchina.bjy.cloud.framework.common.util.json.JsonUtils;
import cn.powerchina.bjy.link.iot.dal.dataobject.device.DeviceDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.instructiondownlog.InstructionDownLogDO;
import cn.powerchina.bjy.link.iot.dto.message.DeviceCmdModel;
import cn.powerchina.bjy.link.iot.dto.up.EdgeReadPropertyValue;
import cn.powerchina.bjy.link.iot.enums.CommandServeEnum;
import cn.powerchina.bjy.link.iot.enums.IotTopicConstant;
import cn.powerchina.bjy.link.iot.model.DeviceClockSyncTransportModel;
import cn.powerchina.bjy.link.iot.model.EdgeResultModel;
import cn.powerchina.bjy.link.iot.mq.RocketMQv5Client;
import cn.powerchina.bjy.link.iot.service.device.DeviceService;
import cn.powerchina.bjy.link.iot.service.devicepropertylog.DevicePropertyLogService;
import cn.powerchina.bjy.link.iot.service.instructiondownlog.InstructionDownLogService;
import com.alibaba.fastjson.JSON;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Description: 设备命令响应
 * @Author: handl
 * @CreateDate: 2025/07/23
 */
@Slf4j
@Component
public class DeviceCommandsHandler {
    @Resource
    private InstructionDownLogService instructionDownLogService;

    @Resource
    private DevicePropertyLogService propertyLogService;

    @Resource
    private DeviceService deviceService;

    @Resource
    private RocketMQv5Client rocketMQv5Client;

    public void handler(DeviceCmdModel deviceCmdModel) {
        try {
            InstructionDownLogDO instructionDownLogDO = instructionDownLogService.getByMessageId(deviceCmdModel.getRequestId());
            String thingIdentity = instructionDownLogDO.getThingIdentity();

            if (thingIdentity.equals(CommandServeEnum.CHANNEL_VALUE.getThingIdentity())) {
                EdgeResultModel resultModel = JsonUtils.parseObject(JSON.toJSONString(deviceCmdModel.getParams()), EdgeResultModel.class);
                List<EdgeReadPropertyValue.EdgeDevicePropertyValueDTO> propertyValueList = JsonUtils.parseArray(resultModel.getData(), EdgeReadPropertyValue.EdgeDevicePropertyValueDTO.class);
                propertyLogService.savePropertyLog(propertyValueList, null);
            }else if (thingIdentity.equals(CommandServeEnum.SET_DATETIME.getThingIdentity())) {

                DeviceClockSyncTransportModel.ClockSyncData clockSyncData = JsonUtils.parseObject(JSON.toJSONString(deviceCmdModel.getParams()), DeviceClockSyncTransportModel.ClockSyncData.class);
                if (clockSyncData.getCode() == 0) {
                    DeviceDO deviceDO = deviceService.getDeviceByCode(deviceCmdModel.getDeviceCode());
                    if(clockSyncData.getData().startsWith(":"+deviceDO.getSlaveId()+"03")){
                        String tempStr = clockSyncData.getData().substring((":"+deviceDO.getSlaveId()+"03").length());;
                        clockSyncData.setData(tempStr);
                    }

                    DeviceClockSyncTransportModel transportModel = new DeviceClockSyncTransportModel();
                    transportModel.setDeviceCode(deviceCmdModel.getDeviceCode());
                    transportModel.setCurrentTime(System.currentTimeMillis());
                    transportModel.setClockSyncData(clockSyncData);
                    rocketMQv5Client.syncSendFifoMessage(IotTopicConstant.TOPIC_DEVICE_CLOCK_SYNC_DATA,  transportModel,  IotTopicConstant.GROUP_DEVICE_CLOCK_SYNC_DATA);
                    log.info("时钟同步成功,deviceCode={},data={}", transportModel.getDeviceCode(), clockSyncData.getData());
                }

            }

            instructionDownLogService.updateInstructionLogByMsgId(deviceCmdModel.getRequestId(),
                    JsonUtils.toJsonString(deviceCmdModel.getParams()), null, null);
            log.info("设备命令响应发送成功！");
        } catch (Exception e) {
            log.error("根据上报的事件，匹配规则异常：{}", e.getMessage());
        }
        //transportSourceService.dataForwarding(TransportSourceTypeEnum.DEVICE_STATUS_CHANGE, deviceEventModel);

    }


}
