package cn.powerchina.bjy.link.iot.controller.admin.datapermissions.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 数据权限新增/修改 Request VO")
@Data
public class DataProductSaveRespVO {


    @Schema(description = "产品ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "27371")
    @NotNull(message = "产品ID不能为空")
    private String productId;

    @Schema(description = "设备ID集合", requiredMode = Schema.RequiredMode.REQUIRED, example = "27371")
    @NotNull(message = "设备ID集合不能为空")
    private List<String> deviceIds;


}
