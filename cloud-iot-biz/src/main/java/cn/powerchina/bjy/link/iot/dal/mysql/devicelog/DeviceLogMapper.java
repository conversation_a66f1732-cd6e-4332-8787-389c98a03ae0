package cn.powerchina.bjy.link.iot.dal.mysql.devicelog;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.iot.controller.admin.devicelog.vo.DeviceLogPageReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.devicelog.DeviceLogDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 设备日志 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface DeviceLogMapper extends BaseMapperX<DeviceLogDO> {

    default PageResult<DeviceLogDO> selectPage(DeviceLogPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<DeviceLogDO>()
                .eqIfPresent(DeviceLogDO::getDeviceCode, reqVO.getDeviceCode())
                .eqIfPresent(DeviceLogDO::getFileSize, reqVO.getFileSize())
                .eqIfPresent(DeviceLogDO::getPath, reqVO.getPath())
                .eqIfPresent(DeviceLogDO::getRemark, reqVO.getRemark())
                .eqIfPresent(DeviceLogDO::getLogLevel, reqVO.getLogLevel())
                .betweenIfPresent(DeviceLogDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(DeviceLogDO::getId));
    }

}