package cn.powerchina.bjy.link.iot.controller.admin.datapermissions.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.models.security.SecurityScheme;
import lombok.*;

import java.util.*;

import jakarta.validation.constraints.*;

@Schema(description = "管理后台 - 数据权限新增/修改 Request VO")
@Data
public class DataPermissionsSaveReqVO {

    @Schema(description = "角色ID")
    private String roleId;

    @Schema(description = "角色名称")
    private String name;

    @Schema(description = "角色权限字符串")
    private String code;

    @Schema(description = "数据范围（1：全部数据权限 2：指定数据权限）")
    private Integer dataScope;

    @Schema(description = "资源空间集合", requiredMode = Schema.RequiredMode.REQUIRED, example = "27371")
    @NotNull(message = "资源空间集合不能为空")
    private List<DataPermissionsTreeVO> dataScopeDeptIds;


}
