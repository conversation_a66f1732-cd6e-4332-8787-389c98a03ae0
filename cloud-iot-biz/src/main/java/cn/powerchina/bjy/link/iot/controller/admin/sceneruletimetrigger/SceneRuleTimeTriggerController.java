package cn.powerchina.bjy.link.iot.controller.admin.sceneruletimetrigger;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.excel.core.util.ExcelUtils;
import cn.powerchina.bjy.link.iot.controller.admin.sceneruletimetrigger.vo.SceneRuleTimeTriggerPageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.sceneruletimetrigger.vo.SceneRuleTimeTriggerRespVO;
import cn.powerchina.bjy.link.iot.controller.admin.sceneruletimetrigger.vo.SceneRuleTimeTriggerSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.sceneruletimetrigger.SceneRuleTimeTriggerDO;
import cn.powerchina.bjy.link.iot.service.sceneruletimetrigger.SceneRuleTimeTriggerService;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.io.IOException;
import java.util.List;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 定时触发")
@RestController
@RequestMapping("/iot/scene-rule-time-trigger")
@Validated
public class SceneRuleTimeTriggerController {

    @Resource
    private SceneRuleTimeTriggerService sceneRuleTimeTriggerService;

    @PostMapping("/create")
    @Operation(summary = "创建定时触发")
//    @PreAuthorize("@ss.hasPermission('iot:scene-rule-time-trigger:create')")
    public CommonResult<Long> createSceneRuleTimeTrigger(@Valid @RequestBody SceneRuleTimeTriggerSaveReqVO createReqVO) {
        return success(sceneRuleTimeTriggerService.createSceneRuleTimeTrigger(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新定时触发")
//    @PreAuthorize("@ss.hasPermission('iot:scene-rule-time-trigger:update')")
    public CommonResult<Boolean> updateSceneRuleTimeTrigger(@Valid @RequestBody SceneRuleTimeTriggerSaveReqVO updateReqVO) {
        sceneRuleTimeTriggerService.updateSceneRuleTimeTrigger(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除定时触发")
    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('iot:scene-rule-time-trigger:delete')")
    public CommonResult<Boolean> deleteSceneRuleTimeTrigger(@RequestParam("id") Long id) {
        sceneRuleTimeTriggerService.deleteSceneRuleTimeTrigger(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得定时触发")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('iot:scene-rule-time-trigger:query')")
    public CommonResult<SceneRuleTimeTriggerRespVO> getSceneRuleTimeTrigger(@RequestParam("id") Long id) {
        SceneRuleTimeTriggerDO sceneRuleTimeTrigger = sceneRuleTimeTriggerService.getSceneRuleTimeTrigger(id);
        return success(BeanUtils.toBean(sceneRuleTimeTrigger, SceneRuleTimeTriggerRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得定时触发分页")
//    @PreAuthorize("@ss.hasPermission('iot:scene-rule-time-trigger:query')")
    public CommonResult<PageResult<SceneRuleTimeTriggerRespVO>> getSceneRuleTimeTriggerPage(@Valid SceneRuleTimeTriggerPageReqVO pageReqVO) {
        PageResult<SceneRuleTimeTriggerDO> pageResult = sceneRuleTimeTriggerService.getSceneRuleTimeTriggerPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, SceneRuleTimeTriggerRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出定时触发 Excel")
//    @PreAuthorize("@ss.hasPermission('iot:scene-rule-time-trigger:export')")
//    @OperateLog(type = EXPORT)
    public void exportSceneRuleTimeTriggerExcel(@Valid SceneRuleTimeTriggerPageReqVO pageReqVO,
                                                HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<SceneRuleTimeTriggerDO> list = sceneRuleTimeTriggerService.getSceneRuleTimeTriggerPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "定时触发.xls", "数据", SceneRuleTimeTriggerRespVO.class,
                BeanUtils.toBean(list, SceneRuleTimeTriggerRespVO.class));
    }

}
