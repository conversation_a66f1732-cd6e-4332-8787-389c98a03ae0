package cn.powerchina.bjy.link.iot.dal.mysql.shadow;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.iot.controller.admin.devicecurrentattribute.vo.DeviceShadowPageReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.devicecurrentattribute.DeviceShadowDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 设备上报的最新属性 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface DeviceShadowMapper extends BaseMapperX<DeviceShadowDO> {

    default PageResult<DeviceShadowDO> selectPage(DeviceShadowPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<DeviceShadowDO>()
                .eqIfPresent(DeviceShadowDO::getProductCode, reqVO.getProductCode())
                .eqIfPresent(DeviceShadowDO::getDeviceCode, reqVO.getDeviceCode())
                .eqIfPresent(DeviceShadowDO::getModelCode, reqVO.getModelCode())
                .eqIfPresent(DeviceShadowDO::getThingIdentity, reqVO.getThingIdentity())
                .eqIfPresent(DeviceShadowDO::getThingValue, reqVO.getThingValue())
                .betweenIfPresent(DeviceShadowDO::getReportTime, reqVO.getReportTime())
                .betweenIfPresent(DeviceShadowDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(DeviceShadowDO::getShadowType, reqVO.getShadowType())
                .orderByDesc(DeviceShadowDO::getId));
    }

}