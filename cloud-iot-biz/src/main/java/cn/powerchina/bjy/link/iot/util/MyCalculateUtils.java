package cn.powerchina.bjy.link.iot.util;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.NumberFormat;
import java.util.Objects;

/**
 * @Description: 描述
 * @Author: yhx
 * @CreateDate: 2024/8/19
 */
public class MyCalculateUtils {

    /**
     * 计算百分比
     *
     * @param count
     * @param total
     * @param section
     * @return
     */
    public static String calculate(Long count, Long total, int section) {
        if (Objects.isNull(count) || Objects.isNull(total) || count <= 0 || total <= 0) {
            return "0%";
        }
        NumberFormat numberFormat = NumberFormat.getInstance();
        numberFormat.setMaximumFractionDigits(2);
        return numberFormat.format((float) count / (float) total * 100) + "%";
    }

    /**
     * 设置值
     *
     * @param value1
     * @param scale
     * @return
     */
    public static BigDecimal calculateValue(String value1, int scale) {
        if (Objects.isNull(value1)) {
            return null;
        }
        return new BigDecimal(value1).setScale(scale, RoundingMode.HALF_UP);
    }
}
