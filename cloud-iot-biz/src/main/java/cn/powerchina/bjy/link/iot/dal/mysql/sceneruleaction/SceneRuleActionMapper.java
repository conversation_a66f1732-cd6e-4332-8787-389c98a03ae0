package cn.powerchina.bjy.link.iot.dal.mysql.sceneruleaction;


import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.iot.controller.admin.sceneruleaction.vo.SceneRuleActionPageReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.sceneruleaction.SceneRuleActionDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 场景规则执行动作 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface SceneRuleActionMapper extends BaseMapperX<SceneRuleActionDO> {

    default PageResult<SceneRuleActionDO> selectPage(SceneRuleActionPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<SceneRuleActionDO>()
                .eqIfPresent(SceneRuleActionDO::getRuleId, reqVO.getRuleId())
                .eqIfPresent(SceneRuleActionDO::getActionType, reqVO.getActionType())
                .eqIfPresent(SceneRuleActionDO::getDelaySeconds, reqVO.getDelaySeconds())
                .eqIfPresent(SceneRuleActionDO::getProductCode, reqVO.getProductCode())
                .eqIfPresent(SceneRuleActionDO::getDeviceCode, reqVO.getDeviceCode())
                .eqIfPresent(SceneRuleActionDO::getSceneId, reqVO.getSceneId())
                .eqIfPresent(SceneRuleActionDO::getSceneStatus, reqVO.getSceneStatus())
                .eqIfPresent(SceneRuleActionDO::getSort, reqVO.getSort())
                .betweenIfPresent(SceneRuleActionDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(SceneRuleActionDO::getId));
    }

}
