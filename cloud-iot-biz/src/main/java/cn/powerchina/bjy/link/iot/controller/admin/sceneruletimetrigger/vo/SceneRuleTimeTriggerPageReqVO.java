package cn.powerchina.bjy.link.iot.controller.admin.sceneruletimetrigger.vo;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import lombok.*;

import java.time.LocalDate;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 定时触发分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SceneRuleTimeTriggerPageReqVO extends PageParam {

    @Schema(description = "规则ID", example = "1259")
    private Long ruleId;

    @Schema(description = "执行时刻")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private String[] executionTime;

    @Schema(description = "重复类型:1-每天,2-指定日期,3-指定周期,4-自定义", example = "1")
    private Integer repeatType;

    @Schema(description = "开始日期/指定日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDate[] repeatStartDate;

    @Schema(description = "结束日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDate[] repeatEndDate;

    @Schema(description = "每周重复的星期几,如:1,2,3,4,5,6,7")
    private String repeatWeekDays;

    @Schema(description = "cron表达式")
    private String cronExpression;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
