package cn.powerchina.bjy.link.iot.dal.mysql.devicegroup;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.iot.controller.admin.devicegroup.vo.DeviceGroupPageReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.devicegroup.DeviceGroupDO;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.util.CollectionUtils;

/**
 * 设备分组 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface DeviceGroupMapper extends BaseMapperX<DeviceGroupDO> {

    default PageResult<DeviceGroupDO> selectPage(DeviceGroupPageReqVO reqVO) {

        PageResult<DeviceGroupDO> result=null;
        if(CollectionUtils.isEmpty(reqVO.getCodes()))
        {
            result=selectPage(reqVO, new LambdaQueryWrapperX<DeviceGroupDO>()
                    .eqIfPresent(DeviceGroupDO::getResourceSpaceId, reqVO.getResourceSpaceId())
                    .eqIfPresent(DeviceGroupDO::getParentId, reqVO.getParentId())
                    .likeIfPresent(DeviceGroupDO::getGroupName, reqVO.getGroupName())
                    .eqIfPresent(DeviceGroupDO::getGroupRemark, reqVO.getGroupRemark())
                    .betweenIfPresent(DeviceGroupDO::getCreateTime, reqVO.getCreateTime())
                    .orderByDesc(DeviceGroupDO::getId));
        }else {
            result=selectPage(reqVO, new LambdaQueryWrapperX<DeviceGroupDO>()
                    .eqIfPresent(DeviceGroupDO::getResourceSpaceId, reqVO.getResourceSpaceId())
                    .eqIfPresent(DeviceGroupDO::getParentId, reqVO.getParentId())
                    .in(DeviceGroupDO::getResourceSpaceId,reqVO.getCodes())
                    .likeIfPresent(DeviceGroupDO::getGroupName, reqVO.getGroupName())
                    .eqIfPresent(DeviceGroupDO::getGroupRemark, reqVO.getGroupRemark())
                    .betweenIfPresent(DeviceGroupDO::getCreateTime, reqVO.getCreateTime())
                    .orderByDesc(DeviceGroupDO::getId));
        }
        return result;
    }

}