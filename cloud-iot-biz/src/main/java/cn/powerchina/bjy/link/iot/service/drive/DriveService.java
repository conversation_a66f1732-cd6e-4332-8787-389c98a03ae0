package cn.powerchina.bjy.link.iot.service.drive;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.link.iot.controller.admin.drive.vo.DrivePageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.drive.vo.DriveSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.drive.DriveDO;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 驱动 Service 接口
 *
 * <AUTHOR>
 */
public interface DriveService {

    /**
     * 创建驱动
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createDrive(@Valid DriveSaveReqVO createReqVO);

    /**
     * 更新驱动
     *
     * @param updateReqVO 更新信息
     */
    void updateDrive(@Valid DriveSaveReqVO updateReqVO);

    /**
     * 删除驱动
     *
     * @param id 编号
     */
    void deleteDrive(Long id);

    /**
     * 获得驱动
     *
     * @param id 编号
     * @return 驱动
     */
    DriveDO getDrive(Long id);

    /**
     * 获得驱动分页
     *
     * @param pageReqVO 分页查询
     * @return 驱动分页
     */
    PageResult<DriveDO> getDrivePage(DrivePageReqVO pageReqVO);

    /**
     * 部署驱动
     * @param driverCode 驱动编码
     * @param edgeCode 边缘网关编码
     */
    void deployDrive(String driverCode, String edgeCode);
}