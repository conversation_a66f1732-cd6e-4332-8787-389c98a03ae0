package cn.powerchina.bjy.link.iot.controller.admin.drive;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.link.iot.controller.admin.drive.vo.DriveDeployReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.drive.vo.DrivePageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.drive.vo.DriveRespVO;
import cn.powerchina.bjy.link.iot.controller.admin.drive.vo.DriveSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.drive.DriveDO;
import cn.powerchina.bjy.link.iot.service.drive.DriveService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 驱动")
@RestController
@RequestMapping("/iot/drive")
@Validated
public class DriveController {

    @Resource
    private DriveService driveService;

    @PostMapping("/create")
    @Operation(summary = "创建驱动")
    @PreAuthorize("@ss.hasPermission('iot:drive:create')")
    public CommonResult<Long> createDrive(@Valid @RequestBody DriveSaveReqVO createReqVO) {
        return success(driveService.createDrive(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新驱动")
    @PreAuthorize("@ss.hasPermission('iot:drive:update')")
    public CommonResult<Boolean> updateDrive(@Valid @RequestBody DriveSaveReqVO updateReqVO) {
        driveService.updateDrive(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除驱动")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('iot:drive:delete')")
    public CommonResult<Boolean> deleteDrive(@RequestParam("id") Long id) {
        driveService.deleteDrive(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得驱动")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('iot:drive:query')")
    public CommonResult<DriveRespVO> getDrive(@RequestParam("id") Long id) {
        DriveDO drive = driveService.getDrive(id);
        return success(BeanUtils.toBean(drive, DriveRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得驱动分页")
    //@PreAuthorize("@ss.hasPermission('iot:drive:query')")
    public CommonResult<PageResult<DriveRespVO>> getDrivePage(@Valid DrivePageReqVO pageReqVO) {
        PageResult<DriveDO> pageResult = driveService.getDrivePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, DriveRespVO.class));
    }

    @PostMapping("/deployDrive")
    @Operation(summary = "驱动部署")
    //@PreAuthorize("@ss.hasPermission('iot:drive:deploy')")
    public CommonResult<Boolean> deployDrive(@Valid @RequestBody DriveDeployReqVO driveDeployReqVO) {
        driveService.deployDrive(driveDeployReqVO.getDriverCode(), driveDeployReqVO.getEdgeCode());
        return success(true);
    }

}