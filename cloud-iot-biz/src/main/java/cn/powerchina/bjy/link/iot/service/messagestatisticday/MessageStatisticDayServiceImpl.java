package cn.powerchina.bjy.link.iot.service.messagestatisticday;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.iot.controller.admin.index.bo.StatisticDayBO;
import cn.powerchina.bjy.link.iot.controller.admin.messagestatisticday.vo.MessageStatisticDayPageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.messagestatisticday.vo.MessageStatisticDaySaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.messagestatisticday.MessageStatisticDayDO;
import cn.powerchina.bjy.link.iot.dal.mysql.messagestatisticday.MessageStatisticDayMapper;
import cn.powerchina.bjy.link.iot.enums.IotRedisConstant;
import cn.powerchina.bjy.link.iot.util.MyDateUtils;
import jakarta.annotation.Resource;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.link.iot.enums.ErrorCodeConstants.MESSAGE_STATISTIC_DAY_NOT_EXISTS;

/**
 * 设备消息数按日统计 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class MessageStatisticDayServiceImpl implements MessageStatisticDayService {

    @Resource
    private MessageStatisticDayMapper messageStatisticDayMapper;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Override
    public Long createMessageStatisticDay(MessageStatisticDaySaveReqVO createReqVO) {
        // 插入
        MessageStatisticDayDO messageStatisticDay = BeanUtils.toBean(createReqVO, MessageStatisticDayDO.class);
        messageStatisticDayMapper.insert(messageStatisticDay);
        // 返回
        return messageStatisticDay.getId();
    }

    @Override
    public Long insertMessageStatisticDay(Date statisticDate, Integer statisticType, Long statisticCount) {
        if (Objects.isNull(statisticDate)) {
            return 0L;
        }
        Date statisticDateToDb = MyDateUtils.getParseDate(MyDateUtils.getFormatDate(statisticDate, "yyyy-MM-dd"), "yyyy-MM-dd");
        // 插入
        MessageStatisticDayDO messageStatisticDay = findMessageStatisticDayByDayAndType(statisticDateToDb, statisticType);
        int result;
        if (Objects.isNull(messageStatisticDay)) {
            messageStatisticDay = new MessageStatisticDayDO();
            messageStatisticDay.setStatisticDay(statisticDateToDb);
            messageStatisticDay.setStatisticCount(statisticCount);
            messageStatisticDay.setStatisticType(statisticType);
            result = messageStatisticDayMapper.insert(messageStatisticDay);
        } else {
            result = messageStatisticDayMapper.updateStatisticCount(statisticDateToDb, statisticType, statisticCount);
        }
        if (result > 0) {
            String key = String.format(IotRedisConstant.STATISTIC_MESSAGE_DAY_KEY, statisticType, MyDateUtils.getFormatDate(statisticDateToDb, "yyyyMMdd"));
            redisTemplate.opsForHash().increment(key, MyDateUtils.getFormatDate(statisticDate, "HH"), statisticCount);
            redisTemplate.expire(key, 3, TimeUnit.DAYS);
        }
        // 返回
        return messageStatisticDay.getId();
    }

    @Override
    public MessageStatisticDayDO findMessageStatisticDayByDayAndType(Date statisticDay, Integer statisticType) {
        return messageStatisticDayMapper.selectOne(new LambdaQueryWrapperX<MessageStatisticDayDO>()
                .eq(MessageStatisticDayDO::getStatisticDay, statisticDay)
                .eq(MessageStatisticDayDO::getStatisticType, statisticType));
    }

    @Override
    public void updateMessageStatisticDay(MessageStatisticDaySaveReqVO updateReqVO) {
        // 校验存在
        validateMessageStatisticDayExists(updateReqVO.getId());
        // 更新
        MessageStatisticDayDO updateObj = BeanUtils.toBean(updateReqVO, MessageStatisticDayDO.class);
        messageStatisticDayMapper.updateById(updateObj);
    }

    @Override
    public void deleteMessageStatisticDay(Long id) {
        // 校验存在
        validateMessageStatisticDayExists(id);
        // 删除
        messageStatisticDayMapper.deleteById(id);
    }

    private void validateMessageStatisticDayExists(Long id) {
        if (messageStatisticDayMapper.selectById(id) == null) {
            throw exception(MESSAGE_STATISTIC_DAY_NOT_EXISTS);
        }
    }

    @Override
    public MessageStatisticDayDO getMessageStatisticDay(Long id) {
        return messageStatisticDayMapper.selectById(id);
    }

    @Override
    public PageResult<MessageStatisticDayDO> getMessageStatisticDayPage(MessageStatisticDayPageReqVO pageReqVO) {
        return messageStatisticDayMapper.selectPage(pageReqVO);
    }

    @Override
    public Long countMessage(Date statisticDay, Integer statisticType) {
        Long count = messageStatisticDayMapper.selectCountMessage(statisticDay, statisticType);
        return Objects.isNull(count) ? 0 : count;
    }

    @Override
    public List<StatisticDayBO> selectListByStatisticDay(Date startTime, Date endTime, Integer statisticType) {
        List<MessageStatisticDayDO> dayDOList = messageStatisticDayMapper.selectList(new LambdaQueryWrapperX<MessageStatisticDayDO>().ge(MessageStatisticDayDO::getStatisticDay, startTime)
                .le(MessageStatisticDayDO::getStatisticDay, endTime).eq(MessageStatisticDayDO::getStatisticType, statisticType).orderByAsc(MessageStatisticDayDO::getStatisticDay));
        if (!CollectionUtils.isEmpty(dayDOList)) {
            return dayDOList.stream().map(item -> StatisticDayBO.builder().statisticDay(item.getStatisticDay()).statisticCount(item.getStatisticCount()).build()).collect(Collectors.toList());
        }
        return null;
    }
}
