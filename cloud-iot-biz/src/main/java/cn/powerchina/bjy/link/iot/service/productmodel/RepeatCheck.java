package cn.powerchina.bjy.link.iot.service.productmodel;

import cn.powerchina.bjy.cloud.framework.common.exception.ErrorCode;
import cn.powerchina.bjy.cloud.framework.common.util.collection.CollectionUtils;
import cn.powerchina.bjy.link.iot.controller.admin.productmodel.vo.ProductModelSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.productmodel.ProductModelDO;
import cn.powerchina.bjy.link.iot.dal.mysql.productmodel.ProductModelMapper;
import cn.powerchina.bjy.link.iot.enums.ThingModeTypeEnum;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import jakarta.annotation.Resource;

import java.util.List;
import java.util.Objects;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.link.iot.enums.ErrorCodeConstants.PRODUCT_MODEL_IDENTIFY_EXISTS;
import static cn.powerchina.bjy.link.iot.enums.ErrorCodeConstants.PRODUCT_MODEL_NAME_EXISTS;

/**
 * RepeatCheck
 *
 * <AUTHOR>
 **/
public abstract class RepeatCheck {

    @Resource
    private ProductModelMapper productModelMapper;

    /**
     * 通用方法，校验属性名称是否存在
     *
     * @param name 物模型名称
     * @param id   物模型id，id非空则说明为更新操作
     */
    protected void validateNameExits(String productCode, String name, Long id, Integer thingType) {
        String desc = ThingModeTypeEnum.getDescByType(thingType);
        check(productCode, ProductModelDO::getThingName, name, id, PRODUCT_MODEL_NAME_EXISTS, desc);
    }

    /**
     * 通用方法，校验唯一标识是否存在
     *
     * @param identify 物模型唯一标识
     * @param id       物模型id，id非空则说明为更新操作
     */
    protected void validateIdentifyExits(String productCode, String identify, Long id, Integer thingType) {
        String desc = ThingModeTypeEnum.getDescByType(thingType);
        check(productCode, ProductModelDO::getThingIdentity, identify, id, PRODUCT_MODEL_IDENTIFY_EXISTS, desc);
    }

    private void check(String productCode, SFunction<ProductModelDO, ?> function, String param, Long id, ErrorCode errorCode, String... errorDesc) {
        if (null == id) {
            QueryWrapper<ProductModelDO> wrapper = new QueryWrapper<>();
            wrapper.lambda().eq(ProductModelDO::getProductCode, productCode).eq(function, param).eq(ProductModelDO::getDeleted, Boolean.FALSE);
            if (productModelMapper.exists(wrapper)) {
                errorException(errorCode, errorDesc);
            }
        } else {
            QueryWrapper<ProductModelDO> wrapper = new QueryWrapper<>();
            wrapper.lambda().eq(ProductModelDO::getProductCode, productCode).eq(function, param).eq(ProductModelDO::getDeleted, Boolean.FALSE);
            List<ProductModelDO> productModelDOList = productModelMapper.selectList(wrapper);
            // 为空，说明改了模型名称，且产品名未出现过
            if (CollectionUtils.isAnyEmpty(productModelDOList)) {
                return;
            }
            // 不为空，且size不为1，则说明有多个相同的模型名，不可更新
            if (productModelDOList.size() != 1) {
                errorException(errorCode, errorDesc);
            }
            // 只有一条记录，则校验是否为记录自身
            if (!productModelDOList.get(0).getId().equals(id)) {
                errorException(errorCode, errorDesc);
            }
        }
    }

    /**
     * 根据可变参数，组织错误信息
     *
     * @param errorCode
     * @param errorDesc
     */
    private void errorException(ErrorCode errorCode, String[] errorDesc) {
        if (Objects.isNull(errorDesc)) {
            throw exception(errorCode);
        } else {
            throw exception(errorCode, errorDesc[0]);
        }
    }

    /**
     * 差异化校验
     * 针对物模型场景：
     * 1）属性，只校验属性名和标识符，无需实现该方法
     * 2）服务，校验属性名和标识符，有差异化校验，需实现，对于输入参数每次为覆盖操作，只需内存处理，无需查库
     * 3）事件，校验属性名和标识符，有差异化校验，需实现，对于输入参数每次为覆盖操作，只需内存处理，无需查库
     *
     * @param reqVO 物模型入参
     */
    protected abstract void validateCustom(ProductModelSaveReqVO reqVO);

    /**
     * 对外暴露校验方法
     *
     * @param reqVO 物模型入参
     */
    public void repeatCheck(ProductModelSaveReqVO reqVO) {
        validateNameExits(reqVO.getProductCode(), reqVO.getThingName(), reqVO.getId(), reqVO.getThingType());
        validateIdentifyExits(reqVO.getProductCode(), reqVO.getThingIdentity(), reqVO.getId(), reqVO.getThingType());
        validateCustom(reqVO);
    }
}
