package cn.powerchina.bjy.link.iot.controller.admin.edgegateway.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(description = "管理后台 - 边缘网关新增/修改 Request VO")
@Data
public class EdgeGatewaySaveReqVO {

    @Schema(description = "主键")
    private Long id;

    @Schema(description = "所属产品编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "所属产品不能为空")
    private String productCode;

    @Schema(description = "实例名称",example = "")
    private String edgeName;

    @Schema(description = "唯一标识")
    private String serial;

    @Schema(description = "描述")
    private String description;

}