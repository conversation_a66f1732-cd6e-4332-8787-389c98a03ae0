package cn.powerchina.bjy.link.iot.controller.admin.devicegroup.vo;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 设备分组分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DeviceGroupPageReqVO extends PageParam {

    @Schema(description = "资源空间id")
    private Long resourceSpaceId;

    @Schema(description = "父节点id")
    private Long parentId;

    @Schema(description = "分组名称")
    private String groupName;

    @Schema(description = "分组描述")
    private String groupRemark;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "设备分组中资源空间id集合")
    private List<String> codes;

}