package cn.powerchina.bjy.link.iot.listener.register;

import cn.hutool.core.util.ObjectUtil;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.link.iot.dto.register.DeviceRegisterModel;
import cn.powerchina.bjy.link.iot.dto.register.GatewayRegisterModel;
import cn.powerchina.bjy.link.iot.enums.device.IotDeviceMessageMethodEnum;
import cn.powerchina.bjy.link.iot.model.IotDeviceRegister;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.annotation.RocketMQMessageListener;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.apache.rocketmq.client.core.RocketMQListener;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;

/**
 * @Description: 监听设备注册的消息
 * @Author: handl
 * @CreateDate: 2025/08/18
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = IotDeviceRegister.TOPIC_IOT_DEVICE_REGISTER, consumerGroup = IotDeviceRegister.GROUP_IOT_DEVICE_REGISTER, requestTimeout = 10, consumptionThreadCount = 10)
public class DeviceRegisterReceiver implements RocketMQListener {

    @Resource
    private ThreadPoolTaskExecutor iotThreadPoolTaskExecutor;

    @Resource
    DeviceRegisterHandler deviceRegisterHandler;

    @Resource
    GatewayRegisterHandler gatewayRegisterHandler;

    @Override
    public ConsumeResult consume(MessageView messageView) {
        log.info("接收上报消息ID: {}", messageView.getMessageId());
        if (ObjectUtil.isNull(messageView)) {
            log.error("MessageView is null, skip consumption");
        }
        try {
            // 解析消息体
            IotDeviceRegister iotDeviceRegister = parseMessageBody(messageView);
            if (iotDeviceRegister == null) {
                log.error("MessageView is null, skip consumption");
                return ConsumeResult.SUCCESS;
            }
            IotDeviceMessageMethodEnum enumByMethod = IotDeviceMessageMethodEnum.getEnumByMethod(iotDeviceRegister.getMethod());
            if (null == enumByMethod) {
                log.error("不支持的上报类型 {}", iotDeviceRegister.getMethod());
                return ConsumeResult.SUCCESS;
            }

            log.info("receive message: {}", JSONObject.toJSON(iotDeviceRegister));
            iotThreadPoolTaskExecutor.execute(() -> {
                switch (enumByMethod) {

                    case DEVICE_REGISTER:
                        //设备注册
                        DeviceRegisterModel deviceRegisterModel = BeanUtils.toBean(iotDeviceRegister, DeviceRegisterModel.class);
                        deviceRegisterHandler.handler(deviceRegisterModel);
                        break;
                    case GATEWAY_REGISTER:
                        //边缘实例设备注册
                        iotDeviceRegister.getGatewayDevices().forEach(DeviceRegister -> {
                            GatewayRegisterModel subDevice = BeanUtils.toBean(DeviceRegister, GatewayRegisterModel.class);
                            subDevice.setEdgeCode(iotDeviceRegister.getEdgeCode());
                            gatewayRegisterHandler.handler(subDevice);
                        });
                        break;
                }
            });


        } catch (Exception e) {
            log.error("处理上行消息messageView={}异常{}", messageView, e.getMessage());
            return ConsumeResult.SUCCESS;
        }
        return ConsumeResult.SUCCESS;
    }

    /**
     * 解析消息体为实体类
     */
    private IotDeviceRegister parseMessageBody(MessageView messageView) {
        try {
            ByteBuffer body = messageView.getBody();
            if (body.remaining() == 0) {
                log.error("Message body is empty, messageId: {}", messageView.getMessageId());
                return null;
            }

            String message = StandardCharsets.UTF_8.decode(body).toString();
            return JSON.parseObject(message, IotDeviceRegister.class);

        } catch (JSONException e) {
            log.error("JSON解析失败, messageId: {}, content: {}",
                    messageView.getMessageId(),
                    messageView.getBody(),
                    e
            );
            return null;
        }
    }


}
