package cn.powerchina.bjy.link.iot.controller.admin.deviceeventlog.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class EventNameVO {
    @Schema(description = "物模型标识符", example = "temp_alarm")
    private String thingIdentity;
    @Schema(description = "事件名称", example = "温度告警")
    private String thingName;
}