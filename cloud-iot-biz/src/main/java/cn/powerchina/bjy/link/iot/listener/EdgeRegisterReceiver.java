package cn.powerchina.bjy.link.iot.listener;

import cn.hutool.core.util.ObjectUtil;
import cn.powerchina.bjy.cloud.framework.common.util.json.JsonUtils;
import cn.powerchina.bjy.link.iot.dto.down.EdgeSyncUpDTO;
import cn.powerchina.bjy.link.iot.dto.up.EdgeReportEventValue;
import cn.powerchina.bjy.link.iot.enums.EdgeSyncTypeEnum;
import cn.powerchina.bjy.link.iot.enums.IotTopicConstant;
import cn.powerchina.bjy.link.iot.service.edgegateway.EdgeGatewayService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.annotation.RocketMQMessageListener;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.apache.rocketmq.client.core.RocketMQListener;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.util.Objects;

/**
 * 接收边缘网关注册
 */
@Slf4j
@Component
// rocketmq调整为mqtt，下面代码不再需要，所以注释掉
//@RocketMQMessageListener(topic = IotTopicConstant.TOPIC_DEVICE_SYNC, consumerGroup = IotTopicConstant.TOPIC_DEVICE_SYNC, requestTimeout = 10, consumptionThreadCount = 10)
public class EdgeRegisterReceiver implements RocketMQListener {

    @Resource
    private EdgeGatewayService edgeGatewayService;

    @Override
    public ConsumeResult consume(MessageView messageView) {
        /* rocketmq调整为mqtt，下面代码不再需要，所以注释掉
        if (ObjectUtil.isNull(messageView)) {
            log.error("MessageView is null, skip consumption");
            return ConsumeResult.SUCCESS;
        }
        try {
            // 解析消息体
            EdgeSyncUpDTO entityDTO = parseMessageBody(messageView);
            if (entityDTO == null) {
                log.error("MessageView is null, skip consumption");
                return ConsumeResult.SUCCESS;
            }
            // 同步逻辑
            edgeGatewayService.receiveRegister(entityDTO);
            //如果是注册，则需要探测登录状态
            if (Objects.equals(entityDTO.getSyncType(), EdgeSyncTypeEnum.REGISTER.getType())) {
                edgeGatewayService.edgeOnlineCheck(entityDTO.getNode());
            }
        } catch (Exception e) {
            log.error("edgeRegisterReceive--->error,message={}", e.getMessage(), e);
        }
         */
        return ConsumeResult.SUCCESS;
    }
    /**
     * 解析消息体为实体类
     rocketmq调整为mqtt，下面代码不再需要，所以注释掉
    private EdgeSyncUpDTO parseMessageBody(MessageView messageView) {
        try {
            ByteBuffer body = messageView.getBody();
            if (body.remaining() == 0) {
                log.error("Message body is empty, messageId: {}", messageView.getMessageId());
                return null;
            }

            String message = StandardCharsets.UTF_8.decode(body).toString();
            return JSON.parseObject(message, EdgeSyncUpDTO.class);

        } catch (JSONException e) {
            log.error("JSON解析失败, messageId: {}, content: {}",
                    messageView.getMessageId(),
                    messageView.getBody(),
                    e
            );
            return null;
        }
    }
     */
}
