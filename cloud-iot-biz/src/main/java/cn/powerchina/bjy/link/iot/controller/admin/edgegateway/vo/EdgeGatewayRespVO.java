package cn.powerchina.bjy.link.iot.controller.admin.edgegateway.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 边缘网关 Response VO")
@Data
@ExcelIgnoreUnannotated
public class EdgeGatewayRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "网关实例编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("网关实例编码")
    private String edgeCode;

    @Schema(description = "边缘网关名称")
    @ExcelProperty("边缘网关名称")
    private String edgeName;

    @Schema(description = "边缘网关应用名", example = "")
    @ExcelProperty("边缘网关应用名")
    private String edgeServiceName;

    @Schema(description = "边缘网关host", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("边缘网关host")
    private String edgeHost;

    @Schema(description = "边缘网关端口", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("边缘网关端口")
    private Integer edgePort;

    @Schema(description = "描述", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("描述")
    private String description;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "主机唯一标识", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("主机唯一标识")
    private String edgeIdentifier;

    @Schema(description = "主机状态（0:未激活; 1:离线; 2:在线）", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("主机状态（0:未激活; 1:离线; 2:在线）")
    private Integer edgeStatus;

    @Schema(description = "最近上线时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("最近上线时间")
    private LocalDateTime onlineTime;

    private String deviceName;

    @Schema(description = "资源空间id", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("资源空间id")
    private Long resourceSpaceId;

    @Schema(description = "资源空间名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("资源空间名称")
    private String spaceName;

    /**
     * 产品id
     */
    @Schema(description = "产品id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long productId;

    /**
     * 产品编码
     */
    @Schema(description = "产品编码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String productCode;

    /**
     * 产品名称
     */
    @Schema(description = "产品名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String productName;

    /**
     * 密钥
     */
    @Schema(description = "密钥", requiredMode = Schema.RequiredMode.REQUIRED)
    private String deviceSecret;

    /**
     * 节点类型编码
     */
    @Schema(description = "节点类型(0直连，1网关，2网关子设备)", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer nodeType;

    /**
     * 节点类型名称
     */
    private String nodeTypeCN;

    /**
     * 激活时间
     */
    @Schema(description = "激活时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime activeTime;

}