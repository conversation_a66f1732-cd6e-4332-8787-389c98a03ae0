package cn.powerchina.bjy.link.iot.dal.dataobject.notificationmethod;
import cn.powerchina.bjy.cloud.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;
import com.baomidou.mybatisplus.annotation.*;

/**
 * 通知方式 DO
 *
 * <AUTHOR>
 */
@TableName("iot_notification_method")
@KeySequence("iot_notification_method_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NotificationMethodDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 关联的告警模板ID
     */
    private Long alarmTemplateId;
    /**
     * 通知方式（1:钉钉 2：邮件 3：短信）
     */
    private Integer notificationMethod;
    /**
     * 通知账号/webhook地址
     */
    private String notificationAccount;
    /**
     * 通知内容
     */
    private String notificationContent;

}