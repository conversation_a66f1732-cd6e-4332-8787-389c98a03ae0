package cn.powerchina.bjy.link.iot.controller.admin.devicecurrentattribute.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 设备上报的最新属性新增/修改 Request VO")
@Data
public class DeviceShadowSaveReqVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "13009")
    private Long id;

    @Schema(description = "产品code", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "产品code不能为空")
    private String productCode;

    @Schema(description = "设备code", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "设备code不能为空")
    private String deviceCode;

    @Schema(description = "传感器模型code")
    private String modelCode;

    @Schema(description = "属性标识")
    private String thingIdentity;

    @Schema(description = "属性值")
    private String thingValue;

    @Schema(description = "类型（0-属性，1-事件，2-状态变更）")
    private Integer shadowType;

    @Schema(description = "负载数据")
    private String shadowPayload;

    @Schema(description = "上报时间")
    private LocalDateTime reportTime;

}