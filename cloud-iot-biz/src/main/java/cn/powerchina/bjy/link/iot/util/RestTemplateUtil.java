package cn.powerchina.bjy.link.iot.util;

import org.apache.hc.client5.http.classic.HttpClient;
import org.apache.hc.client5.http.impl.classic.HttpClients;
import org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManager;
import org.apache.hc.client5.http.config.RequestConfig;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;
import java.util.concurrent.TimeUnit;

public  class RestTemplateUtil {

    /**
     * 创建配置了连接池的 RestTemplate 实例
     * @return 配置好的 RestTemplate 实例
     */
    public static RestTemplate createRestTemplate() {
        // 创建连接池管理器
        PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager();

        // 配置超时设置
        RequestConfig config = RequestConfig.custom()
                .setConnectTimeout(30_000, TimeUnit.MILLISECONDS)  // 连接超时 30秒
                .setResponseTimeout(30_000, TimeUnit.MILLISECONDS) // 响应超时 30秒
                .build();

        // 配置 HttpClient
        HttpClient httpClient = HttpClients.custom()
                .setConnectionManager(connectionManager)
                .setDefaultRequestConfig(config)
                .build();

        // 创建基于 HttpClient 的请求工厂
        HttpComponentsClientHttpRequestFactory requestFactory =
                new HttpComponentsClientHttpRequestFactory(httpClient);

        return new RestTemplate(requestFactory);
    }

    /**
     * 获取默认配置的 RestTemplate 单例
     * @return RestTemplate 单例
     */
    private static final RestTemplate INSTANCE = createRestTemplate();

    public static RestTemplate getInstance() {
        return INSTANCE;
    }
}

