package cn.powerchina.bjy.link.iot.controller.admin.mqttauth;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.link.iot.controller.admin.mqttauth.vo.MqttAuthPageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.mqttauth.vo.MqttAuthRespVO;
import cn.powerchina.bjy.link.iot.controller.admin.mqttauth.vo.MqttAuthSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.mqttauth.MqttAuthDO;
import cn.powerchina.bjy.link.iot.service.mqttauth.MqttAuthService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;


@Tag(name = "管理后台 - mqtt认证")
@RestController
@RequestMapping("/iot/mqtt-auth")
@Validated
public class MqttAuthController {

    @Resource
    private MqttAuthService mqttAuthService;

    @PostMapping("/create")
    @Operation(summary = "创建mqtt认证")
    @PreAuthorize("@ss.hasPermission('iot:mqtt-auth:create')")
    public CommonResult<Long> createMqttAuth(@Valid @RequestBody MqttAuthSaveReqVO createReqVO) {
        return success(mqttAuthService.createMqttAuth(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新mqtt认证")
    @PreAuthorize("@ss.hasPermission('iot:mqtt-auth:update')")
    public CommonResult<Boolean> updateMqttAuth(@Valid @RequestBody MqttAuthSaveReqVO updateReqVO) {
        mqttAuthService.updateMqttAuth(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除mqtt认证")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('iot:mqtt-auth:delete')")
    public CommonResult<Boolean> deleteMqttAuth(@RequestParam("id") Long id) {
        mqttAuthService.deleteMqttAuth(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得mqtt认证")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('iot:mqtt-auth:query')")
    public CommonResult<MqttAuthRespVO> getMqttAuth(@RequestParam("id") Long id) {
        MqttAuthDO mqttAuth = mqttAuthService.getMqttAuth(id);
        return success(BeanUtils.toBean(mqttAuth, MqttAuthRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得mqtt认证分页")
    @PreAuthorize("@ss.hasPermission('iot:mqtt-auth:query')")
    public CommonResult<PageResult<MqttAuthRespVO>> getMqttAuthPage(@Valid MqttAuthPageReqVO pageReqVO) {
        PageResult<MqttAuthDO> pageResult = mqttAuthService.getMqttAuthPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, MqttAuthRespVO.class));
    }


}