package cn.powerchina.bjy.link.iot.dto.message;

import cn.powerchina.bjy.link.iot.model.ServiceProperty;
import lombok.Data;

import java.util.List;

/**
 * @Description: 描述
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @CreateDate: 2025/7/7
 */
@Data
public class DevicePropertiesResponseModel {

    private List<ServiceProperty> services;

    private String productCode;

    private String thingIdentity;

    private String deviceCode;
    private String id;

    private String requestId;

    private String subDeviceId;

}
