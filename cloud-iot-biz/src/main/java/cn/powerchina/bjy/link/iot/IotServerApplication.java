package cn.powerchina.bjy.link.iot;


import org.apache.rocketmq.client.autoconfigure.RocketMQAutoConfiguration;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Import;

/**
 * 项目的启动类
 *
 * <AUTHOR>
 */
@SpringBootApplication
@Import(RocketMQAutoConfiguration.class)
@EnableFeignClients(basePackages = {"cn.powerchina.bjy.cloud"})
public class IotServerApplication {

    public static void main(String[] args) {
        SpringApplication.run(IotServerApplication.class, args);
    }

}
