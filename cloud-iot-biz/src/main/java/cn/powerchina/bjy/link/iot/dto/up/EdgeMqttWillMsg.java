package cn.powerchina.bjy.link.iot.dto.up;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * Mqtt遗嘱消息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EdgeMqttWillMsg implements Serializable {

    /**
     * Mqtt客户端id
     */
    private String clientId;

    /**
     * 在线状态
     */
    private String status;

    /**
     * 当前时间
     */
    private Long currentTime;

    /**
     * 租户id
     */
    private String tenantId;
}
