package cn.powerchina.bjy.link.iot.controller.admin.mqttauth.vo;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


@Schema(description = "管理后台 - mqtt认证分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MqttAuthPageReqVO extends PageParam {

    @Schema(description = "用户名", example = "张三")
    private String userName;

    @Schema(description = "密钥")
    private String secret;

    @Schema(description = "认证类型(1:应用管理;2:产品管理;3:设备管理)", example = "2")
    private Integer type;

    @Schema(description = "状态(1:启用;0:禁用)", example = "2")
    private Integer status;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}