package cn.powerchina.bjy.link.iot.strategy.impl;

import cn.powerchina.bjy.cloud.framework.common.util.json.JsonUtils;
import cn.powerchina.bjy.link.iot.dto.up.EdgeReadPropertyValue;
import cn.powerchina.bjy.link.iot.strategy.ProductModelStrategy;
import cn.powerchina.bjy.link.iot.strategy.vo.FloatLimitVO;
import cn.powerchina.bjy.link.iot.util.MyCalculateUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * float型数据校验
 * 校验值是否小于或大于配置区间
 *
 * <AUTHOR>
 **/
@Slf4j
@Component("floatStrategy")
public class FloatStrategy implements ProductModelStrategy {
    @Override
    public boolean shouldIgnore(Object context, String strategy) {
        if (StringUtils.isBlank(strategy) || null == context) {
            return false;
        }
        try {
            FloatLimitVO limit = JsonUtils.parseObject(strategy, new TypeReference<>() {
            });
            if (null == limit) {
                return false;
            }
            float data = (float) context;
            BigDecimal decimal = new BigDecimal(String.valueOf(data));
            return data < limit.getMin() || data > limit.getMax() || decimal.scale() > limit.getPrecision();
        } catch (Exception e) {
            log.error("物模型数据类型【float】配置转换异常，配置【{}】", strategy, e);
        }
        return true;
    }

    @Override
    public boolean shouldIgnore(String context, String strategy, EdgeReadPropertyValue.EdgeDevicePropertyValueDTO valueDTO) {
        if (StringUtils.isBlank(strategy) || null == context) {
            return false;
        }
        try {
            FloatLimitVO limit = JsonUtils.parseObject(strategy, new TypeReference<>() {
            });
            if (null == limit) {
                return false;
            }
            float data = Float.parseFloat(context);
            BigDecimal decimal = new BigDecimal(context);
            boolean ignore = data < limit.getMin() || data > limit.getMax();
            if (!ignore && decimal.scale() > limit.getPrecision()) {
                valueDTO.setThingValue(MyCalculateUtils.calculateValue(valueDTO.getThingValue(), limit.getPrecision()) + "");
            }
            return ignore;
        } catch (Exception e) {
            log.error("物模型数据类型【float】配置转换异常，值【{}】配置【{}】", context, strategy, e);
        }
        return true;
    }
}
