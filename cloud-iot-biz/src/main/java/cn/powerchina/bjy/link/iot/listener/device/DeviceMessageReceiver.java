package cn.powerchina.bjy.link.iot.listener.device;

import cn.hutool.core.util.ObjectUtil;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.link.iot.dal.dataobject.device.DeviceDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.productmodel.ProductModelDO;
import cn.powerchina.bjy.link.iot.dto.message.*;
import cn.powerchina.bjy.link.iot.enums.StatisticImageTypeEnum;
import cn.powerchina.bjy.link.iot.enums.device.IotDeviceMessageMethodEnum;
import cn.powerchina.bjy.link.iot.model.IotDeviceMessage;
import cn.powerchina.bjy.link.iot.service.device.DeviceService;
import cn.powerchina.bjy.link.iot.service.messagestatisticday.MessageStatisticDayService;
import cn.powerchina.bjy.link.iot.service.productmodel.ProductModelService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.annotation.RocketMQMessageListener;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.apache.rocketmq.client.core.RocketMQListener;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.util.Date;

/**
 * @Description: 监听设备上报的消息
 * @Author: zhaoqiang
 * @CreateDate: 2024/10/16
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = IotDeviceMessage.TOPIC_IOT_DEVICE_MESSAGE, consumerGroup = IotDeviceMessage.GROUP_IOT_DEVICE_MESSAGE, requestTimeout = 10, consumptionThreadCount = 10)
public class DeviceMessageReceiver implements RocketMQListener {

    @Resource
    private DevicePropertiesResHandler devicePropertiesResHandler;

    @Resource
    private DevicePropertiesHandler devicePropertiesHandler;

    @Resource
    private DeviceStatusChangeHandler deviceStatusChangeHandler;

    @Resource
    private DeviceEventHandler deviceEventHandler;

    @Resource
    private DeviceCommandsHandler deviceCommandsHandler;

    @Resource
    private DeviceService deviceService;

    @Resource
    private MessageStatisticDayService messageStatisticDayService;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private ProductModelService productModelService;

    @Resource
    private ThreadPoolTaskExecutor iotThreadPoolTaskExecutor;

    @Override
    public ConsumeResult consume(MessageView messageView) {
        log.info("接收上报消息ID: {}", messageView.getMessageId());
        if (ObjectUtil.isNull(messageView)) {
            log.error("MessageView is null, skip consumption");
        }
        String lockKey = "data:device:message:lock";
        RLock lock = redissonClient.getLock(lockKey);
        try {
            // 解析消息体
            IotDeviceMessage iotDeviceMessage = parseMessageBody(messageView);
            if (iotDeviceMessage == null) {
                log.error("MessageView is null, skip consumption");
                return ConsumeResult.SUCCESS;
            }
            //设备是否存在
            DeviceDO device = deviceService.getDevice(iotDeviceMessage.getDeviceCode());
            if (device == null) {
                log.error("设备 {} 不存在，丢弃消息 {}", iotDeviceMessage.getDeviceCode(), iotDeviceMessage.getId());
                return ConsumeResult.SUCCESS;
            }

            log.info("receive message: {}", JSONObject.toJSON(iotDeviceMessage));
            IotDeviceMessageMethodEnum enumByMethod = IotDeviceMessageMethodEnum.getEnumByMethod(iotDeviceMessage.getMethod());
            if (null == enumByMethod) {
                log.error("不支持的上报类型 {}", iotDeviceMessage.getMethod());
                return ConsumeResult.SUCCESS;
            }

            if(IotDeviceMessageMethodEnum.EVENT_REPORT.equals(enumByMethod)){
                DeviceEventModel deviceEventModel = BeanUtils.toBean(iotDeviceMessage, DeviceEventModel.class);
                ProductModelDO productModelDO = productModelService.getProductModel(device.getProductCode(), deviceEventModel.getThingIdentity());
                if(ObjectUtil.isNull(productModelDO)){
                    log.info("产品 {} 物模型 {} 不存在，丢弃消息 {}", device.getProductCode(), deviceEventModel.getThingIdentity(), iotDeviceMessage.getId());
                    return ConsumeResult.SUCCESS;
                }
            }
            lock.lock();
            messageStatisticDayService.insertMessageStatisticDay(new Date(), StatisticImageTypeEnum.MESSAGE.getType(), 1L);
            iotThreadPoolTaskExecutor.execute(() -> {

                switch (enumByMethod) {
                    case PROPERTIES_REPORT:
                        //设备属性上报
                        DevicePropertiesReportModel devicePropertiesReportModel = BeanUtils.toBean(iotDeviceMessage, DevicePropertiesReportModel.class);
                        devicePropertiesReportModel.setProductCode(device.getProductCode());
                        log.info("处理设备属性上报消息: {}", devicePropertiesReportModel);
                        devicePropertiesHandler.handler(devicePropertiesReportModel, device);
                        break;
                    case PROPERTIES_GET_RESPONSE:
                        // 查询设备属性
                        DevicePropertiesResponseModel devicePropertiesResponseModel = BeanUtils.toBean(iotDeviceMessage, DevicePropertiesResponseModel.class);
                        log.info("处理查询设备属性消息: {}", devicePropertiesResponseModel);
                        devicePropertiesResHandler.handler(devicePropertiesResponseModel, device);
                        break;
                    case STATE_UPDATE:
                        //设备状态更新
                        DeviceStatusModel deviceStatusModel = BeanUtils.toBean(iotDeviceMessage, DeviceStatusModel.class);
                        deviceStatusModel.setProductCode(device.getProductCode());
                        log.info("处理设备状态更新消息: {}", deviceStatusModel);
                        deviceStatusChangeHandler.handler(deviceStatusModel, device);
                        break;
                    case EVENT_REPORT:
                        //设备事件上报
                        DeviceEventModel deviceEventModel = BeanUtils.toBean(iotDeviceMessage, DeviceEventModel.class);
                        deviceEventModel.setProductCode(device.getProductCode());
                        log.info("处理设备事件更新消息: {}", deviceEventModel);
                        deviceEventHandler.handler(deviceEventModel);
                        break;
                    case COMMANDS_RESPONSE:
                        //设备命令响应
                        DeviceCmdModel deviceCmdModel = BeanUtils.toBean(iotDeviceMessage, DeviceCmdModel.class);
                        deviceCmdModel.setDeviceCode(iotDeviceMessage.getDeviceCode());
                        deviceCommandsHandler.handler(deviceCmdModel);
                        break;


                }
            });
        } catch (Exception e) {
            log.error("处理上行消息messageView={}异常{}", messageView, e.getMessage());
            return ConsumeResult.SUCCESS;
        } finally {
            lock.unlock();
        }
        return ConsumeResult.SUCCESS;
    }

    /**
     * 解析消息体为实体类
     */
    private IotDeviceMessage parseMessageBody(MessageView messageView) {
        try {
            ByteBuffer body = messageView.getBody();
            if (body.remaining() == 0) {
                log.error("Message body is empty, messageId: {}", messageView.getMessageId());
                return null;
            }

            String message = StandardCharsets.UTF_8.decode(body).toString();
            return JSON.parseObject(message, IotDeviceMessage.class);

        } catch (JSONException e) {
            log.error("JSON解析失败, messageId: {}, content: {}",
                    messageView.getMessageId(),
                    messageView.getBody(),
                    e
            );
            return null;
        }
    }

}
