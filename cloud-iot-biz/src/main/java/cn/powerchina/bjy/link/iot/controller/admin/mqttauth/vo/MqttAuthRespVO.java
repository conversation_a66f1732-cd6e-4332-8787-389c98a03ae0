package cn.powerchina.bjy.link.iot.controller.admin.mqttauth.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - mqtt认证 Response VO")
@Data
@ExcelIgnoreUnannotated
public class MqttAuthRespVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "24425")
    @ExcelProperty("主键id")
    private Long id;

    @Schema(description = "用户名", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @ExcelProperty("用户名")
    private String userName;

    @Schema(description = "密钥")
    @ExcelProperty("密钥")
    private String secret;

    @Schema(description = "认证类型(1:应用管理;2:产品管理;3:设备管理)", example = "2")
    @ExcelProperty("认证类型(1:应用管理;2:产品管理;3:设备管理)")
    private Integer type;

    @Schema(description = "状态(1:启用;0:禁用)", example = "2")
    @ExcelProperty("状态(1:启用;0:禁用)")
    private Integer status;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}