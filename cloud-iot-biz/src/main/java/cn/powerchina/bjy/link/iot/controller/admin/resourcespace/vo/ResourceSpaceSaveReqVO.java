package cn.powerchina.bjy.link.iot.controller.admin.resourcespace.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Schema(description = "管理后台 - 资源空间新增/修改 Request VO")
@Data
public class ResourceSpaceSaveReqVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1652")
    private Long id;

    @Schema(description = "空间名称")
    @NotBlank(message = "空间名称必填")
    private String spaceName;

    @Schema(description = "空间APPID")
    private String spaceAppid;

}