package cn.powerchina.bjy.link.iot.controller.admin.product;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.link.iot.controller.admin.product.bo.ProductBO;
import cn.powerchina.bjy.link.iot.controller.admin.product.bo.ProductBO;
import cn.powerchina.bjy.link.iot.controller.admin.product.vo.*;
import cn.powerchina.bjy.link.iot.service.device.DeviceService;
import cn.powerchina.bjy.link.iot.service.product.ProductService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.security.PermitAll;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;


@Tag(name = "管理后台 - 产品")
@RestController
@RequestMapping("/iot/product")
@Validated
public class ProductController {

    @Resource
    private ProductService productService;

    @Resource
    private DeviceService deviceService;

    @PostMapping("/create")
    @Operation(summary = "创建产品")
//    @PreAuthorize("@ss.hasPermission('iot:product:create')")
    public CommonResult<Long> createProduct(@Valid @RequestBody ProductSaveReqVO createReqVO) {
        return success(productService.createProduct(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新产品")
//    @PreAuthorize("@ss.hasPermission('iot:product:update')")
    public CommonResult<Boolean> updateProduct(@Valid @RequestBody ProductSaveReqVO updateReqVO) {
        productService.updateProduct(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除产品")
    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('iot:product:delete')")
    public CommonResult<Boolean> deleteProduct(@RequestParam("id") Long id) {
        productService.deleteProduct(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得产品")
    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('iot:product:query')")
    public CommonResult<ProductRespVO> getProduct(@RequestParam("id") Long id) {
        ProductBO productBO = productService.getProductBO(id);
        ProductRespVO result = BeanUtils.toBean(productBO, ProductRespVO.class);
        if (productBO != null) {
            Long deviceCount = deviceService.getDeviceCountByProductCode(productBO.getProductCode());
            result.setDeviceCount(deviceCount);
        }
        return success(result);
    }

    @GetMapping("/page")
    @Operation(summary = "获得产品分页")
//    @PreAuthorize("@ss.hasPermission('iot:product:query')")
    public CommonResult<PageResult<ProductRespVO>> getProductPage(@Valid ProductPageReqVO pageReqVO) {
        PageResult<ProductBO> pageResult = productService.getProductPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ProductRespVO.class));
    }

    @PutMapping("/deviceLimit")
    @Operation(summary = "更新产品设备限额")
    public CommonResult<Boolean> updateProductDeviceLimit(@Valid @RequestBody ProductDeviceLimitUpdateReqVO updateReqVO) {
        productService.updateDeviceLimit(updateReqVO.getId(), updateReqVO.getDeviceLimit());
        return success(true);
    }

    @PutMapping("/update-dynamic-register")
    @Operation(summary = "更新产品动态注册")
    public CommonResult<Boolean> updateProductDynamicRegister(@Valid @RequestBody ProductDynamicRegisterUpdateReqVO updateReqVO) {
        productService.updateDynamicRegister(updateReqVO.getId(), updateReqVO.getDynamicRegister());
        return success(true);
    }

    @PutMapping("/update-pre-register")
    @Operation(summary = "更新产品预注册")
    public CommonResult<Boolean> updateProductPreRegister(@Valid @RequestBody ProductPreRegisterUpdateReqVO updateReqVO) {
        productService.updatePreRegister(updateReqVO.getId(), updateReqVO.getPreRegister());
        return success(true);
    }
}
