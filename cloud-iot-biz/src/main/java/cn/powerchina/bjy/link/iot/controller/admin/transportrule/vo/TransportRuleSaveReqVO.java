package cn.powerchina.bjy.link.iot.controller.admin.transportrule.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import jakarta.validation.constraints.*;

@Schema(description = "管理后台 - 转发规则新增/修改 Request VO")
@Data
public class TransportRuleSaveReqVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "29315")
    private Long id;

    @Schema(description = "规则名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    //@NotEmpty(message = "规则名称不能为空")
    private String name;

    @Schema(description = "转发规则编码", requiredMode = Schema.RequiredMode.REQUIRED)
    //@NotEmpty(message = "转发规则编码不能为空")
    private String ruleCode;

    @Schema(description = "启用状态（0:未启动 1：运行中）", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    //@NotNull(message = "启用状态（0:未启动 1：运行中）不能为空")
    private Integer status;

    @Schema(description = "规则描述", example = "你猜")
    private String remark;

}