package cn.powerchina.bjy.link.iot.controller.admin.product.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(description = "管理后台 - 产品预注册更新 Request VO")
@Data
public class ProductPreRegisterUpdateReqVO {

    @Schema(description = "编号", required = true, example = "1024")
    @NotNull(message = "编号不能为空")
    private Long id;

    @Schema(description = "预注册", required = true, example = "1")
    @NotNull(message = "预注册不能为空")
    private Integer preRegister;
}
