package cn.powerchina.bjy.link.iot.dto.up;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 检测设备在线状态
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EdgeCheckOnlineStatus implements Serializable {

    /**
     * 节点类型(0直连，1网关，2网关子设备）
     */
    private Integer nodeType;

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 设备编码
     */
    private String deviceCode;

    /**
     * 网关实例编码
     */
    private String edgeCode;

    /**
     * 从站号
     */
    private String slaveId;

    /**
     * 检测结果，1：正常，0：失败
     */
    private Integer checkResult;

    /**
     * 子设备状态
     */
    private List<ChildDevice> childDeviceStatus;

    /**
     * 采集时刻时间
     */
    private Long currentTime;

    /**
     * 子设备
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ChildDevice {
        /**
         * 设备编码
         */
        private String deviceCode;

        /**
         * MCU通道编码
         */
        private String mcuChannel;

        /**
         * 检测结果，1：正常，0：失败
         */
        private Integer checkResult;
    }

}
