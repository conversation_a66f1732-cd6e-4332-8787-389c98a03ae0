package cn.powerchina.bjy.link.iot.service.modelTemplateDetails;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.link.iot.controller.admin.modelTemplateDetails.bo.ModelTemplateDetailsBO;
import cn.powerchina.bjy.link.iot.controller.admin.modelTemplateDetails.vo.ModelTemplateDetailsPageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.modelTemplateDetails.vo.ModelTemplateDetailsSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.modelTemplateDetails.ModelTemplateDetailsDO;
import cn.powerchina.bjy.link.iot.dal.mysql.modelTemplateDetails.ModelTemplateDetailsMapper;
import cn.powerchina.bjy.link.iot.enums.ErrorCodeConstants;
import cn.powerchina.bjy.link.iot.enums.ThingModeTypeEnum;
import cn.powerchina.bjy.link.iot.util.SnowFlakeUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import jakarta.annotation.Resource;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;


@Service
@ToString
@Slf4j
public class ModelTemplateDetailsServiceImpl implements ModelTemplateDetailsService {

    @Resource
    private ModelTemplateDetailsMapper modelTemplateDetailsMapper;

    @Autowired
    private SnowFlakeUtil snowFlakeUtil;
    @Override
    public PageResult<ModelTemplateDetailsBO> getModelTemplateDetailsPage(ModelTemplateDetailsPageReqVO pageReqVO) {
        PageResult<ModelTemplateDetailsDO> doPageResult = modelTemplateDetailsMapper.selectPage(pageReqVO);
        PageResult<ModelTemplateDetailsBO> bOPageResult = BeanUtils.toBean(doPageResult, ModelTemplateDetailsBO.class);
        return bOPageResult;
    }

    @Override
    public ModelTemplateDetailsDO getModelTemplateDetails(Long id) {
        return Objects.isNull(id) ? null : modelTemplateDetailsMapper.selectById(id);
    }

    @Override
    public ModelTemplateDetailsBO getModelTemplateDetailsBO(Long id) {

        ModelTemplateDetailsDO modelTemplateDetailsDO = getModelTemplateDetails(id);
        if (Objects.nonNull(modelTemplateDetailsDO)) {
            ModelTemplateDetailsBO modelTemplateDetailsBO = BeanUtils.toBean(modelTemplateDetailsDO, ModelTemplateDetailsBO.class);
            return modelTemplateDetailsBO;
        }
        return null;
    }

    @Override
    public Long createModelTemplateDetails(ModelTemplateDetailsSaveReqVO createReqVO) {
        // 校验物模板名称是否重复
        validateTemplateDetailsNameExists(createReqVO.getTemplateDetailsName(),createReqVO.getTemplateId());
        // 校验物模板标识符是否重复
        validateTemplateIdentityExists(createReqVO.getTemplateIdentity(),createReqVO.getTemplateId());
        // 插入分类
        ModelTemplateDetailsDO modelTemplateDetailsDO = BeanUtils.toBean(createReqVO, ModelTemplateDetailsDO.class);
        // 避免数据库报错 TODO 待优化
        if (StringUtils.isBlank(modelTemplateDetailsDO.getOutputParams())) {
            modelTemplateDetailsDO.setOutputParams(null);
        }
        if (StringUtils.isBlank(modelTemplateDetailsDO.getInputParams())) {
            modelTemplateDetailsDO.setInputParams(null);
        }
        if (StringUtils.isBlank(modelTemplateDetailsDO.getExtra())) {
            modelTemplateDetailsDO.setExtra(null);
        }
        if(createReqVO.getTemplateType()!=ThingModeTypeEnum.PROPERTY.getType())
        {
            //输入参数去重
            if(!StringUtils.isEmpty(createReqVO.getInputParams())) {
                JSONArray elements=JSONArray.parseArray(createReqVO.getInputParams().toString().replaceAll("\\\\",""));
                //参数名去重
                List<String> paramsNames = new ArrayList<>();
                JSONArray paramsArr = new JSONArray();
                ListUtils.emptyIfNull(elements).stream().map((e) -> (JSONObject)e).forEach(e -> {
                    String paramsName = e.getString("templateDetailsName");
                    if(!paramsNames.contains(paramsName)){
                        paramsNames.add(paramsName);
                        paramsArr.add(e);
                    }
                });
                //标识符去重
                List<String> identitys = new ArrayList<>();
                JSONArray inputList = new JSONArray();
                ListUtils.emptyIfNull(paramsArr).stream().map((e) -> (JSONObject)e).forEach(e -> {
                    String identity = e.getString("templateIdentity");
                    if(!identitys.contains(identity)){
                        identitys.add(identity);
                        inputList.add(e);
                    }
                });
                modelTemplateDetailsDO.setInputParams(inputList.toString());
            }
            //输出参数去重
            if(!StringUtils.isEmpty(createReqVO.getOutputParams())) {
                JSONArray elements=JSONArray.parseArray(createReqVO.getOutputParams().toString().replaceAll("\\\\",""));
                //参数名去重
                List<String> paramsNames = new ArrayList<>();
                JSONArray paramsArr = new JSONArray();
                ListUtils.emptyIfNull(elements).stream().map((e) -> (JSONObject)e).forEach(e -> {
                    String paramsName = e.getString("templateDetailsName");
                    if(!paramsNames.contains(paramsName)){
                        paramsNames.add(paramsName);
                        paramsArr.add(e);
                    }
                });
                //标识符去重
                List<String> identitys = new ArrayList<>();
                JSONArray outList = new JSONArray();
                ListUtils.emptyIfNull(paramsArr).stream().map((e) -> (JSONObject)e).forEach(e -> {
                    String identity = e.getString("templateIdentity");
                    if(!identitys.contains(identity)){
                        identitys.add(identity);
                        outList.add(e);
                    }
                });
                modelTemplateDetailsDO.setOutputParams(outList.toString());
            }
        }
        modelTemplateDetailsDO.setId(snowFlakeUtil.snowflakeId());
        modelTemplateDetailsMapper.insert(modelTemplateDetailsDO);
        return modelTemplateDetailsDO.getId();
    }

    @Override
    public void updateModelTemplateDetails(ModelTemplateDetailsSaveReqVO updateReqVO) {
        ModelTemplateDetailsDO modelTemplateDetailsDO=modelTemplateDetailsMapper.selectById(updateReqVO.getId());
        // 校验存在
        if ( modelTemplateDetailsDO== null) {
            throw exception(ErrorCodeConstants.TEMPLATE_DETAILS_NOT_EXISTS);
        }
        if(!StringUtils.isEmpty(updateReqVO.getTemplateDetailsName())) {
            if(!updateReqVO.getTemplateDetailsName().equals(modelTemplateDetailsDO.getTemplateDetailsName())){
                // 校验产品是否重名
                validateTemplateDetailsNameExists(updateReqVO.getTemplateDetailsName(),updateReqVO.getTemplateId());
            }
        }
        if(!StringUtils.isEmpty(updateReqVO.getTemplateIdentity())) {
            if(!updateReqVO.getTemplateIdentity().equals(modelTemplateDetailsDO.getTemplateIdentity())){
                // 校验物模板标识符是否重复
                validateTemplateIdentityExists(updateReqVO.getTemplateIdentity(),updateReqVO.getTemplateId());
            }
        }
        if(updateReqVO.getTemplateType()!=ThingModeTypeEnum.PROPERTY.getType()){
            //输入参数去重
            if(!StringUtils.isEmpty(updateReqVO.getInputParams())) {
                JSONArray elements=JSONArray.parseArray(updateReqVO.getInputParams().toString().replaceAll("\\\\",""));
                //参数名去重
                List<String> paramsNames = new ArrayList<>();
                JSONArray paramsArr = new JSONArray();
                ListUtils.emptyIfNull(elements).stream().map((e) -> (JSONObject)e).forEach(e -> {
                    String paramsName = e.getString("templateDetailsName");
                    if(!paramsNames.contains(paramsName)){
                        paramsNames.add(paramsName);
                        paramsArr.add(e);
                    }
                });
                //标识符去重
                List<String> identitys = new ArrayList<>();
                JSONArray inputList = new JSONArray();
                ListUtils.emptyIfNull(paramsArr).stream().map((e) -> (JSONObject)e).forEach(e -> {
                    String identity = e.getString("templateIdentity");
                    if(!identitys.contains(identity)){
                        identitys.add(identity);
                        inputList.add(e);
                    }
                });
                updateReqVO.setInputParams(inputList.toString());
            }
            //输出参数去重
            if(!StringUtils.isEmpty(updateReqVO.getOutputParams())) {
                JSONArray elements=JSONArray.parseArray(updateReqVO.getOutputParams().toString().replaceAll("\\\\",""));
                //参数名去重
                List<String> paramsNames = new ArrayList<>();
                JSONArray paramsArr = new JSONArray();
                ListUtils.emptyIfNull(elements).stream().map((e) -> (JSONObject)e).forEach(e -> {
                    String paramsName = e.getString("templateDetailsName");
                    if(!paramsNames.contains(paramsName)){
                        paramsNames.add(paramsName);
                        paramsArr.add(e);
                    }
                });
                //标识符去重
                List<String> identitys = new ArrayList<>();
                JSONArray outList = new JSONArray();
                ListUtils.emptyIfNull(paramsArr).stream().map((e) -> (JSONObject)e).forEach(e -> {
                    String identity = e.getString("templateIdentity");
                    if(!identitys.contains(identity)){
                        identitys.add(identity);
                        outList.add(e);
                    }
                });
                updateReqVO.setOutputParams(outList.toString());
            }
        }
        // 更新
        ModelTemplateDetailsDO updateObj = BeanUtils.toBean(updateReqVO, ModelTemplateDetailsDO.class);
        modelTemplateDetailsMapper.updateById(updateObj);
    }

    @Override
    public void updateModelTemplateDetails(ModelTemplateDetailsDO updateModelTemplateDetailsDO) {
        modelTemplateDetailsMapper.updateById(updateModelTemplateDetailsDO);
    }

    @Override
    public void deleteModelTemplateDetails(Long id) {
        // 校验是否存在
        validateTemplateDetailsExists(id);
        // 删除分类
        modelTemplateDetailsMapper.deleteById(id);

    }
    /**
     * 根据产品名称校验产品是否存在
     * 新增名称唯一
     *
     * @param templateDetailsName     产品名称
     */
    private void validateTemplateDetailsNameExists(String templateDetailsName,String templateId) {

        QueryWrapper<ModelTemplateDetailsDO> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(ModelTemplateDetailsDO::getTemplateDetailsName, templateDetailsName).eq(ModelTemplateDetailsDO::getTemplateId, templateId).eq(ModelTemplateDetailsDO::getDeleted, Boolean.FALSE);
        if (modelTemplateDetailsMapper.exists(wrapper)) {
            throw exception(ErrorCodeConstants.TEMPLATE_DETAILS_NAME_EXITS);
        }
    }
    private void validateTemplateIdentityExists(String templateIdentity,String templateId) {

        QueryWrapper<ModelTemplateDetailsDO> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(ModelTemplateDetailsDO::getTemplateIdentity, templateIdentity).eq(ModelTemplateDetailsDO::getTemplateId, templateId).eq(ModelTemplateDetailsDO::getDeleted, Boolean.FALSE);
        if (modelTemplateDetailsMapper.exists(wrapper)) {
            throw exception(ErrorCodeConstants.TEMPLATE_DETAILS_TEMPLATEIDENTITY_EXITS);
        }
    }
    private void validateTemplateDetailsExists(Long id) {
        if (modelTemplateDetailsMapper.selectById(id) == null) {
            throw exception(ErrorCodeConstants.TEMPLATE_DETAILS_NOT_EXISTS);
        }
    }
}