package cn.powerchina.bjy.link.iot.controller.admin.edgechannel.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 通道 Response VO")
@Data
@ExcelIgnoreUnannotated
public class EdgeChannelRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "3853")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "关联的驱动code", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("关联的驱动code")
    private String driverCode;

    @Schema(description = "关联的边缘实例code")
    @ExcelProperty("关联的边缘实例code")
    private String edgeCode;

    @Schema(description = "连接方式（1-RTU；2-TCP）", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("连接方式（1-RTU；2-TCP）")
    private Integer connectType;

    @Schema(description = "通道编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("通道编码")
    private String channelCode;

    @Schema(description = "通道名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "")
    @ExcelProperty("通道名称")
    private String channelName;

    @Schema(description = "差异化扩展")
    @ExcelProperty("差异化扩展")
    private String extra;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}