package cn.powerchina.bjy.link.iot.api.datapermissions;

import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.link.iot.api.datapermissions.dto.DataPermissionsDto;
import cn.powerchina.bjy.link.iot.controller.admin.datapermissions.vo.DataPermissionsSaveReqVO;
import cn.powerchina.bjy.link.iot.service.datapermissions.DataPermissionsService;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RestController
@Validated
public class DatapermissionsApiImpl implements DatapermissionsApi{

    @Resource
    private DataPermissionsService dataPermissionsService;

    @Override
    public Boolean createDataPermissions(DataPermissionsDto createReqVO) {
        return dataPermissionsService.createDataPermissions(BeanUtils.toBean(createReqVO, DataPermissionsSaveReqVO.class));
    }
}
