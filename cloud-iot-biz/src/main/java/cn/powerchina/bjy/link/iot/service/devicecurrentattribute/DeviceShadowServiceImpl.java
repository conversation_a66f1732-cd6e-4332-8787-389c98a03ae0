package cn.powerchina.bjy.link.iot.service.devicecurrentattribute;

import cn.hutool.core.collection.CollectionUtil;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.iot.controller.admin.devicecurrentattribute.vo.DeviceShadowPageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.devicecurrentattribute.vo.DeviceShadowSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.devicecurrentattribute.DeviceShadowDO;
import cn.powerchina.bjy.link.iot.dal.mysql.shadow.DeviceShadowMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.List;

/**
 * 设备上报的最新属性 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class DeviceShadowServiceImpl implements DeviceShadowService {

    @Resource
    private DeviceShadowMapper deviceShadowMapper;

    @Override
    public Long createDeviceShadow(DeviceShadowSaveReqVO createReqVO) {
        // 插入
        DeviceShadowDO deviceShadow = BeanUtils.toBean(createReqVO, DeviceShadowDO.class);
        deviceShadowMapper.insert(deviceShadow);
        // 返回
        return deviceShadow.getId();
    }

    @Override
    public void updateDeviceShadow(DeviceShadowSaveReqVO updateReqVO) {
        // 更新
        DeviceShadowDO updateObj = BeanUtils.toBean(updateReqVO, DeviceShadowDO.class);
        deviceShadowMapper.updateById(updateObj);
    }

    @Override
    public void deleteDeviceShadow(Long id) {
        // 删除
        deviceShadowMapper.deleteById(id);
    }


    @Override
    public DeviceShadowDO getDeviceShadow(Long id) {
        return deviceShadowMapper.selectById(id);
    }

    @Override
    public PageResult<DeviceShadowDO> getDeviceShadowPage(DeviceShadowPageReqVO pageReqVO) {
        return deviceShadowMapper.selectPage(pageReqVO);
    }

    @Override
    public void createOrUpdateShadow(List<DeviceShadowDO> deviceShadowDOList) {
        if (CollectionUtil.isNotEmpty(deviceShadowDOList)) {
            List<DeviceShadowDO> newList = new ArrayList<>();
            List<DeviceShadowDO> updateList = new ArrayList<>();
            deviceShadowDOList.forEach(item -> {
                if (item.getId() == null) {
                    newList.add(item);
                } else {
                    updateList.add(item);
                }
            });
            if (CollectionUtil.isNotEmpty(newList)) {
                deviceShadowMapper.insertBatch(newList);
            }
            if (CollectionUtil.isNotEmpty(updateList)) {
                deviceShadowMapper.updateBatch(updateList);
            }
        }
    }

    @Override
    public void createShadow(List<DeviceShadowDO> deviceShadowDOList) {
        if (CollectionUtil.isNotEmpty(deviceShadowDOList)) {
            deviceShadowMapper.insertBatch(deviceShadowDOList);
        }
    }

    @Override
    public void updateShadow(List<DeviceShadowDO> deviceShadowDOList) {
        if (CollectionUtil.isNotEmpty(deviceShadowDOList)) {
            deviceShadowMapper.updateBatch(deviceShadowDOList);
        }
    }

    @Override
    public List<DeviceShadowDO> getShadowListByDeviceCode(String deviceCode) {
        return deviceShadowMapper.selectList(new LambdaQueryWrapperX<DeviceShadowDO>().eq(DeviceShadowDO::getDeviceCode, deviceCode));
    }

    @Override
    public DeviceShadowDO getShadowByDeviceCodeAndThingIdentity(String deviceCode, String thingIdentity) {
        return deviceShadowMapper.selectOne(new LambdaQueryWrapperX<DeviceShadowDO>().eq(DeviceShadowDO::getDeviceCode, deviceCode).
                eq(DeviceShadowDO::getThingIdentity, thingIdentity));

    }

    @Override
    public List<DeviceShadowDO> getShadowList() {
        return deviceShadowMapper.selectList();
    }

}