package cn.powerchina.bjy.link.iot.dal.dataobject.user;

import cn.powerchina.bjy.cloud.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;
import com.baomidou.mybatisplus.annotation.*;

/**
 * 用户信息 DO
 *
 * <AUTHOR>
 */
@TableName("iot_user")
@KeySequence("iot_user_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserDO extends BaseDO {

    /**
     * 主键id，system_user关联用户id
     */
    @TableId
    private Long id;
    /**
     * 用户账号
     */
    private String username;
    /**
     * 姓名
     */
    private String name;
    /**
     * 手机号码
     */
    private String mobile;
    /**
     * 部门名称
     */
    private String deptName;
    /**
     * 职务
     */
    private String postName;
    /**
     * 用户邮箱
     */
    private String email;
    /**
     * 启用状态（0正常 1停用）
     */
    private Integer status;

}