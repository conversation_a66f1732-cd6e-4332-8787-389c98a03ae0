package cn.powerchina.bjy.link.iot.controller.admin.edgechannel;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.link.iot.controller.admin.edgechannel.vo.EdgeChannelPageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.edgechannel.vo.EdgeChannelRespVO;
import cn.powerchina.bjy.link.iot.controller.admin.edgechannel.vo.EdgeChannelSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.edgechannel.EdgeChannelDO;
import cn.powerchina.bjy.link.iot.service.edgechannel.EdgeChannelService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 通道")
@RestController
@RequestMapping("/iot/edge-channel")
@Validated
public class EdgeChannelController {

    @Resource
    private EdgeChannelService edgeChannelService;

    @PostMapping("/create")
    @Operation(summary = "创建通道")
//    @PreAuthorize("@ss.hasPermission('iot:edge-channel:create')")
    public CommonResult<Long> createEdgeChannel(@Valid @RequestBody EdgeChannelSaveReqVO createReqVO) {
        return success(edgeChannelService.createEdgeChannel(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新通道")
//    @PreAuthorize("@ss.hasPermission('iot:edge-channel:update')")
    public CommonResult<Boolean> updateEdgeChannel(@Valid @RequestBody EdgeChannelSaveReqVO updateReqVO) {
        edgeChannelService.updateEdgeChannel(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除通道")
    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('iot:edge-channel:delete')")
    public CommonResult<Boolean> deleteEdgeChannel(@RequestParam("id") Long id) {
        edgeChannelService.deleteEdgeChannel(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得通道")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('iot:edge-channel:query')")
    public CommonResult<EdgeChannelRespVO> getEdgeChannel(@RequestParam("id") Long id) {
        EdgeChannelDO edgeChannel = edgeChannelService.getEdgeChannel(id);
        return success(BeanUtils.toBean(edgeChannel, EdgeChannelRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得通道分页")
//    @PreAuthorize("@ss.hasPermission('iot:edge-channel:query')")
    public CommonResult<PageResult<EdgeChannelRespVO>> getEdgeChannelPage(@Valid EdgeChannelPageReqVO pageReqVO) {
        PageResult<EdgeChannelDO> pageResult = edgeChannelService.getEdgeChannelPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, EdgeChannelRespVO.class));
    }

}