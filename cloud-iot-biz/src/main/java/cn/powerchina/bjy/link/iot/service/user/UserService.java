package cn.powerchina.bjy.link.iot.service.user;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.link.iot.controller.admin.user.vo.UserPageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.user.vo.UserPasswordVO;
import cn.powerchina.bjy.link.iot.controller.admin.user.vo.UserRespVO;
import cn.powerchina.bjy.link.iot.controller.admin.user.vo.UserSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.user.UserDO;
import jakarta.validation.*;

/**
 * 用户信息 Service 接口
 *
 * <AUTHOR>
 */
public interface UserService {

    /**
     * 创建用户信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createUser(@Valid UserSaveReqVO createReqVO);

    /**
     * 更新用户信息
     *
     * @param updateReqVO 更新信息
     */
    void updateUser(@Valid UserSaveReqVO updateReqVO);

    /**
     * 删除用户信息
     *
     * @param id 编号
     */
    void deleteUser(Long id);

    /**
     * 获得用户信息
     *
     * @param id 编号
     * @return 用户信息
     */
    UserDO getUser(Long id);

    /**
     * 获得用户信息分页
     *
     * @param pageReqVO 分页查询
     * @return 用户信息分页
     */
    PageResult<UserRespVO> getUserPage(UserPageReqVO pageReqVO);

    /**
     * 修改状态
     *
     * @param id     用户编号
     * @param status 状态
     */
    void updateUserStatus(Long id, Integer status);

    /**
     * 更新密码
     *
     * @param userPasswordVO
     */
    void updatePassword(@Valid UserPasswordVO userPasswordVO);

    /**
     * 根据用户名联动获取主数据
     * @param updateReqVO
     * @return
     */
    UserDO getUserByUsername(UserSaveReqVO updateReqVO);

}