package cn.powerchina.bjy.link.iot.dto.down;

import cn.powerchina.bjy.link.iot.model.MessageToEdge;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 边缘网关同步物联网平台注册信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EdgeSyncUpDTO implements MessageToEdge, Serializable {

    /**
     * 租户
     */
    private String tenant;

    /**
     * 边缘网关名称
     */
    private String name;

    /**
     * 边缘网关应用名称
     */
    private String service;

    /**
     * 边缘网关主机
     */
    private String host;

    /**
     * 边缘网关端口
     */
    private Integer port;

    /**
     * 边缘网关描述
     */
    private String remark;

    /**
     * 客户端编码
     */
    private String client;

    /**
     * mac地址
     */
    private String macAddr;

    /**
     * 边缘网关节点
     */
    private String node;

    /**
     * 上报时间
     */
    private Long currentTime;

    /**
     * 上报类型，1：注册，2：心跳，3：断开
     */
    private Integer syncType;

    /**
     * 资源空间id
     */
    private Long resourceSpaceId;
}
