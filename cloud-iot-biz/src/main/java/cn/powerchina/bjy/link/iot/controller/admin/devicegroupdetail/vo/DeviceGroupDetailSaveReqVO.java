package cn.powerchina.bjy.link.iot.controller.admin.devicegroupdetail.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 设备分组明细新增/修改 Request VO")
@Data
public class DeviceGroupDetailSaveReqVO {

    @Schema(description = "设备分组id")
    @NotNull(message = "请选择设备分组")
    private Long deviceGroupId;

    @Schema(description = "设备编号")
    private List<String> deviceCodeList;

}