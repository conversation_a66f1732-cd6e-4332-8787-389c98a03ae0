package cn.powerchina.bjy.link.iot.controller.admin.devicegroupdetail.vo;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

@Schema(description = "管理后台 - 设备分组明细分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DeviceGroupDetailPageReqVO extends PageParam {

    @Schema(description = "设备分组id")
    @NotNull(message = "请选择设备分组")
    private Long deviceGroupId;

    @Schema(description = "设备编号")
    private String deviceCode;

    @Schema(description = "设备唯一标识")
    @ExcelProperty("设备唯一标识")
    private String deviceSerial;

    @Schema(description = "产品编码")
    private String productCode;

    @Schema(description = "节点类型(0直连，1网关，2网关子设备）")
    @ExcelProperty("节点类型(0直连，1网关，2网关子设备）")
    private Integer nodeType;

    @Schema(description = "设备分组详情中设备code集合")
    private List<String> codes;

}