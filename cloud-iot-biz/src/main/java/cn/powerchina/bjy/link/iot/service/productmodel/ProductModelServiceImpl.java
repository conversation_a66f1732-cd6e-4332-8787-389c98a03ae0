package cn.powerchina.bjy.link.iot.service.productmodel;

import cn.hutool.core.collection.CollectionUtil;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.iot.controller.admin.productmodel.vo.ProductModelPageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.productmodel.vo.ProductModelSaveReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.productmodel.vo.ThingModelDateOrTextDataSpecs;
import cn.powerchina.bjy.link.iot.controller.admin.transportsource.vo.ProductModelRelayVO;
import cn.powerchina.bjy.link.iot.controller.admin.transportsource.vo.ProductRelayVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.product.ProductDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.productmodel.ProductModelDO;
import cn.powerchina.bjy.link.iot.dal.mysql.product.ProductMapper;
import cn.powerchina.bjy.link.iot.dal.mysql.productmodel.ProductModelMapper;
import cn.powerchina.bjy.link.iot.dal.tdengine.IotDevicePropertyMapper;
import cn.powerchina.bjy.link.iot.enums.ProductsModelDataTypeEnum;
import cn.powerchina.bjy.link.iot.enums.ThingModeTypeEnum;
import cn.powerchina.bjy.link.iot.enums.TransportSourceTypeEnum;
import cn.powerchina.bjy.link.iot.framework.tdengine.core.TDengineTableField;
import cn.powerchina.bjy.link.iot.service.transportsource.TransportSourceService;
import cn.powerchina.bjy.link.iot.thingmodel.IotDataSpecsDataTypeEnum;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.link.iot.enums.ErrorCodeConstants.*;
import static cn.powerchina.bjy.link.iot.service.tdengine.IotDevicePropertyServiceImpl.TYPE_MAPPING;

/**
 * 产品物模型 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class ProductModelServiceImpl implements ProductModelService {

    @Resource
    private ProductModelMapper productModelMapper;

    @Resource
    private EventRepeatCheck eventRepeatCheck;

    @Resource
    private FunctionRepeatCheck functionRepeatCheck;

    @Resource
    private PropertyRepeatCheck propertyRepeatCheck;

    @Resource
    private IotDevicePropertyMapper iotDevicePropertyMapper;

    @Resource
    private ProductMapper productMapper;

    @Resource
    private TransportSourceService transportSourceService;

    @Override
    public Long createProductModel(ProductModelSaveReqVO createReqVO) {
        // 插入
        ProductModelDO productModel = BeanUtils.toBean(createReqVO, ProductModelDO.class);

        // 避免数据库报错 TODO 待优化
        if (StringUtils.isBlank(productModel.getOutputParams())) {
            productModel.setOutputParams(null);
        }
        if (StringUtils.isBlank(productModel.getInputParams())) {
            productModel.setInputParams(null);
        }
        if (StringUtils.isBlank(productModel.getExtra())) {
            productModel.setExtra(null);
        }
        // 校验
        validateProductModel(createReqVO);
        productModelMapper.insert(productModel);

        if (ThingModeTypeEnum.PROPERTY.getType() == productModel.getThingType()) {
            TDengineTableField field = new TDengineTableField();
            field.setType(TYPE_MAPPING.get(productModel.getDatatype()));
            field.setField(productModel.getThingIdentity());
            if (IotDataSpecsDataTypeEnum.TEXT.getDataType().equals(productModel.getDatatype())) {
                ObjectMapper mapper = new ObjectMapper();
                try {
                    ThingModelDateOrTextDataSpecs thingModelDateOrTextDataSpecs = mapper.readValue(productModel.getInputParams(), ThingModelDateOrTextDataSpecs.class);
                    field.setLength(thingModelDateOrTextDataSpecs.getLength());
                } catch (JsonProcessingException e) {
                    log.error("JSON解析失败：{}", productModel.getInputParams(), e);
                }
            }
            try {
                iotDevicePropertyMapper.alterProductPropertySTableAddField(productModel.getProductCode(), field);
            } catch (Exception e) {
                //回滚mysql
                log.warn("时序数据库操作失败，回滚mysql");
                productModelMapper.deleteById(productModel);
                throw exception(PRODUCT_MODEL_TD_IDENTIFY_CREATE);
            }
        }
        //数据转发
        try {
            productModelDataForwarding(productModel, ProductsModelDataTypeEnum.PRODUCT_MODEL_CREATE);
        } catch (Exception e) {
            log.error("产品物模型数据转发失败：{}", productModel, e);
        }
        return productModel.getId();
    }

    @Override
    public void updateProductModel(ProductModelSaveReqVO updateReqVO) {
        // 校验存在并获取老数据
        ProductModelDO productModelOld = validateProductModelExists(updateReqVO.getId());

        // 校验
        validateProductModel(updateReqVO);
        // 更新
        ProductModelDO updateObj = BeanUtils.toBean(updateReqVO, ProductModelDO.class);

        // 避免数据库报错 TODO 待优化
        if (StringUtils.isBlank(updateObj.getOutputParams())) {
            updateObj.setOutputParams("{}");
        }
        if (StringUtils.isBlank(updateObj.getInputParams())) {
            updateObj.setInputParams("{}");
        }
        if (StringUtils.isBlank(updateObj.getExtra())) {
            updateObj.setExtra("{}");
        }
        if (updateReqVO.getThingType() != ThingModeTypeEnum.PROPERTY.getType()) {
            //输入参数去重
            if (!StringUtils.isEmpty(updateReqVO.getInputParams())) {
                JSONArray elements = JSONArray.parseArray(updateReqVO.getInputParams().toString().replaceAll("\\\\", ""));
                //参数名去重
                List<String> paramsNames = new ArrayList<>();
                JSONArray paramsArr = new JSONArray();
                ListUtils.emptyIfNull(elements).stream().map((e) -> (JSONObject) e).forEach(e -> {
                    String paramsName = e.getString("templateDetailsName");
                    if (!paramsNames.contains(paramsName)) {
                        paramsNames.add(paramsName);
                        paramsArr.add(e);
                    }
                });
                //标识符去重
                List<String> identitys = new ArrayList<>();
                JSONArray inputList = new JSONArray();
                ListUtils.emptyIfNull(paramsArr).stream().map((e) -> (JSONObject) e).forEach(e -> {
                    String identity = e.getString("templateIdentity");
                    if (!identitys.contains(identity)) {
                        identitys.add(identity);
                        inputList.add(e);
                    }
                });
                updateReqVO.setInputParams(inputList.toString());
            }
            //输出参数去重
            if (!StringUtils.isEmpty(updateReqVO.getOutputParams())) {
                JSONArray elements = JSONArray.parseArray(updateReqVO.getOutputParams());
                //参数名去重
                List<String> paramsNames = new ArrayList<>();
                JSONArray paramsArr = new JSONArray();
                ListUtils.emptyIfNull(elements).stream().map((e) -> (JSONObject) e).forEach(e -> {
                    String paramsName = e.getString("templateDetailsName");
                    if (!paramsNames.contains(paramsName)) {
                        paramsNames.add(paramsName);
                        paramsArr.add(e);
                    }
                });
                //标识符去重
                List<String> identitys = new ArrayList<>();
                JSONArray outList = new JSONArray();
                ListUtils.emptyIfNull(paramsArr).stream().map((e) -> (JSONObject) e).forEach(e -> {
                    String identity = e.getString("templateIdentity");
                    if (!identitys.contains(identity)) {
                        identitys.add(identity);
                        outList.add(e);
                    }
                });
                updateReqVO.setOutputParams(outList.toString());
            }
        }
        if (ThingModeTypeEnum.PROPERTY.getType() == updateObj.getThingType()) {
            TDengineTableField field = new TDengineTableField();
            field.setType(TYPE_MAPPING.get(updateObj.getDatatype()));
            field.setField(updateObj.getThingIdentity());
            if (IotDataSpecsDataTypeEnum.TEXT.getDataType().equals(updateObj.getDatatype())) {
                ObjectMapper mapper = new ObjectMapper();
                try {
                    ThingModelDateOrTextDataSpecs thingModelDateOrTextDataSpecs = mapper.readValue(updateReqVO.getInputParams(), ThingModelDateOrTextDataSpecs.class);
                    field.setLength(thingModelDateOrTextDataSpecs.getLength());
                } catch (JsonProcessingException e) {
                    log.error("JSON解析失败：{}", updateObj.getInputParams(), e);
                }
            }
            List<Map> reportDataList = iotDevicePropertyMapper.selectSuperTable(updateReqVO.getProductCode());
            if (CollectionUtil.isNotEmpty(reportDataList)) {
                throw exception(PRODUCT_MODEL_TD_DATA_EXISTS);
            }
            try {
                //没有数据，删除超级表字段，重新新增
                TDengineTableField dropField = new TDengineTableField();
                dropField.setType(TYPE_MAPPING.get(productModelOld.getDatatype()));
                dropField.setField(productModelOld.getThingIdentity());
                iotDevicePropertyMapper.alterProductPropertySTableDropField(updateReqVO.getProductCode(), dropField);

                iotDevicePropertyMapper.alterProductPropertySTableAddField(updateReqVO.getProductCode(), field);
            } catch (Exception e) {
                log.error("超级表修改物模型属性异常{}", e.getMessage());
                throw exception(PRODUCT_MODEL_TD_IDENTIFY_UPDATE);
            }
        }
        productModelMapper.updateById(updateObj);
        //数据转发
        try {
            productModelDataForwarding(updateObj, ProductsModelDataTypeEnum.PRODUCT_MODEL_UPDATE);
        } catch (Exception e) {
            log.error("产品物模型数据转发失败：{}", updateObj, e);
        }
    }

    @Override
    public void deleteProductModel(Long id) {
        // 校验存在
        ProductModelDO productModel = validateProductModelExists(id);
        // 删除
        productModelMapper.deleteById(id);
        //组装转发数据
        ProductDO productDO = productMapper.selectByCode(productModel.getProductCode());
        ProductRelayVO productRelayVO = BeanUtils.toBean(productDO, ProductRelayVO.class);
        List<ProductModelRelayVO> productModelList = new ArrayList<>();
        productModelList.add(BeanUtils.toBean(productModel, ProductModelRelayVO.class));
        productRelayVO.setProductModelList(productModelList);
        //数据转发
        try {
            productModelDataForwarding(productModel, ProductsModelDataTypeEnum.PRODUCT_MODEL_DELETE);
        } catch (Exception e) {
            log.error("产品物模型数据转发失败：{}", productModel, e);
        }
    }

    private ProductModelDO validateProductModelExists(Long id) {
        ProductModelDO productModel = productModelMapper.selectById(id);
        if (productModel == null) {
            throw exception(PRODUCT_MODEL_NOT_EXISTS);
        }
        return productModel;
    }

    private void validateProductModel(ProductModelSaveReqVO productModel) {
        // 校验
        if (ThingModeTypeEnum.PROPERTY.getType() == productModel.getThingType()) {
            propertyRepeatCheck.repeatCheck(productModel);
        }

        if (ThingModeTypeEnum.FUNCTION.getType() == productModel.getThingType()) {
            functionRepeatCheck.repeatCheck(productModel);
        }

        if (ThingModeTypeEnum.EVENT.getType() == productModel.getThingType()) {
            eventRepeatCheck.repeatCheck(productModel);
        }
    }

    @Override
    public ProductModelDO getProductModel(Long id) {
        return productModelMapper.selectById(id);
    }

    @Override
    public ProductModelDO getProductModel(String productCode, String identify) {
        return productModelMapper.selectByProductCodeAndIdentify(productCode, identify);
    }

    @Override
    public PageResult<ProductModelDO> getProductModelPage(ProductModelPageReqVO pageReqVO) {
        return productModelMapper.selectPage(pageReqVO);
    }

    @Override
    public PageResult<ProductModelDO> getProductModelTypePage(String productCode, Integer thingType, PageParam pageParam) {
        QueryWrapper<ProductModelDO> wrapper = new QueryWrapper<>();
        wrapper.lambda()
                .eq(ProductModelDO::getProductCode, productCode)
                .eq(ProductModelDO::getThingType, thingType)
                .orderByDesc(ProductModelDO::getCreateTime);
        return productModelMapper.selectPage(pageParam, wrapper);
    }

    @Override
    public PageResult<ProductModelDO> getProductModelPropertyPage(String productCode, String thingName, PageParam pageParam, Integer[] readWriteType, int thingType) {
        QueryWrapper<ProductModelDO> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(ProductModelDO::getProductCode, productCode)
                .like(StringUtils.isNotBlank(thingName), ProductModelDO::getThingName, thingName)
                .in(readWriteType.length > 0, ProductModelDO::getReadWriteType, readWriteType)
                .eq(ProductModelDO::getThingType, thingType);
        return productModelMapper.selectPage(pageParam, wrapper);
    }

    @Override
    public List<ProductModelDO> getProductModelByProductCodeAndThingType(String productCode, Integer thingType) {
        return productModelMapper.selectList(new LambdaQueryWrapperX<ProductModelDO>()
                .eq(ProductModelDO::getProductCode, productCode)
                .eqIfPresent(ProductModelDO::getThingType, thingType)
                .orderByAsc(ProductModelDO::getThingType)
                .orderByDesc(ProductModelDO::getId));
    }

//    @Override
//    public List<EventTypeVO> getEventTypesByProduct(String productCode) {
//        List<ProductModelDO> list = productModelMapper.selectList(new LambdaQueryWrapperX<ProductModelDO>()
//                .eq(ProductModelDO::getProductCode, productCode)
//                .eq(ProductModelDO::getThingType, 3)
//                .orderByAsc(ProductModelDO::getEventType));
//        return list.stream()
//                .filter(pm -> pm.getEventType() != null)
//                .map(ProductModelDO::getEventType)
//                .distinct()
//                .map(type -> new EventTypeVO(type, getEventTypeName(type)))
//                .toList();
//    }

//    @Override
//    public List<EventNameVO> getEventNamesByProductAndType(String productCode, Integer eventType) {
//        List<ProductModelDO> list = productModelMapper.selectList(new LambdaQueryWrapperX<ProductModelDO>()
//                .eq(ProductModelDO::getProductCode, productCode)
//                .eq(ProductModelDO::getThingType, 3)
//                .eq(ProductModelDO::getEventType, eventType));
//        return list.stream()
//                .map(pm -> new EventNameVO(pm.getThingIdentity(), pm.getThingName()))
//                .toList();
//    }

    private String getEventTypeName(Integer type) {
        if (type == null) return "";
        return switch (type) {
            case 1 -> "信息";
            case 2 -> "告警";
            case 3 -> "故障";
            default -> String.valueOf(type);
        };
    }

    private void productModelDataForwarding(ProductModelDO productModel, ProductsModelDataTypeEnum modelDataType) {
        //组装转发数据
        ProductDO productDO = productMapper.selectByCode(productModel.getProductCode());
        ProductRelayVO productRelayVO = BeanUtils.toBean(productDO, ProductRelayVO.class);
        List<ProductModelRelayVO> productModelList = new ArrayList<>();
        ProductModelRelayVO productModelRelayVO = BeanUtils.toBean(productModel, ProductModelRelayVO.class);
        productModelRelayVO.setModelDataType(modelDataType.getCode());
        productModelList.add(productModelRelayVO);
        productRelayVO.setProductModelList(productModelList);
        //数据转发
        transportSourceService.dataForwarding(TransportSourceTypeEnum.PRODUCT_UPDATE, productRelayVO);
    }


}