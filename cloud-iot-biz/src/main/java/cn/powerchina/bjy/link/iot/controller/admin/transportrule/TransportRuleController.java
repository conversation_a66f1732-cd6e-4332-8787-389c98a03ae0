package cn.powerchina.bjy.link.iot.controller.admin.transportrule;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.link.iot.controller.admin.transportrule.vo.TransportRulePageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.transportrule.vo.TransportRuleRespVO;
import cn.powerchina.bjy.link.iot.controller.admin.transportrule.vo.TransportRuleSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.transportrule.TransportRuleDO;
import cn.powerchina.bjy.link.iot.service.transportrule.TransportRuleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;


@Tag(name = "管理后台 - 转发规则")
@RestController
@RequestMapping("/iot/transport-rule")
@Validated
public class TransportRuleController {

    @Resource
    private TransportRuleService transportRuleService;

    @PostMapping("/create")
    @Operation(summary = "创建转发规则")
    @PreAuthorize("@ss.hasPermission('iot:transport-rule:create')")
    public CommonResult<Long> createTransportRule(@Valid @RequestBody TransportRuleSaveReqVO createReqVO) {
        return success(transportRuleService.createTransportRule(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新转发规则")
    @PreAuthorize("@ss.hasPermission('iot:transport-rule:update')")
    public CommonResult<Boolean> updateTransportRule(@Valid @RequestBody TransportRuleSaveReqVO updateReqVO) {
        transportRuleService.updateTransportRule(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除转发规则")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('iot:transport-rule:delete')")
    public CommonResult<Boolean> deleteTransportRule(@RequestParam("id") Long id) {
        transportRuleService.deleteTransportRule(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得转发规则")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('iot:transport-rule:query')")
    public CommonResult<TransportRuleRespVO> getTransportRule(@RequestParam("id") Long id) {
        TransportRuleDO transportRule = transportRuleService.getTransportRule(id);
        return success(BeanUtils.toBean(transportRule, TransportRuleRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得转发规则分页")
    @PreAuthorize("@ss.hasPermission('iot:transport-rule:query')")
    public CommonResult<PageResult<TransportRuleRespVO>> getTransportRulePage(@Valid TransportRulePageReqVO pageReqVO) {
        PageResult<TransportRuleDO> pageResult = transportRuleService.getTransportRulePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, TransportRuleRespVO.class));
    }

    @PutMapping("/updateStatus")
    @Operation(summary = "更新转发规则状态")
    @PreAuthorize("@ss.hasPermission('iot:transport-rule:status')")
    public CommonResult<Boolean> updateStatus(@Valid @RequestBody TransportRuleSaveReqVO updateReqVO) {
        transportRuleService.updateTransportRule(updateReqVO);
        return success(true);
    }

}