package cn.powerchina.bjy.link.iot.controller.admin.modelTemplateDetails.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 物模板明细 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ModelTemplateDetailsRespVO {

    @Schema(description = "主键", example = "3695")
    private Long id;

    @Schema(description = "物模板ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("物模板ID")
    private Long templateId;

    @Schema(description = "物模板明细名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("物模板明细名称")
    private String templateDetailsName;

    @Schema(description = "物模板标识符", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("物模板标识符")
    private String templateIdentity;

    @Schema(description = "物模型类型，1-属性；2-服务；3-事件；", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("物模板类型")
    private Integer templateType;

    @Schema(description = "数据类型（integer、decimal、string、bool、array、enum）", example = "2")
    @ExcelProperty("数据类型")
    private String datatype;

    @Schema(description = "读写类型，thing_type为1时必填，1-读写；2-只读；3-只写", example = "1")
    @ExcelProperty("读写类型")
    private Integer readWriteType;

    @Schema(description = "事件类型，thing_type为3时必填，1-信息；2告警；3-故障", example = "2")
    @ExcelProperty("事件类型")
    private Integer eventType;

    @Schema(description = "输入参数")
    @ExcelProperty("输入参数")
    private String inputParams;

    @Schema(description = "输出参数")
    @ExcelProperty("输出参数")
    private String outputParams;

    @Schema(description = "属性扩展信息")
    @ExcelProperty("属性扩展信息")
    private String extra;

    @Schema(description = "描述", example = "")
    @ExcelProperty("描述")
    private String remark;
}
