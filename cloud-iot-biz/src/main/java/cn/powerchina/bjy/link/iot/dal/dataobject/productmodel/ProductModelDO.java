package cn.powerchina.bjy.link.iot.dal.dataobject.productmodel;

import cn.powerchina.bjy.cloud.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.*;
import lombok.*;

/**
 * 产品物模型 DO
 *
 * <AUTHOR>
 */
@TableName("iot_product_model")
@KeySequence("iot_product_model_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductModelDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 产品编码
     */
    private String productCode;
    /**
     * 物模型标识符
     */
    private String thingIdentity;
    /**
     * 物模型名称
     */
    private String thingName;
    /**
     * 物模型类型，1-属性；2-服务；3-事件；
     */
    private Integer thingType;
    /**
     * 数据类型（integer、decimal、string、bool、array、enum）
     */
    private String datatype;
    /**
     * 读写类型，thing_type为1时必填
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Integer readWriteType;
    /**
     * 事件类型，thing_type为3时必填
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Integer eventType;
    /**
     * 输入参数
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String inputParams;
    /**
     * 输出参数
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String outputParams;
    /**
     * 属性扩展信息
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String extra;
    /**
     * 描述
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String remark;

}