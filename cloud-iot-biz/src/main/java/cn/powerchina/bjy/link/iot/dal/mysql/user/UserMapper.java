package cn.powerchina.bjy.link.iot.dal.mysql.user;


import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.iot.controller.admin.user.vo.UserPageReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.user.UserDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 用户信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface UserMapper extends BaseMapperX<UserDO> {

    default PageResult<UserDO> selectPage(UserPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<UserDO>()
                .likeIfPresent(UserDO::getUsername, reqVO.getUsername())
                .likeIfPresent(UserDO::getName, reqVO.getName())
                .likeIfPresent(UserDO::getMobile, reqVO.getMobile())
                .likeIfPresent(UserDO::getDeptName, reqVO.getDeptName())
                .likeIfPresent(UserDO::getPostName, reqVO.getPostName())
                .eqIfPresent(UserDO::getEmail, reqVO.getEmail())
                .eqIfPresent(UserDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(UserDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(UserDO::getId));
    }

}