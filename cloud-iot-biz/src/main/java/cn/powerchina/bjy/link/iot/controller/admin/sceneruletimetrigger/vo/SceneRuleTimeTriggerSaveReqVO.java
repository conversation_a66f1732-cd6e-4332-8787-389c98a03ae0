package cn.powerchina.bjy.link.iot.controller.admin.sceneruletimetrigger.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDate;
import java.util.*;
import jakarta.validation.constraints.*;

@Schema(description = "管理后台 - 定时触发新增/修改 Request VO")
@Data
public class SceneRuleTimeTriggerSaveReqVO {

    @Schema(description = "主键id",  example = "26614")
    private Long id;

    @Schema(description = "规则ID",  example = "1259")
    private Long ruleId;

    private Long triggerId;

    @Schema(description = "执行时刻")
    private String executionTime;

    @Schema(description = "重复类型:1-每天,2-指定日期,3-指定周期,4-自定义", example = "1")
    private Integer repeatType;

    @Schema(description = "开始日期/指定日期")
    private LocalDate repeatStartDate;

    @Schema(description = "结束日期")
    private LocalDate repeatEndDate;

    @Schema(description = "每周重复的星期几,如:1,2,3,4,5,6,7")
    private String repeatWeekDays;

    @Schema(description = "cron表达式")
    private String cronExpression;

    @Schema(description = "jobId", hidden = true)
    private String jobId;

    @Schema(description = "排序", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "排序不能为空")
    private Integer sort;

}
