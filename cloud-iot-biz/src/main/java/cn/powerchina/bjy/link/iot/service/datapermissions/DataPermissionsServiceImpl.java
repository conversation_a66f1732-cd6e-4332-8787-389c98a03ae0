package cn.powerchina.bjy.link.iot.service.datapermissions;

import cn.powerchina.bjy.cloud.framework.common.exception.enums.GlobalErrorCodeConstants;
import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.collection.CollectionUtils;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.cloud.system.api.permission.PermissionApi;
import cn.powerchina.bjy.link.iot.controller.admin.datapermissions.vo.DataPermissionsPageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.datapermissions.vo.DataPermissionsSaveReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.datapermissions.vo.DataPermissionsTreeVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.datapermissions.DataPermissionsDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.device.DeviceDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.edgegateway.EdgeGatewayDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.product.ProductDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.resourcespace.ResourceSpaceDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.roledatapermissions.RoleDataPermissionsDO;
import cn.powerchina.bjy.link.iot.dal.mysql.datapermissions.DataPermissionsMapper;
import cn.powerchina.bjy.link.iot.dal.mysql.device.DeviceMapper;
import cn.powerchina.bjy.link.iot.dal.mysql.edgegateway.EdgeGatewayMapper;
import cn.powerchina.bjy.link.iot.dal.mysql.product.ProductMapper;
import cn.powerchina.bjy.link.iot.dal.mysql.resourcespace.ResourceSpaceMapper;
import cn.powerchina.bjy.link.iot.dal.mysql.roledatapermissions.RoleDataPermissionsMapper;
import cn.powerchina.bjy.link.iot.enums.ErrorCodeConstants;
import cn.powerchina.bjy.link.iot.enums.RuleStateEnum;
import cn.powerchina.bjy.link.iot.service.scenerule.SceneRuleService;
import cn.powerchina.bjy.link.iot.service.sceneruleaction.SceneRuleActionService;
import cn.powerchina.bjy.link.iot.service.sceneruletrigger.SceneRuleTriggerService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import java.util.stream.Collectors;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.cloud.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

/**
 * 数据权限 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class DataPermissionsServiceImpl implements DataPermissionsService {

    @Resource
    private DataPermissionsMapper dataPermissionsMapper;

    @Resource
    private ResourceSpaceMapper resourceSpaceMapper;

    @Resource
    private ProductMapper productMapper;

    @Resource
    private DeviceMapper deviceMapper;

    @Resource
    private EdgeGatewayMapper edgeGatewayMapper;

    @Resource
    private RoleDataPermissionsMapper roleDataPermissionsMapper;

    @Resource
    private PermissionApi permissionApi;

    @Resource
    private SceneRuleTriggerService sceneRuleTriggerService;
    @Resource
    private SceneRuleActionService sceneRuleActionService;
    @Resource
    private SceneRuleService sceneRuleService;

    @Override
    @Transactional
    public Boolean createDataPermissions(DataPermissionsSaveReqVO createReqVO) {
        //删除角色跟权限关联关系
        roleDataPermissionsMapper.deleteByRoleId(Long.parseLong(createReqVO.getRoleId()));
        //添加角色与权限关联关系信息
        RoleDataPermissionsDO roleDataPermissionsDO = BeanUtils.toBean(createReqVO, RoleDataPermissionsDO.class);
        roleDataPermissionsMapper.insert(roleDataPermissionsDO);

        dataPermissionsMapper.deleteByRoleId(Long.parseLong(createReqVO.getRoleId()));
        if (!CollectionUtils.isAnyEmpty(createReqVO.getDataScopeDeptIds())) {
            List<DataPermissionsDO> list = addDataPermissions(createReqVO);
            if (!CollectionUtils.isAnyEmpty(list)) {
                dataPermissionsMapper.insertBatch(list);
            }

            //场景失效
            CommonResult<Set<Long>> users = permissionApi.getUserRoleIdListByRoleIds(Collections.singletonList(Long.parseLong(createReqVO.getRoleId())));
            if (GlobalErrorCodeConstants.SUCCESS.getCode().equals(users.getCode())) {
                for (Long userId : users.getData()) {
                    //场景失效
                    //查询人员创建的触发条件、限制条件失效
                    List<Long> invalidTriggerList = sceneRuleTriggerService.invalidSceneRuleTrigger(userId, list);
                    //执行动作失效
                    List<Long> invalidActionList = sceneRuleActionService.invalidSceneRuleAction(userId, list);
                    //场景联动失效
                    List<Long> allInvalidRuleList = new ArrayList<>();
                    allInvalidRuleList.addAll(invalidTriggerList);
                    allInvalidRuleList.addAll(invalidActionList);
                    List<Long> invalidRuleList = allInvalidRuleList.stream().distinct().collect(Collectors.toList());
                    sceneRuleService.invalidSceneRule(invalidRuleList, RuleStateEnum.INVALID.getType());

                    //场景生效
                    //查询人员创建的触发条件、限制条件生效
                    List<Long> effectiveTriggerList = sceneRuleTriggerService.effectiveSceneRuleTrigger(userId, list);
                    //执行动作生效
                    List<Long> effectiveActionList = sceneRuleActionService.effectiveSceneRuleAction(userId, list);
                    //场景联动生效
                    List<Long> allEffectiveRuleList = new ArrayList<>();
                    allEffectiveRuleList.addAll(effectiveTriggerList);
                    allEffectiveRuleList.addAll(effectiveActionList);
                    List<Long> effectiveRuleList = allEffectiveRuleList.stream().distinct().collect(Collectors.toList());

                    sceneRuleService.effectiveSceneRule(effectiveRuleList, RuleStateEnum.YES.getType());

                }
            }




        }
        return true;
    }

    @Override
    public Boolean updateDataPermissions(DataPermissionsSaveReqVO updateReqVO) {
        //删除当前用户的权限数据
        dataPermissionsMapper.deleteByRoleId(getLoginUserId());
        return dataPermissionsMapper.insertBatch(addDataPermissions(updateReqVO));
    }

    @Override
    public void deleteDataPermissions(Long id) {
        // 校验存在
        validateDataPermissionsExists(id);
        // 删除
        dataPermissionsMapper.deleteById(id);
    }

    private void validateDataPermissionsExists(Long id) {
        if (dataPermissionsMapper.selectById(id) == null) {
            throw exception(ErrorCodeConstants.DATA_PERMISSIONS_NOT_EXISTS);
        }
    }

//    @Override
//    public DataPermissionsSaveReqVO getDataPermissions() {
//        DataPermissionsSaveReqVO resp=new DataPermissionsSaveReqVO();
//        List<DataPermissionsDO> list=dataPermissionsMapper.selectListByRoleId(getLoginUserId());
//        List<DataPermissionsDO>  doList=dataPermissionsMapper.selectList(getLoginUserId());
//        if(!CollectionUtils.isAnyEmpty(list))
//        {
//            List<DataResourceSaveRespVO> resourceSaveRespVOList=new ArrayList<>();
//            list.stream().forEach(resoure -> {
//                DataResourceSaveRespVO dataResourceSaveRespVO=new DataResourceSaveRespVO();
//                dataResourceSaveRespVO.setResourceSpaceId(resoure.getResourceSpaceId()+"");
//                if(DataPermissionsEnum.PRODUCT.getCode().equals(resoure.getType()))
//                {
//                    List<DataProductSaveRespVO> dataProductSaveRespVOList=new ArrayList<>();
//                    DataProductSaveRespVO dataProductSaveRespVO=new DataProductSaveRespVO();
//                    dataProductSaveRespVO.setProductId(resoure.getProductOrEdgeId());
//                    List<String> deviceIds=new ArrayList<>();
//                    doList.stream().forEach(devices ->{
//                        if(devices.getProductOrEdgeId().equals(resoure.getProductOrEdgeId()))
//                        {
//                            deviceIds.add(devices.getDeviceOrGatewayId());
//                        }
//                    });
//                    dataProductSaveRespVO.setDeviceIds(deviceIds);
//                    dataProductSaveRespVOList.add(dataProductSaveRespVO);
//                    dataResourceSaveRespVO.setProductList(dataProductSaveRespVOList);
//                }
//                if(DataPermissionsEnum.GATEWAY.getCode().equals(resoure.getType()))
//                {
//                    List<DataGatewaySaveRespVO> dataGatewaySaveRespVOList=new ArrayList<>();
//                    DataGatewaySaveRespVO dataGatewaySaveRespVO=new DataGatewaySaveRespVO();
//                    dataGatewaySaveRespVO.setEdgeId(resoure.getProductOrEdgeId());
//                    List<String> edgeIds=new ArrayList<>();
//                    doList.stream().forEach(edges ->{
//                        if(edges.getDeviceOrGatewayId().equals(resoure.getDeviceOrGatewayId()))
//                        {
//                            edgeIds.add(edges.getDeviceOrGatewayId());
//                        }
//                    });
//                    dataGatewaySaveRespVO.setGatewayIds(edgeIds);
//                    dataGatewaySaveRespVOList.add(dataGatewaySaveRespVO);
//                    dataResourceSaveRespVO.setEdgeList(dataGatewaySaveRespVOList);
//                }
//                resourceSaveRespVOList.add(dataResourceSaveRespVO);
//            });
////            resp.setRespVOList(resourceSaveRespVOList);
//            resp.setRoleId(getLoginUserId()+"");
//        }
//
//        return resp;
//    }

    @Override
    public PageResult<DataPermissionsDO> getDataPermissionsPage(DataPermissionsPageReqVO pageReqVO) {
        return dataPermissionsMapper.selectPage(pageReqVO);
    }

    /**
     * level解释  1：资源空间 2：所有产品 3：产品 4：所有设备 5：设备 6：边缘计算 7：所有实例 8：边缘实例)
     * id中数字解释：1资源空间层 2产品层/边缘计算 3设备层级/边缘实例层
     * @return
     */
    @Override
    public List<DataPermissionsTreeVO> getAllList() {

        //查询所有资源空间数据
        List<ResourceSpaceDO> resourceSpaceDOList = resourceSpaceMapper.selectList();
        //查询所有产品数据
        List<ProductDO> productDOList = productMapper.selectList();
        Map<String, Long> productMap = productDOList.stream().collect(Collectors.toMap(ProductDO::getProductCode, ProductDO::getResourceSpaceId, (k1, k2)->k1));
        //查询所有设备数据
        List<DeviceDO> deviceDOList = deviceMapper.selectList(new LambdaQueryWrapperX<DeviceDO>().eq(DeviceDO::getDeviceType, 1));
        //查询所有边缘实例数据
        //List<EdgeGatewayDO> edgeGatewayDOList = edgeGatewayMapper.selectList();
        List<DeviceDO> edgeGatewayDOList = deviceMapper.selectList(new LambdaQueryWrapperX<DeviceDO>().eq(DeviceDO::getDeviceType, 0));
        List<DataPermissionsTreeVO> list = new ArrayList<>();
        if (!CollectionUtils.isAnyEmpty(resourceSpaceDOList)) {
            resourceSpaceDOList.forEach(resoure -> {
                list.add(DataPermissionsTreeVO.builder().id(resoure.getId() + "_1").name(resoure.getSpaceName()).parentId("0").level(1).build());
                list.add(DataPermissionsTreeVO.builder().id(-resoure.getId() + "_2").name("(+)所有产品").parentId(resoure.getId() + "_1").level(2).build());
                productDOList.forEach(product -> {
                    if (resoure.getId().equals(product.getResourceSpaceId())) {
                        list.add(DataPermissionsTreeVO.builder().id(product.getId() + "_2").name(product.getProductName()).parentId(resoure.getId() + "_1").level(3).build());
                        list.add(DataPermissionsTreeVO.builder().id(-product.getId() + "_3").name("(+)所有设备").parentId(product.getId() + "_2").level(4).build());
                        deviceDOList.forEach(device -> {
                            if (product.getProductCode().equals(device.getProductCode())) {
                                list.add(DataPermissionsTreeVO.builder().id(device.getId() + "_3").name(device.getDeviceName()).parentId(product.getId() + "_2").level(5).build());
                            }
                        });
                    }
                });
                Long gateWayId = resoure.getId() + 10;
                list.add(DataPermissionsTreeVO.builder().id(-gateWayId + "_2").name("边缘计算").parentId(resoure.getId() + "_1").level(6).build());
                list.add(DataPermissionsTreeVO.builder().id(-gateWayId + 100 + "_3").name("(+)所有边缘实例").parentId(-gateWayId + "_2").level(7).build());
                edgeGatewayDOList.forEach(gateway -> {
                    if (productMap.get(gateway.getProductCode()) != null && resoure.getId().equals(productMap.get(gateway.getProductCode()))) {
                        list.add(DataPermissionsTreeVO.builder().id(gateway.getId() + "_3").name(gateway.getDeviceName()).parentId(-gateWayId + "_2").level(8).build());
                    }
                });
            });
        }
        return list;
    }

    @Override
    public DataPermissionsSaveReqVO getAllListByRoleId(String roleId) {

        DataPermissionsSaveReqVO dataPermissionsSaveReqVO = new DataPermissionsSaveReqVO();
        List<DataPermissionsTreeVO> dataList = new ArrayList<>();
        RoleDataPermissionsDO roleDataPermissionsDO = roleDataPermissionsMapper.selectAllByRoleId(Long.parseLong(roleId));
        if (roleDataPermissionsDO != null) {
            dataPermissionsSaveReqVO.setRoleId(roleDataPermissionsDO.getRoleId().toString());
            dataPermissionsSaveReqVO.setDataScope(roleDataPermissionsDO.getDataScope());
            List<Integer> levels = new ArrayList<>();
            levels.add(1);
            levels.add(3);
            levels.add(6);
            //List<DataPermissionsDO> list = dataPermissionsMapper.selectAllByRoleId(Long.parseLong(roleId),levels);

            LambdaQueryWrapper<DataPermissionsDO> lambdaWrapper = new LambdaQueryWrapper<>();
            lambdaWrapper.eq(DataPermissionsDO::getRoleId, Long.parseLong(roleId));
            lambdaWrapper.notIn(DataPermissionsDO::getLevel, levels);
            List<DataPermissionsDO> list = dataPermissionsMapper.selectList(lambdaWrapper);

            if (!CollectionUtils.isAnyEmpty(list)) {
                list.forEach(item -> {
                    DataPermissionsTreeVO dataPermissionsTreeVO = new DataPermissionsTreeVO();
                    switch (item.getLevel()) {
                        case 2:
                            dataPermissionsTreeVO.setId(item.getDataId() + "_2");
                            dataPermissionsTreeVO.setParentId(item.getParentId() + "_1");
                            break;
                        case 4:
                        case 5:
                        case 7:
                        case 8:  // level=4/5/7/8 走相同逻辑
                            dataPermissionsTreeVO.setId(item.getDataId() + "_3");
                            dataPermissionsTreeVO.setParentId(item.getParentId() + "_2");
                            break;
                    }
                    dataPermissionsTreeVO.setLevel(item.getLevel());
                    dataPermissionsTreeVO.setName(item.getName());
                    dataList.add(dataPermissionsTreeVO);
                });
            }
            dataPermissionsSaveReqVO.setDataScopeDeptIds(dataList);
        }
        return dataPermissionsSaveReqVO;
    }

    private List<DataPermissionsDO> addDataPermissions(DataPermissionsSaveReqVO reqVO) {
        // 插入
        List<DataPermissionsDO> list = new ArrayList<>();
        reqVO.getDataScopeDeptIds().stream().forEach(item -> {

            DataPermissionsDO dataPermissionsDO = new DataPermissionsDO();
            dataPermissionsDO.setDataId(item.getId().split("_")[0]);
            dataPermissionsDO.setName(item.getName());
            dataPermissionsDO.setRoleId(Long.parseLong(reqVO.getRoleId()));
            dataPermissionsDO.setParentId(item.getParentId().split("_")[0]);
            dataPermissionsDO.setLevel(item.getLevel());
            list.add(dataPermissionsDO);
        });
        return list;
    }
}