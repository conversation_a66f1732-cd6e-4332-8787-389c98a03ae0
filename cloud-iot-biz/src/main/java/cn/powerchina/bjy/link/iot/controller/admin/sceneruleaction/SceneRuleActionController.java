package cn.powerchina.bjy.link.iot.controller.admin.sceneruleaction;


import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.excel.core.util.ExcelUtils;
import cn.powerchina.bjy.link.iot.controller.admin.sceneruleaction.vo.SceneRuleActionPageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.sceneruleaction.vo.SceneRuleActionRespVO;
import cn.powerchina.bjy.link.iot.controller.admin.sceneruleaction.vo.SceneRuleActionSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.sceneruleaction.SceneRuleActionDO;
import cn.powerchina.bjy.link.iot.service.sceneruleaction.SceneRuleActionService;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;
import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;
import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

@Tag(name = "管理后台 - 场景规则执行动作")
@RestController
@RequestMapping("/iot/scene-rule-action")
@Validated
public class SceneRuleActionController {

    @Resource
    private SceneRuleActionService sceneRuleActionService;

    @PostMapping("/create")
    @Operation(summary = "创建场景规则执行动作")
//    @PreAuthorize("@ss.hasPermission('iot:scene-rule-action:create')")
    public CommonResult<Long> createSceneRuleAction(@Valid @RequestBody SceneRuleActionSaveReqVO createReqVO) {
        return success(sceneRuleActionService.createSceneRuleAction(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新场景规则执行动作")
//    @PreAuthorize("@ss.hasPermission('iot:scene-rule-action:update')")
    public CommonResult<Boolean> updateSceneRuleAction(@Valid @RequestBody SceneRuleActionSaveReqVO updateReqVO) {
        sceneRuleActionService.updateSceneRuleAction(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除场景规则执行动作")
    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('iot:scene-rule-action:delete')")
    public CommonResult<Boolean> deleteSceneRuleAction(@RequestParam("id") Long id) {
        sceneRuleActionService.deleteSceneRuleAction(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得场景规则执行动作")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('iot:scene-rule-action:query')")
    public CommonResult<SceneRuleActionRespVO> getSceneRuleAction(@RequestParam("id") Long id) {
        SceneRuleActionDO sceneRuleAction = sceneRuleActionService.getSceneRuleAction(id);
        return success(BeanUtils.toBean(sceneRuleAction, SceneRuleActionRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得场景规则执行动作分页")
//    @PreAuthorize("@ss.hasPermission('iot:scene-rule-action:query')")
    public CommonResult<PageResult<SceneRuleActionRespVO>> getSceneRuleActionPage(@Valid SceneRuleActionPageReqVO pageReqVO) {
        PageResult<SceneRuleActionDO> pageResult = sceneRuleActionService.getSceneRuleActionPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, SceneRuleActionRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出场景规则执行动作 Excel")
//    @PreAuthorize("@ss.hasPermission('iot:scene-rule-action:export')")
//    @OperateLog(type = EXPORT)
    public void exportSceneRuleActionExcel(@Valid SceneRuleActionPageReqVO pageReqVO,
                                           HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<SceneRuleActionDO> list = sceneRuleActionService.getSceneRuleActionPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "场景规则执行动作.xls", "数据", SceneRuleActionRespVO.class,
                BeanUtils.toBean(list, SceneRuleActionRespVO.class));
    }

}
