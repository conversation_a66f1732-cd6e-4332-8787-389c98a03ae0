package cn.powerchina.bjy.link.iot.listener;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.powerchina.bjy.cloud.framework.common.util.json.JsonUtils;
import cn.powerchina.bjy.link.iot.dal.dataobject.device.DeviceDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.deviceeventlog.DeviceEventLogDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.productmodel.ProductModelDO;
import cn.powerchina.bjy.link.iot.dto.up.EdgeCheckConfigStatus;
import cn.powerchina.bjy.link.iot.dto.up.EdgeReadPropertyValue;
import cn.powerchina.bjy.link.iot.dto.up.EdgeReportEventValue;
import cn.powerchina.bjy.link.iot.enums.IotTopicConstant;
import cn.powerchina.bjy.link.iot.enums.StatisticImageTypeEnum;
import cn.powerchina.bjy.link.iot.service.device.DeviceService;
import cn.powerchina.bjy.link.iot.service.deviceeventlog.DeviceEventLogService;
import cn.powerchina.bjy.link.iot.service.messagestatisticday.MessageStatisticDayService;
import cn.powerchina.bjy.link.iot.service.productmodel.ProductModelService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.annotation.RocketMQMessageListener;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.apache.rocketmq.client.core.RocketMQListener;
import org.springframework.stereotype.Component;

import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;

@Slf4j
@Component
@RocketMQMessageListener(
        topic = IotTopicConstant.TOPIC_DEVICE_EVENT,
        consumerGroup = IotTopicConstant.GROUP_DEVICE_EVENT,
        requestTimeout = 10, consumptionThreadCount = 10
)
public class EventReceiver implements RocketMQListener {

    @Resource
    private DeviceEventLogService eventLogService;
    @Resource
    private DeviceService deviceService;
    @Resource
    private ProductModelService productModelService;
    @Resource
    private MessageStatisticDayService messageStatisticDayService;

    @Override
    public ConsumeResult consume(MessageView messageView) {
        if (ObjectUtil.isNull(messageView)) {
            log.error("MessageView is null, skip consumption");
            return ConsumeResult.SUCCESS;
        }

        try {
            // 解析消息体
            EdgeReportEventValue edgeReportEventValue = parseMessageBody(messageView);
            if (edgeReportEventValue == null) {
                log.error("MessageView is null, skip consumption");
                return ConsumeResult.SUCCESS;
            }

            // 时间转换
            LocalDateTime localDateTime = LocalDateTime.ofInstant(
                    Instant.ofEpochMilli(edgeReportEventValue.getCurrentTime()),
                    ZoneId.systemDefault()
            );

            // 初始化日志列表
            List<DeviceEventLogDO> deviceEventLogList = new ArrayList<>();

            // 处理事件值列表
            if (CollectionUtil.isNotEmpty(edgeReportEventValue.getDeviceEventValueList())) {
                edgeReportEventValue.getDeviceEventValueList().forEach(deviceEventValue -> processDeviceEvent(deviceEventValue, localDateTime, deviceEventLogList));
            }

            // 批量保存日志
            if (!deviceEventLogList.isEmpty()) {
                eventLogService.createDeviceEventLogSaveBatch(deviceEventLogList);
            }

            // 统计消息
            messageStatisticDayService.insertMessageStatisticDay(new Date(), StatisticImageTypeEnum.MESSAGE.getType(), 1L);

            return ConsumeResult.SUCCESS;

        } catch (Exception e) {
            log.error("事件上报属性解析异常, messageId: {}, error: {}", messageView.getMessageId(), e.getMessage(), e);
        }
        return ConsumeResult.SUCCESS;
    }

    private void processDeviceEvent(
            EdgeReportEventValue.EdgeDeviceReportEventValue deviceEventValue,
            LocalDateTime localDateTime,
            List<DeviceEventLogDO> deviceEventLogList) {

        // 查询设备
        DeviceDO device = queryDevice(deviceEventValue);
        if (device == null) {
            return;
        }

        // 查询产品模型
        ProductModelDO productModel = queryProductModel(device, deviceEventValue);
        if (productModel == null) {
            return;
        }

        // 构建事件日志
        buildDeviceEventLog(device, productModel, deviceEventValue, localDateTime, deviceEventLogList);
    }

    private DeviceDO queryDevice(EdgeReportEventValue.EdgeDeviceReportEventValue deviceEventValue) {
        DeviceDO device = deviceService.getDeviceBySlaveAndEdgeCode(
                deviceEventValue.getSlaveId(),
                deviceEventValue.getEdgeCode()
        );

        if (device != null && deviceEventValue.getSelfType() == 0) { // 网关子设备处理
            device = deviceService.getDeviceByParentCodeAndMcuChannel(
                    device.getDeviceCode(),
                    deviceEventValue.getMcuChannel()
            );
        }

        if (ObjectUtil.isNull(device)) {
            log.warn("设备不存在: slaveId={}, edgeCode={}",
                    deviceEventValue.getSlaveId(),
                    deviceEventValue.getEdgeCode()
            );
        }
        return device;
    }

    private ProductModelDO queryProductModel(
            DeviceDO device,
            EdgeReportEventValue.EdgeDeviceReportEventValue deviceEventValue) {

        ProductModelDO productModel = productModelService.getProductModel(
                device.getProductCode(),
                deviceEventValue.getThingIdentity()
        );

        if (ObjectUtil.isNull(productModel)) {
            log.warn("产品模型不存在: productCode={}, thingIdentity={}",
                    device.getProductCode(),
                    deviceEventValue.getThingIdentity()
            );
        }
        return productModel;
    }

    private void buildDeviceEventLog(
            DeviceDO device,
            ProductModelDO productModel,
            EdgeReportEventValue.EdgeDeviceReportEventValue deviceEventValue,
            LocalDateTime localDateTime,
            List<DeviceEventLogDO> deviceEventLogList) {

        DeviceEventLogDO deviceEventLogDO = new DeviceEventLogDO(
                null,
                device.getDeviceCode(),
                device.getDeviceName(),
                productModel.getThingIdentity(),
                productModel.getThingName(),
                productModel.getEventType(),
                null,
                null,
                productModel.getRemark()
        );

        if (JsonUtils.isJson(productModel.getOutputParams())) {
            JSONArray modelArray = JSON.parseArray(productModel.getOutputParams());
            JSONObject eventValueJson = JSON.parseObject(deviceEventValue.getThingValue());

            Map<String, Object> valueMap = new HashMap<>();
            modelArray.forEach(model -> {
                JSONObject modelItem = (JSONObject) model;
                String thingIdentity = modelItem.getString("thingIdentity");
                String thingName = modelItem.getString("thingName");
                valueMap.put(thingName, eventValueJson.getString(thingIdentity));
            });

            deviceEventLogDO.setThingValue(JsonUtils.toJsonString(valueMap));
            deviceEventLogDO.setCreateTime(localDateTime);
            deviceEventLogList.add(deviceEventLogDO);
        }
    }

    /**
     * 解析消息体为实体类
     */
    private EdgeReportEventValue parseMessageBody(MessageView messageView) {
        try {
            ByteBuffer body = messageView.getBody();
            if (body.remaining() == 0) {
                log.error("Message body is empty, messageId: {}", messageView.getMessageId());
                return null;
            }

            String message = StandardCharsets.UTF_8.decode(body).toString();
            return JSON.parseObject(message, EdgeReportEventValue.class);

        } catch (JSONException e) {
            log.error("JSON解析失败, messageId: {}, content: {}",
                    messageView.getMessageId(),
                    messageView.getBody(),
                    e
            );
            return null;
        }
    }
}