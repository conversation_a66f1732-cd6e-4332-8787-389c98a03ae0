package cn.powerchina.bjy.link.iot.controller.admin.drive.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

@Schema(description = "管理后台 - 驱动新增/修改 Request VO")
@Data
public class DriveDeployReqVO {


    @Schema(description = "驱动编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "驱动编码不能为空")
    private String driverCode;

    @Schema(description = "边缘网关code", example = "123")
    private String edgeCode;

}