package cn.powerchina.bjy.link.iot.dal.mysql.product;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.iot.controller.admin.product.vo.ProductPageReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.modelTemplate.ModelTemplateDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.product.ProductDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.resourcespace.ResourceSpaceDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 产品 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ProductMapper extends BaseMapperX<ProductDO> {

    default PageResult<ProductDO> selectPage(ProductPageReqVO reqVO) {

        PageResult<ProductDO> result=null;
        if(CollectionUtils.isEmpty(reqVO.getProductCodes()))
        {
            result=selectPage(reqVO, new LambdaQueryWrapperX<ProductDO>()
                    .likeIfPresent(ProductDO::getProductName, reqVO.getProductName())
                    .likeIfPresent(ProductDO::getProductCode, reqVO.getProductCode())
                    .likeIfPresent(ProductDO::getProductModel, reqVO.getProductModel())
                    .likeIfPresent(ProductDO::getFirmName, reqVO.getFirmName())
                    .eqIfPresent(ProductDO::getDescription, reqVO.getDescription())
                    .eqIfPresent(ProductDO::getNodeType, reqVO.getNodeType())
                    .eqIfPresent(ProductDO::getProtocolCode, reqVO.getProtocolCode())
                    .eqIfPresent(ProductDO::getNetworkMethod, reqVO.getNetworkMethod())
                    .eqIfPresent(ProductDO::getDataFormat, reqVO.getDataFormat())
                    .eqIfPresent(ProductDO::getProductState, reqVO.getProductState())
                    .eqIfPresent(ProductDO::getProductSecret, reqVO.getProductSecret())
                    .betweenIfPresent(ProductDO::getCreateTime, reqVO.getCreateTime())
                    .eqIfPresent(ProductDO::getResourceSpaceId, reqVO.getResourceSpaceId())
                    .orderByDesc(ProductDO::getId));
        }else {
            result=selectPage(reqVO, new LambdaQueryWrapperX<ProductDO>()
                    .likeIfPresent(ProductDO::getProductName, reqVO.getProductName())
                    .likeIfPresent(ProductDO::getProductCode, reqVO.getProductCode())
                    .likeIfPresent(ProductDO::getProductModel, reqVO.getProductModel())
                    .likeIfPresent(ProductDO::getFirmName, reqVO.getFirmName())
                    .in(ProductDO::getId,reqVO.getProductCodes())
                    .eqIfPresent(ProductDO::getDescription, reqVO.getDescription())
                    .eqIfPresent(ProductDO::getNodeType, reqVO.getNodeType())
                    .eqIfPresent(ProductDO::getProtocolCode, reqVO.getProtocolCode())
                    .eqIfPresent(ProductDO::getNetworkMethod, reqVO.getNetworkMethod())
                    .eqIfPresent(ProductDO::getDataFormat, reqVO.getDataFormat())
                    .eqIfPresent(ProductDO::getProductState, reqVO.getProductState())
                    .eqIfPresent(ProductDO::getProductSecret, reqVO.getProductSecret())
                    .betweenIfPresent(ProductDO::getCreateTime, reqVO.getCreateTime())
                    .eqIfPresent(ProductDO::getResourceSpaceId, reqVO.getResourceSpaceId())
                    .orderByDesc(ProductDO::getId));
        }
        return result;
    }

    @Select("select * from iot_product where product_code = #{productCode} and deleted = 0")
    ProductDO selectByCode(@Param("productCode") String productCode);
    int updateInfo(ProductDO productDO);

    default Long selectCountByTemplateId(Long templateId) {
        return selectCount(ProductDO::getTemplateId, templateId);
    }

}