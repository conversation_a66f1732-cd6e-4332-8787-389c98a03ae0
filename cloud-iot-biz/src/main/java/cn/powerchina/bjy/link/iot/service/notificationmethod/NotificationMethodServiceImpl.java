package cn.powerchina.bjy.link.iot.service.notificationmethod;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.iot.controller.admin.notificationmethod.vo.NotificationMethodPageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.notificationmethod.vo.NotificationMethodSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.notificationmethod.NotificationMethodDO;
import cn.powerchina.bjy.link.iot.dal.mysql.notificationmethod.NotificationMethodMapper;
import cn.powerchina.bjy.link.iot.util.SnowFlakeUtil;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.link.iot.enums.ErrorCodeConstants.NOTIFICATION_METHOD_NOT_EXISTS;

/**
 * 通知方式 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class NotificationMethodServiceImpl implements NotificationMethodService {

    @Resource
    private NotificationMethodMapper notificationMethodMapper;


    @Override
    public void createAndUpdateNotificationMethod(List<NotificationMethodSaveReqVO> saveOrUpdateList, Long alarmTemplateId) {
        List<Long> oldIdList = new ArrayList<>();
        List<NotificationMethodDO> oldEntityList = getNotificationMethodByAlarmTemplateId(alarmTemplateId);
        if (!CollectionUtils.isEmpty(oldEntityList)) {
            oldIdList = oldEntityList.stream().map(NotificationMethodDO::getId).collect(Collectors.toList());
        }

        for (NotificationMethodSaveReqVO notificationVO : saveOrUpdateList) {
            notificationVO.setAlarmTemplateId(alarmTemplateId);
            if (Objects.isNull(notificationVO.getId())) {
                createNotificationMethod(notificationVO);
            } else {
                updateNotificationMethod(notificationVO);
                oldIdList.remove(notificationVO.getId());
            }
        }
        //删除
        if (!CollectionUtils.isEmpty(oldIdList)) {
            notificationMethodMapper.deleteBatchIds(oldIdList);
        }
    }

    @Override
    public List<NotificationMethodDO> getNotificationMethodByAlarmTemplateId(Long alarmTemplateId) {
        return notificationMethodMapper.selectList(new LambdaQueryWrapperX<NotificationMethodDO>().
                eq(NotificationMethodDO::getAlarmTemplateId, alarmTemplateId)
                .orderByAsc(NotificationMethodDO::getCreateTime));
    }

    @Override
    public Long createNotificationMethod(NotificationMethodSaveReqVO createReqVO) {
        // 插入
        NotificationMethodDO notificationMethod = BeanUtils.toBean(createReqVO, NotificationMethodDO.class);
        notificationMethodMapper.insert(notificationMethod);
        // 返回
        return notificationMethod.getId();
    }

    @Override
    public void updateNotificationMethod(NotificationMethodSaveReqVO updateReqVO) {
        // 校验存在
        validateNotificationMethodExists(updateReqVO.getId());
        // 更新
        NotificationMethodDO updateObj = BeanUtils.toBean(updateReqVO, NotificationMethodDO.class);
        notificationMethodMapper.updateById(updateObj);
    }

    @Override
    public void deleteNotificationMethod(Long id) {
        // 校验存在
        validateNotificationMethodExists(id);
        // 删除
        notificationMethodMapper.deleteById(id);
    }

    private void validateNotificationMethodExists(Long id) {
        if (notificationMethodMapper.selectById(id) == null) {
            throw exception(NOTIFICATION_METHOD_NOT_EXISTS);
        }
    }

    @Override
    public NotificationMethodDO getNotificationMethod(Long id) {
        return notificationMethodMapper.selectById(id);
    }

    @Override
    public PageResult<NotificationMethodDO> getNotificationMethodPage(NotificationMethodPageReqVO pageReqVO) {
        return notificationMethodMapper.selectPage(pageReqVO);
    }

}