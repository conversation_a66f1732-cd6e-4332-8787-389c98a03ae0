package cn.powerchina.bjy.link.iot.service.productmodel;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.link.iot.controller.admin.productmodel.vo.ProductModelPageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.productmodel.vo.ProductModelSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.productmodel.ProductModelDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 产品物模型 Service 接口
 *
 * <AUTHOR>
 */
public interface ProductModelService {

    /**
     * 创建产品物模型
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createProductModel(@Valid ProductModelSaveReqVO createReqVO);

    /**
     * 更新产品物模型
     *
     * @param updateReqVO 更新信息
     */
    void updateProductModel(@Valid ProductModelSaveReqVO updateReqVO);

    /**
     * 删除产品物模型
     *
     * @param id 编号
     */
    void deleteProductModel(Long id);

    /**
     * 获得产品物模型
     *
     * @param id 编号
     * @return 产品物模型
     */
    ProductModelDO getProductModel(Long id);

    /**
     * 根据产品code和标识符获取物模型
     *
     * @param productCode 产品编码
     * @param identify    物模型标识符
     * @return 物模型
     */
    ProductModelDO getProductModel(String productCode, String identify);

    /**
     * 获得产品物模型分页
     *
     * @param pageReqVO 分页查询
     * @return 产品物模型分页
     */
    PageResult<ProductModelDO> getProductModelPage(ProductModelPageReqVO pageReqVO);

    /**
     * 获取产品的服务物模型
     *
     * @param productCode
     * @param thingType
     * @param pageParam
     * @return
     */
    PageResult<ProductModelDO> getProductModelTypePage(String productCode, Integer thingType, PageParam pageParam);

    /**
     * 获取产品的物模型
     *
     * @param productCode 产品code
     * @param thingName   物模型名称
     * @param pageParam   分页参数
     * @param readOrWrite 是否只读/只写
     * @param thingType   物模型类型
     * @return
     */
    PageResult<ProductModelDO> getProductModelPropertyPage(String productCode, String thingName, PageParam pageParam, Integer[] readOrWrite, int thingType);

    /**
     * 根据产品code和物模型类型获取产品的物模型
     *
     * @param productCode
     * @param thingType
     * @return
     */
    List<ProductModelDO> getProductModelByProductCodeAndThingType(String productCode, Integer thingType);

//    List<EventTypeVO> getEventTypesByProduct(String productCode);
//    List<EventNameVO> getEventNamesByProductAndType(String productCode, Integer eventType);
}