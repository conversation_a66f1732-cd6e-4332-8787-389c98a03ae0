package cn.powerchina.bjy.link.iot.dto.up;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 设备事件上报
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EdgeReportEventValue implements Serializable {

    /**
     * 采集时刻时间
     */
    private Long currentTime;

    /**
     * 设备事件信息
     */
    private List<EdgeDeviceReportEventValue> deviceEventValueList;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class EdgeDeviceReportEventValue {

        /**
         * 是否是采集仪，1：是，0：否
         */
        private Integer selfType;

        /**
         * 物模型标识符
         */
        private String thingIdentity;

        /**
         * 网关实例编码
         */
        private String edgeCode;

        /**
         * 从站号
         */
        private String slaveId;

        /**
         * MCU通道编码
         */
        private String mcuChannel;

        /**
         * 物模型采集值
         */
        private String thingValue;

        /**
         * 事件类型，1：信息，2：告警，3：故障
         */
        private Integer eventType;

        /**
         * 边缘网关应用名称
         */
        private String service;
    }
}