package cn.powerchina.bjy.link.iot.controller.admin.product.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(description = "管理后台 - 产品设备限额更新 Request VO")
@Data
public class ProductDeviceLimitUpdateReqVO {

    @Schema(description = "主键，更新时必填")
    private Long id;

    @Schema(description = "设备限额")
    private Long deviceLimit;

}