package cn.powerchina.bjy.link.iot.controller.admin.modelTemplateDetails.vo;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 物模板明细分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ModelTemplateDetailsPageReqVO extends PageParam {


    @Schema(description = "物模板ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long templateId;

    @Schema(description = "物模板标识符")
    private String templateIdentity;

    @Schema(description = "物模板类型")
    private Integer templateType;
    /**
     * 物模板明细名称
     */
    @Schema(description = "物模板明细名称")
    private String templateDetailsName;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;
}
