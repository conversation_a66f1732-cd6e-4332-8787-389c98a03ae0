package cn.powerchina.bjy.link.iot.controller.admin.device;

import cn.hutool.core.collection.CollectionUtil;
import cn.powerchina.bjy.cloud.framework.common.exception.ErrorCode;
import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.link.iot.controller.admin.device.vo.DeviceAndProductVO;
import cn.powerchina.bjy.link.iot.controller.admin.device.vo.DevicePageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.device.vo.DeviceRespVO;
import cn.powerchina.bjy.link.iot.controller.admin.device.vo.DeviceSaveReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.product.bo.ProductBO;
import cn.powerchina.bjy.link.iot.dal.dataobject.device.DeviceDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.product.ProductDO;
import cn.powerchina.bjy.link.iot.dal.mysql.product.ProductMapper;
import cn.powerchina.bjy.link.iot.enums.SceneTypeEnum;
import cn.powerchina.bjy.link.iot.service.device.DeviceService;
import cn.powerchina.bjy.link.iot.service.product.ProductService;
import cn.powerchina.bjy.link.iot.util.CodeGenerator;
import com.alibaba.cloud.commons.lang.StringUtils;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.core.io.ClassPathResource;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;


@Tag(name = "管理后台 - 设备")
@RestController
@RequestMapping("/iot/device")
@Validated
public class DeviceController {

    @Resource
    private DeviceService deviceService;
    @Resource
    private ProductService productService;
    @Resource
    private ProductMapper productMapper;
    @Resource
    private ThreadPoolTaskExecutor iotThreadPoolTaskExecutor;

    @PostMapping("/create")
    @Operation(summary = "创建设备")
//    @PreAuthorize("@ss.hasPermission('iot:device:create')")
    public CommonResult<Long> createDevice(@Valid @RequestBody DeviceSaveReqVO createReqVO) {
        return success(deviceService.createDevice(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新设备")
//    @PreAuthorize("@ss.hasPermission('iot:device:update')")
    public CommonResult<Boolean> updateDevice(@Valid @RequestBody DeviceSaveReqVO updateReqVO) {
        deviceService.updateDevice(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除设备")
    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('iot:device:delete')")
    public CommonResult<Boolean> deleteDevice(@RequestParam("id") Long id) {
        deviceService.deleteDevice(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "根据主键id获得设备")
    @Parameter(name = "id", description = "设备主键id", required = true)
//    @PreAuthorize("@ss.hasPermission('iot:device:query')")
    public CommonResult<DeviceRespVO> getDevice(@RequestParam("id") Long id) {
        DeviceDO device = deviceService.getDevice(id);
        DeviceRespVO deviceRespVO = BeanUtils.toBean(device, DeviceRespVO.class);
        if (Objects.nonNull(device)) {
            ProductBO product = productService.getProductBOByCode(device.getProductCode());
            if (Objects.nonNull(product)) {
                deviceRespVO.setFirmName(product.getFirmName());//厂商
                deviceRespVO.setProductName(product.getProductName());//产品名称
                deviceRespVO.setProductModel(product.getProductModel());//产品型号
                deviceRespVO.setResourceSpaceId(product.getResourceSpaceId());//资源空间id
                deviceRespVO.setSpaceName(product.getSpaceName());//资源空间名称
            }
        }
        return success(deviceRespVO);
    }

    @GetMapping("/get/code")
    @Operation(summary = "根据设备code获得设备")
    @Parameter(name = "deviceCode", description = "设备编码", required = true)
//    @PreAuthorize("@ss.hasPermission('iot:device:query')")
    public CommonResult<DeviceRespVO> getDeviceByDeviceCode(@RequestParam("deviceCode") String deviceCode) {
        DeviceDO device = deviceService.getDeviceByCode(deviceCode);
        DeviceRespVO deviceRespVO = BeanUtils.toBean(device, DeviceRespVO.class);
        if (Objects.nonNull(device)) {
            ProductBO product = productService.getProductBOByCode(device.getProductCode());
            if (Objects.nonNull(product)) {
                deviceRespVO.setFirmName(product.getFirmName());//厂商
                deviceRespVO.setProductName(product.getProductName());//产品名称
                deviceRespVO.setProductModel(product.getProductModel());//产品型号
                deviceRespVO.setResourceSpaceId(product.getResourceSpaceId());//资源空间id
                deviceRespVO.setSpaceName(product.getSpaceName());//资源空间名称
                DeviceDO parentDevice = deviceService.getDeviceByCode(device.getParentCode());
                if (parentDevice != null) {
                    deviceRespVO.setParentName(parentDevice.getDeviceName());
                }
            }
            DeviceDO edgeDevice = deviceService.getDeviceByCode(device.getEdgeCode());
            if (edgeDevice != null) {
                deviceRespVO.setEdgeName(edgeDevice.getDeviceName());
            }
        }
        return success(deviceRespVO);
    }

    @GetMapping("/page")
    @Operation(summary = "获得设备分页")
//    @PreAuthorize("@ss.hasPermission('iot:device:query')")
    public CommonResult<PageResult<DeviceRespVO>> getDevicePage(@Valid DevicePageReqVO pageReqVO) {
        PageResult<DeviceAndProductVO> pageResult = deviceService.getDevicePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, DeviceRespVO.class));
    }

    @GetMapping("/acount")
    @Operation(summary = "获得设备各状态数量")
//    @PreAuthorize("@ss.hasPermission('iot:device:query')")
    public CommonResult<Map<String, Object>> getDeviceCount(@Valid DevicePageReqVO pageReqVO) {
        Map<String, Object> map = deviceService.getDeviceCountByISOnline(pageReqVO);
        return success(map);
    }

    @GetMapping("/getGroupDevicePage")
    @Operation(summary = "获得分组的设备分页")
//    @PreAuthorize("@ss.hasPermission('iot:device:getGroupDevicePage')")
    public CommonResult<PageResult<DeviceRespVO>> getGroupDevicePage(@Valid DevicePageReqVO pageReqVO) {
        PageResult<DeviceAndProductVO> pageResult = deviceService.getGroupDevicePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, DeviceRespVO.class));
    }

    @GetMapping("/getGatewayDevicePage")
    @Operation(summary = "获得边缘网关下的设备分页")
//    @PreAuthorize("@ss.hasPermission('iot:device:query')")
    public CommonResult<PageResult<DeviceRespVO>> getGatewayDevicePage(@Valid DevicePageReqVO pageReqVO) {
        PageResult<DeviceAndProductVO> pageResult = deviceService.getGatewayDevicePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, DeviceRespVO.class));
    }

    @PutMapping("/replaceDevice")
    @Operation(summary = "设备更换")
//    @PreAuthorize("@ss.hasPermission('iot:device:update')")
    public CommonResult<Boolean> replaceDevice(@Valid @RequestBody DeviceSaveReqVO updateReqVO) {
        deviceService.replaceDevice(updateReqVO);
        return success(true);
    }

    @GetMapping("/addChildDevice")
    @Operation(summary = "新增设备拓扑/修改子设备网关")
//    @PreAuthorize("@ss.hasPermission('iot:device:query')")
    public CommonResult<Boolean> addChildDevice(@RequestParam("deviceCode") String deviceCode, @RequestParam("deviceCodes") List<String> deviceCodes) {
        deviceService.addChildDevice(deviceCode, deviceCodes);
        return success(true);
    }

    @GetMapping("/delChildDevice")
    @Operation(summary = "删除设备拓扑")
//    @PreAuthorize("@ss.hasPermission('iot:device:query')")
    public CommonResult<Boolean> addChildDevice(@RequestParam("deviceCodes") List<String> deviceCodes) {
        deviceService.delChildDevice(deviceCodes);
        return success(true);
    }


    @PostMapping("/batchRegist")
    @Operation(summary = "设备批量注册")
    public CommonResult<Boolean> batchRegist(@Valid @RequestParam("file") MultipartFile file, @RequestParam(name = "productCode") String productCode) {
        XSSFSheet sheetAt;
        try {
            XSSFWorkbook sheets = new XSSFWorkbook(file.getInputStream());
            sheetAt = sheets.getSheetAt(0);
        } catch (Exception e) {
            throw exception(new ErrorCode(500, "导入失败!"));
        }
        assert sheetAt != null;
        int rowNum = sheetAt.getLastRowNum();
        List<DeviceDO> deviceList = new ArrayList<>();
        ProductDO productDO = productMapper.selectByCode(productCode);

        for (int i = 1; i <= rowNum; i++) {
            XSSFRow hssfRow = sheetAt.getRow(i);
            DeviceDO deviceDO = new DeviceDO();
            deviceDO.setProductCode(productCode);
            deviceDO.setDeviceCode(CodeGenerator.createCode(SceneTypeEnum.DEVICE.getPrefix()));
            deviceDO.setDeviceSecret(UUID.randomUUID().toString().replaceAll("-", ""));
            deviceDO.setNodeType(productDO.getNodeType());

            XSSFCell cell0 = hssfRow.getCell(0);
            if (cell0 != null && cell0.getCellType() == CellType.STRING) {
                deviceDO.setDeviceSerial(cell0.getStringCellValue());
            } else {
                throw exception(new ErrorCode(500, "第" + i + "行，设备唯一标识导入异常!"));
            }
            XSSFCell cell1 = hssfRow.getCell(1);
            if (cell1 != null && cell1.getCellType() == CellType.STRING) {
                deviceDO.setDeviceName(cell1.getStringCellValue());
            } else {
                throw exception(new ErrorCode(500, "第" + i + "行，设备名称导入异常!"));
            }
            XSSFCell cell2 = hssfRow.getCell(2);
            deviceDO.setRemark(cell2.getStringCellValue());
            deviceList.add(deviceDO);
        }
        if (CollectionUtil.isNotEmpty(deviceList)) {
            deviceService.deviceBatchRegist(productDO, deviceList);
        }
        return success(true);
    }

    @GetMapping("/export")
    @Operation(summary = "批量注册模板下载")
    public void exportExcel(HttpServletResponse response) {
        try {
            response.setContentType("application/octet-stream");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Cache-Control", "no-store, no-cache");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("设备批量注册模板.xlsx", StandardCharsets.UTF_8));
            ClassPathResource resource = new ClassPathResource("template.excel/deviceRegist-template.xlsx");
            ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).withTemplate(resource.getInputStream()).build();
            excelWriter.finish();
        } catch (Exception e) {
            throw exception(new ErrorCode(500, "模板下载失败!"));
        }
    }
}