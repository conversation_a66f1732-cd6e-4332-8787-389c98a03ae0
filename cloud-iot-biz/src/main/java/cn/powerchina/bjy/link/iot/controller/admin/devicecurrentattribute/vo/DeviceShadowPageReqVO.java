package cn.powerchina.bjy.link.iot.controller.admin.devicecurrentattribute.vo;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


@Schema(description = "管理后台 - 设备上报的最新属性分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DeviceShadowPageReqVO extends PageParam {

    @Schema(description = "产品code")
    private String productCode;

    @Schema(description = "设备code")
    private String deviceCode;

    @Schema(description = "传感器模型code")
    private String modelCode;

    @Schema(description = "属性标识")
    private String thingIdentity;

    @Schema(description = "属性值")
    private String thingValue;

    @Schema(description = "类型（0-属性，1-事件，2-状态变更）")
    private Integer shadowType;

    @Schema(description = "上报时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] reportTime;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}