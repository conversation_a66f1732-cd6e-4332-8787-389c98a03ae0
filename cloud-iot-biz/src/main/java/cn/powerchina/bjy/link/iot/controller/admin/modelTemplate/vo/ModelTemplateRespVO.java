package cn.powerchina.bjy.link.iot.controller.admin.modelTemplate.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 物模板 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ModelTemplateRespVO {

    @Schema(description = "主键", example = "3695")
    private Long id;

    @Schema(description = "物模板ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("物模板ID")
    private Long categorizeId;

    @Schema(description = "物模板一级分类ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("物模板一级分类ID")
    private Long categorizeOneId;

    @Schema(description = "物模板二级分类ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("物模板二级分类ID")
    private Long categorizeTwoId;

    @Schema(description = "物模板名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("物模板名称")
    private String templateName;

    @Schema(description = "引用产品数", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("引用产品数")
    private Integer productsNumber;

    @Schema(description = "描述", example = "")
    @ExcelProperty("描述")
    private String remark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime updateTime;

    /**
     * 物模板分类名称
     */
    private String categorizeName;
}
