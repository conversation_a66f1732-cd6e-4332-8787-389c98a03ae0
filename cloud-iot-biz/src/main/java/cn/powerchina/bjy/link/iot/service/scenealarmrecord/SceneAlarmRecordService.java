package cn.powerchina.bjy.link.iot.service.scenealarmrecord;

import java.util.*;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.link.iot.controller.admin.scenealarmrecord.vo.SceneAlarmRecordPageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.scenealarmrecord.vo.SceneAlarmRecordReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.scenealarmrecord.vo.SceneAlarmRecordRespVO;
import cn.powerchina.bjy.link.iot.controller.admin.scenealarmrecord.vo.SceneAlarmRecordSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.scenealarmrecord.SceneAlarmRecordDO;
import jakarta.validation.*;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 告警记录 Service 接口
 *
 * <AUTHOR>
 */
public interface SceneAlarmRecordService {

    /**
     * 创建告警记录
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createSceneAlarmRecord(@Valid SceneAlarmRecordSaveReqVO createReqVO);

    /**
     * 更新告警记录
     *
     * @param updateReqVO 更新信息
     */
    void updateSceneAlarmRecord(@Valid SceneAlarmRecordSaveReqVO updateReqVO);

    /**
     * 删除告警记录
     *
     * @param id 编号
     */
    void deleteSceneAlarmRecord(Long id);

    /**
     * 获得告警记录
     *
     * @param id 编号
     * @return 告警记录
     */
    SceneAlarmRecordDO getSceneAlarmRecord(Long id);

    /**
     * 获得告警记录详情
     *
     * @param alarmId 告警ID
     * @param alarmDetailId 告警详情ID
     * @return 告警记录详情
     */
    SceneAlarmRecordRespVO getAlarmRecordDetail(Long alarmId, Long alarmDetailId);

    /**
     * 获得告警记录分页
     *
     * @param pageReqVO 分页查询
     * @return 告警记录分页
     */
    PageResult<SceneAlarmRecordRespVO> getSceneAlarmRecordPage(SceneAlarmRecordPageReqVO pageReqVO);

    /**
     * 获得告警记录
     *
     * @param reqVO 编号
     * @return 告警记录
     */
    List<SceneAlarmRecordDO> getSceneAlarmRecordList(SceneAlarmRecordReqVO reqVO);

    /**
     * 处理告警记录
     *
     * @param id 告警记录ID processRecord 处理记录 processUserId 处理人ID processUserName 处理人名称
     * @return 是否处理成功
     */
    boolean processAlarmRecord(Long id, String processRecord, Long userID, String userName);
    /**
     * 恢复告警记录
     *
     * @param id 告警记录ID recoveryUserId 恢复人ID recoveryUserName 恢复人名称
     * @return 是否恢复成功
     */
    boolean recoverAlarmRecord(Long id, Long userId, String userName);

}
