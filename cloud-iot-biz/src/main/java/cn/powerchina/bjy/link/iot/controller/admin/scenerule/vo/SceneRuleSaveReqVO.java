package cn.powerchina.bjy.link.iot.controller.admin.scenerule.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDate;

import jakarta.validation.constraints.*;

@Schema(description = "管理后台 - 场景规则新增/修改 Request VO")
@Data
public class SceneRuleSaveReqVO {

    @Schema(description = "主键id", example = "16863")
    private Long id;

    @Schema(description = "规则名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    @NotEmpty(message = "规则名称不能为空")
    private String ruleName;

    @Schema(description = "所属资源空间ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "15362")
    @NotNull(message = "所属资源空间ID不能为空")
    private Long resourceSpaceId;

    @Schema(description = "状态:0-禁用,1-启用", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "状态:0-禁用,1-启用不能为空")
    private Integer status;

    @Schema(description = "是否抑制:0-禁用抑制,1-启用抑制", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "是否抑制:0-禁用抑制,1-启用抑制不能为空")
    private Integer inhibition;

    @Schema(description = "生效时段类型:1-全天,2-自定义", example = "2")
    private Integer effectiveType;

    @Schema(description = "生效开始时间")
    private String effectiveStartTime;

    @Schema(description = "生效结束时间")
    private String effectiveEndTime;

    @Schema(description = "重复类型:1-每天,2-指定日期,3-指定周期,4-自定义", example = "2")
    private Integer repeatType;

    @Schema(description = "开始日期/指定日期")
    private LocalDate repeatStartDate;

    @Schema(description = "结束日期")
    private LocalDate repeatEndDate;

    @Schema(description = "每周重复的星期几,如:1,2,3,4,5,6,7")
    private String repeatWeekDays;

    @Schema(description = "规则表达式")
    private String ruleExpression;

    @Schema(description = "抑制规则表达式")
    private String inhibitionExpression;

    @Schema(description = "规则优先级")
    private Integer rulePriority;

    @Schema(description = "规则描述")
    private String ruleDesc;

    @Schema(description = "规则action",hidden = true)
    private String ruleAction;

}