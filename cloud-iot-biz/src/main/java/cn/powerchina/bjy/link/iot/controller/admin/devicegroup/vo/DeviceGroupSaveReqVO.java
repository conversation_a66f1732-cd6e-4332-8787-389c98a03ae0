package cn.powerchina.bjy.link.iot.controller.admin.devicegroup.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
@JsonIgnoreProperties(ignoreUnknown=true)
@Schema(description = "管理后台 - 设备分组新增/修改 Request VO")
@Data
public class DeviceGroupSaveReqVO {

    @Schema(description = "主键id")
    private Long id;

    @Schema(description = "资源空间id")
    @NotNull(message = "请选择资源空间")
    private Long resourceSpaceId;

    @Schema(description = "父节点id")
    private Long parentId;

    @Schema(description = "分组名称")
    @NotBlank(message = "请输入分组名称")
    private String groupName;

    @Schema(description = "分组描述")
    private String groupRemark;

}