package cn.powerchina.bjy.link.iot.dto.up;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 设备写入控制命令后返回
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EdgeWritePropertyValue implements Serializable {

    /**
     * 采集时刻时间
     */
    private Long currentTime;

    /**
     * 消息id
     */
    private String messageId;

    /**
     * 设备编码
     */
    private String deviceCode;

    /**
     * 物模型标识符
     */
    private String thingIdentity;

    /**
     * 输出参数
     */
    private String outputParams;

    /**
     * 操作结果，1：成功，2：失败
     */
    private Integer resultCode;

    /**
     * 下行时间
     */
    private Integer downConsumeTime;


}
