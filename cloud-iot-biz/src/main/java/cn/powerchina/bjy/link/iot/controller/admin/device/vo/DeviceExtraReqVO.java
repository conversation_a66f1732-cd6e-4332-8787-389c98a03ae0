package cn.powerchina.bjy.link.iot.controller.admin.device.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = " DeviceExtra Request VO")
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class DeviceExtraReqVO {

    @Schema(description = "设备extra")
    private String deviceExtra;

    @Schema(description = "通道extra")
    private String channelExtra;

    @Schema(description = "连接方式（1-RTU；2-TCP）")
    private Integer connectType;


}