package cn.powerchina.bjy.link.iot.service.mqttauth;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.link.iot.controller.admin.mqttauth.vo.MqttAuthPageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.mqttauth.vo.MqttAuthSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.mqttauth.MqttAuthDO;
import jakarta.validation.*;

import java.util.List;

/**
 * mqtt认证 Service 接口
 *
 * <AUTHOR>
 */
public interface MqttAuthService {

    /**
     * 创建mqtt认证
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createMqttAuth(@Valid MqttAuthSaveReqVO createReqVO);

    /**
     * 更新mqtt认证
     *
     * @param updateReqVO 更新信息
     */
    void updateMqttAuth(@Valid MqttAuthSaveReqVO updateReqVO);

    /**
     * 更新mqtt认证的密钥
     * @param userName 用户名
     * @param secret 密钥
     */
    void updateMqttAuth(String userName, String secret);

    /**
     * 更新mqtt认证的状态
     * @param userName 用户名
     * @param status 状态
     */
    void updateMqttAuthStatus(String userName, Integer status);

    /**
     * 删除mqtt认证
     *
     * @param id 编号
     */
    void deleteMqttAuth(Long id);

    /**
     * 根据用户名删除
     * @param userName 用户名
     */
    void deleteByUserName(String userName);

    /**
     * 根据多个用户名批量删除
     * @param userNameList 用户名列表
     */
    void deleteByUserNameList(List<String> userNameList);

    /**
     * 获得mqtt认证
     *
     * @param id 编号
     * @return mqtt认证
     */
    MqttAuthDO getMqttAuth(Long id);

    /**
     * 获得mqtt认证分页
     *
     * @param pageReqVO 分页查询
     * @return mqtt认证分页
     */
    PageResult<MqttAuthDO> getMqttAuthPage(MqttAuthPageReqVO pageReqVO);

}