package cn.powerchina.bjy.link.iot.service.properties;

import cn.hutool.core.collection.CollectionUtil;
import cn.powerchina.bjy.cloud.framework.common.util.json.JsonUtils;
import cn.powerchina.bjy.link.iot.dal.dataobject.device.DeviceDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.edgechannel.EdgeChannelDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.edgegateway.EdgeGatewayDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.product.ProductDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.productmodel.ProductModelDO;
import cn.powerchina.bjy.link.iot.dal.mysql.device.DeviceMapper;
import cn.powerchina.bjy.link.iot.dal.mysql.edgechannel.EdgeChannelMapper;
import cn.powerchina.bjy.link.iot.dal.mysql.edgegateway.EdgeGatewayMapper;
import cn.powerchina.bjy.link.iot.dal.mysql.product.ProductMapper;
import cn.powerchina.bjy.link.iot.dal.mysql.productmodel.ProductModelMapper;
import cn.powerchina.bjy.link.iot.dto.down.EdgePropertyDTO;
import cn.powerchina.bjy.link.iot.enums.IotTopicConstant;
import cn.powerchina.bjy.link.iot.enums.NodeTypeEnum;
import cn.powerchina.bjy.link.iot.enums.ThingModeTypeEnum;
import cn.powerchina.bjy.link.iot.model.IotDeviceMessage;
import cn.powerchina.bjy.link.iot.mq.RocketMQv5Client;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * PropertiesServiceImpl
 *
 * <AUTHOR>
 **/
@Slf4j
@Service
public class PropertiesServiceImpl implements PropertiesService {

    @Resource
    private DeviceMapper deviceMapper;

    @Resource
    private ProductMapper productMapper;

    @Resource
    private ProductModelMapper modelMapper;

    @Resource
    private EdgeChannelMapper channelMapper;

    @Resource
    private EdgeGatewayMapper gatewayMapper;


    @Resource
    private RocketMQv5Client rocketMQv5Client;

    @Override
    public void fetchEdgeProperties(String deviceCode, List<String> mcuChannelList) {
        EdgePropertyDTO edgePropertyDTO = fetchProperties(deviceCode, mcuChannelList);
        if (Objects.nonNull(edgePropertyDTO)) {
            edgePropertyDTO.setCurrentTime(System.currentTimeMillis());

            JSONObject parent = new JSONObject();
            parent.put("deviceCode", edgePropertyDTO.getEdgeCode());
            parent.put("subDeviceCode", edgePropertyDTO.getDeviceCode());
            parent.put("subDeviceId", edgePropertyDTO.getDeviceCode());
            parent.put("method", "properties.get.request");
            rocketMQv5Client.syncSendFifoMessage(IotDeviceMessage.TOPIC_GATEWAY_DEVICE_MESSAGE, parent,  IotTopicConstant.GROUP_DEVICE_PROPERTY);

            edgePropertyDTO.getDevicePropertyDTOList().forEach(propertyDTO -> {
                JSONObject child = new JSONObject();
                child.put("deviceCode", edgePropertyDTO.getEdgeCode());
                child.put("subDeviceCode", propertyDTO.getDeviceCode());
                child.put("subDeviceId", propertyDTO.getDeviceCode());
                child.put("method", "properties.get.request");
                rocketMQv5Client.syncSendFifoMessage(IotDeviceMessage.TOPIC_GATEWAY_DEVICE_MESSAGE, child,  IotTopicConstant.GROUP_DEVICE_PROPERTY);
            });
        }
    }

    @Override
    public EdgePropertyDTO fetchProperties(String deviceCode, List<String> mcuChannelList) {
        if (StringUtils.isBlank(deviceCode)) {
            return null;
        }
        DeviceDO deviceDO = deviceMapper.selectByCode(deviceCode);
        if (Objects.isNull(deviceDO) || StringUtils.isBlank(deviceDO.getProductCode())) {
            log.warn("设备信息不存在或设备未关联产品，设备编码【{}】", deviceCode);
            return null;
        }
        ProductDO productDO = productMapper.selectByCode(deviceDO.getProductCode());
        if (Objects.isNull(productDO)) {
            log.warn("产品信息不存在，设备编码【{}】，产品编码【{}】", deviceCode, deviceDO.getProductCode());
            return null;
        }
        // 网关设备
        if (NodeTypeEnum.EDGE.getType().equals(productDO.getNodeType())) {
            log.info("网关设备属性采集");
            return edgeProperty(deviceDO, mcuChannelList);
        }
        // 子设备
        else if (NodeTypeEnum.EDGE_SUB.getType().equals(productDO.getNodeType())) {
            log.info("子设备属性采集");
            return edgeSubProperty(deviceDO);
        }
        return null;
    }

    /**
     * 网关设备会连同其所有子设备信息一起下发
     *
     * @param deviceDO 网关设备
     */
    private EdgePropertyDTO edgeProperty(DeviceDO deviceDO, List<String> mcuChannelList) {
        //EdgeGatewayDO edgeGatewayDO = gatewayMapper.selectByEdgeCode(deviceDO.getEdgeCode());
        DeviceDO gatewayDevice = deviceMapper.selectByCode(deviceDO.getEdgeCode());
        if (Objects.isNull(gatewayDevice)) {
            log.warn("边缘网关实例不存在，实例编码【{}】", deviceDO.getEdgeCode());
            return null;
        }
        //组装网关信息
        EdgePropertyDTO propertyDTO = getMcuDTO(deviceDO);
        // 获取所有子设备
        propertyDTO.setDevicePropertyDTOList(fillSubDevicePropertyDTO(deviceDO.getDeviceCode(), mcuChannelList));
        propertyDTO.setEdgeServiceName("cloud-link-edge_"+deviceDO.getEdgeCode());
        return propertyDTO;
    }

    /**
     * 子设备维度的属性获取
     *
     * @param deviceDO 子设备
     */
    private EdgePropertyDTO edgeSubProperty(DeviceDO deviceDO) {
        return fetchSubDeviceProperty(deviceDO);
    }

    /**
     * 组装子设备属性
     *
     * @param deviceDO
     * @return
     */
    private EdgePropertyDTO fetchSubDeviceProperty(DeviceDO deviceDO) {
        if (StringUtils.isBlank(deviceDO.getParentCode())) {
            log.warn("子设备关联的网关编码为空，设备编码【{}】", deviceDO.getDeviceCode());
            return null;
        }
        DeviceDO mcuDevice = deviceMapper.selectByCode(deviceDO.getParentCode());
        if (null == mcuDevice) {
            log.warn("子设备关联的网关设备不存在，网关设备编码【{}】", deviceDO.getParentCode());
            return null;
        }
        EdgeGatewayDO edgeGatewayDO = gatewayMapper.selectByEdgeCode(mcuDevice.getEdgeCode());
        if (null == edgeGatewayDO) {
            log.warn("子设备关联的网关设备对应的网关实例不存在，网关实例编码【{}】", mcuDevice.getEdgeCode());
            return null;
        }
        EdgeChannelDO channelDO = getChannel(mcuDevice.getChannelCode());
        if (null == channelDO) {
            return null;
        }
        EdgePropertyDTO propertyDTO = getMcuDTO(mcuDevice);
        EdgePropertyDTO.EdgeDevicePropertyDTO subPropertyDTO = new EdgePropertyDTO.EdgeDevicePropertyDTO();
        subPropertyDTO.setProductCode(deviceDO.getProductCode());
        subPropertyDTO.setDeviceCode(deviceDO.getDeviceCode());
        subPropertyDTO.setMcuChannel(deviceDO.getMcuChannel());
        subPropertyDTO.setThingIdentityMap(fillIdentityMap(deviceDO.getProductCode()));
        propertyDTO.setDevicePropertyDTOList(Lists.newArrayList(subPropertyDTO));
        propertyDTO.setEdgeServiceName(edgeGatewayDO.getEdgeServiceName());
        return propertyDTO;
    }

    /**
     * 组装网关信息
     *
     * @param deviceDO 网关设备
     * @return EdgePropertyDTO
     */
    private EdgePropertyDTO getMcuDTO(DeviceDO deviceDO) {
        EdgePropertyDTO result = new EdgePropertyDTO();
        EdgeChannelDO channelDO = getChannel(deviceDO.getChannelCode());
        if (null == channelDO) {
            return result;
        }
        result.setDevicePropertyDTOList(new ArrayList<>());
        result.setDriverCode(deviceDO.getDriverCode());
        result.setProductCode(deviceDO.getProductCode());
        result.setEdgeCode(deviceDO.getEdgeCode());
        result.setDeviceCode(deviceDO.getDeviceCode());
        result.setSlaveId(deviceDO.getSlaveId());
        result.setConnectType(channelDO.getConnectType());
        result.setExtra(channelDO.getExtra());
        result.setThingIdentityMap(fillIdentityMap(deviceDO.getProductCode()));
        return result;
    }

    private EdgeChannelDO getChannel(String code) {
        return channelMapper.selectByCode(code);
    }

    /**
     * 组装子设备下发DTO
     *
     * @param parentCode 网关设备编码
     * @return List<EdgePropertyDTO.EdgeDevicePropertyDTO>
     */
    private List<EdgePropertyDTO.EdgeDevicePropertyDTO> fillSubDevicePropertyDTO(String parentCode, List<String> mcuChannelList) {
        QueryWrapper<DeviceDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(DeviceDO::getParentCode, parentCode).eq(DeviceDO::getDeleted, Boolean.FALSE);
        if (!CollectionUtils.isEmpty(mcuChannelList)) {
            queryWrapper.lambda().in(DeviceDO::getMcuChannel, mcuChannelList);
        }
        List<DeviceDO> deviceDOS = deviceMapper.selectList(queryWrapper);

        if (CollectionUtil.isEmpty(deviceDOS)) {
            log.info("未查询到子设备，网关编码为【{}】", parentCode);
            return Lists.newArrayList();
        }

        List<EdgePropertyDTO.EdgeDevicePropertyDTO> result = new ArrayList<>(deviceDOS.size());
        deviceDOS.forEach(item -> {
            EdgePropertyDTO.EdgeDevicePropertyDTO subPropertyDTO = new EdgePropertyDTO.EdgeDevicePropertyDTO();
            subPropertyDTO.setProductCode(item.getProductCode());
            subPropertyDTO.setDeviceCode(item.getDeviceCode());
            subPropertyDTO.setMcuChannel(item.getMcuChannel());
            subPropertyDTO.setThingIdentityMap(fillIdentityMap(item.getProductCode()));
            result.add(subPropertyDTO);
        });

        return result;
    }

    private Map<String, Integer> fillIdentityMap(String productCode) {
        List<ProductModelDO> modelDOList = getProductModel(productCode);
        return thingIdentityConvert(modelDOList);
    }

    /**
     * 获取物模型
     *
     * @param productCode 产品编码
     * @return List<ProductModelDO>
     */
    private List<ProductModelDO> getProductModel(String productCode) {
        QueryWrapper<ProductModelDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(ProductModelDO::getProductCode, productCode)
                .eq(ProductModelDO::getThingType, ThingModeTypeEnum.PROPERTY.getType())
                .eq(ProductModelDO::getDeleted, Boolean.FALSE);

        List<ProductModelDO> modelDOList = modelMapper.selectList(queryWrapper);

        return CollectionUtil.isNotEmpty(modelDOList) ? modelDOList : Lists.newArrayList();
    }

    /**
     * 物模型转换
     * 进行物模型标识符和数据位的转换，默认数据位为0
     *
     * @param modelDOList 物模型
     * @return Map
     */
    private Map<String, Integer> thingIdentityConvert(List<ProductModelDO> modelDOList) {
        Map<String, Integer> result = new HashMap<>(64);
        if (CollectionUtil.isEmpty(modelDOList)) {
            return result;
        }
        modelDOList.forEach(item -> {
            String input = item.getInputParams();
            if (StringUtils.isBlank(input)) {
                result.put(item.getThingIdentity(), 0);
                return;
            }
            Map<String, Object> inputMap = JsonUtils.parseObject(input, new TypeReference<>() {
            });
            String order = (String) inputMap.get("order");
            result.put(item.getThingIdentity(), StringUtils.isBlank(order) ? 0 : Integer.parseInt(order));
        });

        return result;
    }
}
