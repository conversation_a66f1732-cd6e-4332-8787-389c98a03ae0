package cn.powerchina.bjy.link.iot.service.sceneruleaction;

import cn.hutool.core.collection.CollectionUtil;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.iot.controller.admin.sceneruleaction.vo.SceneRuleActionPageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.sceneruleaction.vo.SceneRuleActionSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.datapermissions.DataPermissionsDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.sceneruleaction.SceneRuleActionDO;
import cn.powerchina.bjy.link.iot.dal.mysql.device.DeviceMapper;
import cn.powerchina.bjy.link.iot.dal.mysql.product.ProductMapper;
import cn.powerchina.bjy.link.iot.dal.mysql.sceneruleaction.SceneRuleActionMapper;
import cn.powerchina.bjy.link.iot.enums.ActionTypeEnum;
import cn.powerchina.bjy.link.iot.service.alarmtemplate.AlarmTemplateService;
import com.alibaba.fastjson.JSONObject;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import java.util.stream.Collectors;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.link.iot.enums.ErrorCodeConstants.SCENE_RULE_ACTION_NOT_EXISTS;

/**
 * 场景规则执行动作 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SceneRuleActionServiceImpl implements SceneRuleActionService {

    @Resource
    private SceneRuleActionMapper sceneRuleActionMapper;

    @Resource
    private AlarmTemplateService alarmTemplateService;

    @Resource
    private DeviceMapper deviceMapper;

    @Resource
    private ProductMapper productMapper;

    @Override
    public void createAndUpdateSceneRuleAction(List<SceneRuleActionSaveReqVO> saveOrUpdateList, Long ruleId) {
        List<Long> oldIdList = new ArrayList<>();
        List<SceneRuleActionDO> oldEntityList = getSceneRuleActionByRuleId(ruleId);
        if (!CollectionUtils.isEmpty(oldEntityList)) {
            oldIdList = oldEntityList.stream().map(SceneRuleActionDO::getId).collect(Collectors.toList());
        }
        //saveOrUpdateList为空，oldIdList不为空时，说明是全部删除了。将所属执行动作全部删除
        if (CollectionUtil.isEmpty(saveOrUpdateList) && !CollectionUtils.isEmpty(oldIdList)) {
            sceneRuleActionMapper.deleteBatchIds(oldIdList);
            return;
        }

        for (SceneRuleActionSaveReqVO ruleActionSaveReqVO : saveOrUpdateList) {
            ruleActionSaveReqVO.setRuleId(ruleId);
            //设备动作
            if (ActionTypeEnum.DEVICE_TYPE.getType().equals(ruleActionSaveReqVO.getActionType())) {
                ruleActionSaveReqVO.setCommandConfig(JSONObject.toJSONString(ruleActionSaveReqVO.getCommandList()));
            }
            Long actionId = ruleActionSaveReqVO.getId();
            if (Objects.isNull(ruleActionSaveReqVO.getId())) {
                actionId = createSceneRuleAction(ruleActionSaveReqVO);
            } else {
                updateSceneRuleAction(ruleActionSaveReqVO);
                //将告警模板和消息配置删除
                //alarmTemplateService.inviedAlarmTemplate(oldIdList);
                oldIdList.remove(ruleActionSaveReqVO.getId());
            }
            //判断是否是告警模板
            if (ActionTypeEnum.ALARM_TYPE.getType().equals(ruleActionSaveReqVO.getActionType())) {
                alarmTemplateService.createAndUpdateAlarmTemplate(ruleActionSaveReqVO.getAlarmTemplateSave(), ruleId, actionId);
            }
        }
        //删除
        if (!CollectionUtils.isEmpty(oldIdList)) {
            sceneRuleActionMapper.deleteBatchIds(oldIdList);
        }
    }

    @Override
    public List<SceneRuleActionDO> getSceneRuleActionByRuleId(Long ruleId) {
        return sceneRuleActionMapper.selectList(new LambdaQueryWrapperX<SceneRuleActionDO>().
                eq(SceneRuleActionDO::getRuleId, ruleId)
                .orderByAsc(SceneRuleActionDO::getSort));
    }

    @Override
    public List<SceneRuleActionDO> getRuleActionBySceneId(Long sceneId) {
        return sceneRuleActionMapper.selectList(new LambdaQueryWrapperX<SceneRuleActionDO>().
                eq(SceneRuleActionDO::getSceneId, sceneId));
    }

    @Override
    public Long createSceneRuleAction(SceneRuleActionSaveReqVO createReqVO) {
        // 插入
        SceneRuleActionDO sceneRuleAction = BeanUtils.toBean(createReqVO, SceneRuleActionDO.class);
        sceneRuleActionMapper.insert(sceneRuleAction);
        // 返回
        return sceneRuleAction.getId();
    }

    @Override
    public void updateSceneRuleAction(SceneRuleActionSaveReqVO updateReqVO) {
        // 校验存在
        validateSceneRuleActionExists(updateReqVO.getId());
        // 更新
        SceneRuleActionDO updateObj = BeanUtils.toBean(updateReqVO, SceneRuleActionDO.class);
        sceneRuleActionMapper.updateById(updateObj);
    }

    @Override
    public void deleteSceneRuleAction(Long id) {
        // 校验存在
        validateSceneRuleActionExists(id);
        // 删除
        sceneRuleActionMapper.deleteById(id);
    }

    private void validateSceneRuleActionExists(Long id) {
        if (sceneRuleActionMapper.selectById(id) == null) {
            throw exception(SCENE_RULE_ACTION_NOT_EXISTS);
        }
    }

    @Override
    public SceneRuleActionDO getSceneRuleAction(Long id) {
        return sceneRuleActionMapper.selectById(id);
    }

    @Override
    public PageResult<SceneRuleActionDO> getSceneRuleActionPage(SceneRuleActionPageReqVO pageReqVO) {
        return sceneRuleActionMapper.selectPage(pageReqVO);
    }

    //触发条件、限制条件场景删除
    @Override
    public List<Long> deleteSceneRuleAction(String productCode, String deviceCode) {
        List<SceneRuleActionDO> ruleActionDOList = sceneRuleActionMapper.selectList(new LambdaQueryWrapperX<SceneRuleActionDO>()
                .eqIfPresent(SceneRuleActionDO::getProductCode, productCode)
                .eqIfPresent(SceneRuleActionDO::getDeviceCode, deviceCode)
                .eq(SceneRuleActionDO::getActionType, ActionTypeEnum.DEVICE_TYPE.getType()));
        if (!CollectionUtils.isEmpty(ruleActionDOList)) {
            ruleActionDOList.forEach(item -> item.setIsInvalid(2));
            sceneRuleActionMapper.updateBatch(ruleActionDOList);
        }
        return ruleActionDOList.stream()
                .map(SceneRuleActionDO::getRuleId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
    }

    //触发条件、限制条件场景失效
    @Override
    public List<Long> invalidSceneRuleAction(Long userId, List<DataPermissionsDO> dataPermissionsList) {
        Map<String, String> dataPermissionsMap = new HashMap<>();
        dataPermissionsList.forEach(item -> {
            dataPermissionsMap.put(item.getDataId(), item.getParentId());
        });
        //如果设备没匹配上，找全部设备（-产品），全部设备没匹配上，找所有产品(-资源空间id),还没匹配上更改状态
        //查询所有设备
        Map<String, Long> devicesMap = new HashMap<>();
        Map<String, String> devicesProductMap = new HashMap<>();
        deviceMapper.selectList().forEach(device -> {
            devicesMap.put(device.getDeviceCode(), device.getId());
            devicesProductMap.put(device.getDeviceCode(), device.getProductCode());
        });
        //查询所有产品
        Map<String, Long> productMap = new HashMap<>();
        Map<String, Long> productSpaceMap = new HashMap<>();
        productMapper.selectList().forEach(product -> {
            productMap.put(product.getProductCode(), product.getId());
            productSpaceMap.put(product.getProductCode(), product.getResourceSpaceId());
        });

        List<SceneRuleActionDO> ruleActionList = sceneRuleActionMapper.selectList(new LambdaQueryWrapperX<SceneRuleActionDO>()
                .eq(SceneRuleActionDO::getCreator, userId)
                .eq(SceneRuleActionDO::getActionType, ActionTypeEnum.DEVICE_TYPE.getType())
                .eq(SceneRuleActionDO::getIsInvalid, 0));

        List<SceneRuleActionDO> InvalidList = new ArrayList<>();
        //如果设备没匹配上，找全部设备（-产品），全部设备没匹配上，找所有产品(-资源空间id),还没匹配上就更改状态
        if (!CollectionUtils.isEmpty(ruleActionList)) {
            ruleActionList.forEach(item -> {
                if (devicesMap.containsKey(item.getDeviceCode())) {
                    //获取规则的设备id
                    Long deviceId = devicesMap.get(item.getDeviceCode());
                    //获取产品编码
                    String productCode = devicesProductMap.get(item.getDeviceCode());
                    //通过设备id与权限中的设备id比对
                    if (!dataPermissionsMap.containsKey(String.valueOf(deviceId))) {
                        //如果设备没匹配上，看是否勾选了全部设备（-产品）
                        Long productId = productMap.get(productCode);
                        if (!dataPermissionsMap.containsKey("-" + productId)) {
                            //如果全部设备没匹配上，看是否勾选了所有产品(-资源空间id)
                            Long resourceSpaceId = productSpaceMap.get(productCode);
                            if (!dataPermissionsMap.containsKey("-" + resourceSpaceId)) {
                                item.setIsInvalid(1);
                                InvalidList.add(item);
                            }
                        }
                    }
                }
                if ("-1".equals(item.getDeviceCode())) {
                    Long productId = productMap.get(item.getProductCode());
                    if (!dataPermissionsMap.containsKey("-" + productId)) {
                        Long resourceSpaceId = productSpaceMap.get(item.getProductCode());
                        if (!dataPermissionsMap.containsKey("-" + resourceSpaceId)) {
                            item.setIsInvalid(1);
                            InvalidList.add(item);
                        }
                    }
                }
            });
        }
        if (!CollectionUtils.isEmpty(InvalidList)) {
            sceneRuleActionMapper.updateBatch(InvalidList);
        }
        return InvalidList.stream()
                .map(SceneRuleActionDO::getRuleId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    public List<Long> effectiveSceneRuleAction(Long userId, List<DataPermissionsDO> dataPermissionsList) {
        Map<String, String> dataPermissionsMap = new HashMap<>();
        dataPermissionsList.forEach(item -> {
            dataPermissionsMap.put(item.getDataId(), item.getParentId());
        });
        //如果设备没匹配上，找全部设备（-产品），全部设备没匹配上，找所有产品(-资源空间id),还没匹配上更改状态
        //查询所有设备
        Map<String, Long> devicesMap = new HashMap<>();
        Map<String, String> devicesProductMap = new HashMap<>();
        deviceMapper.selectList().forEach(device -> {
            devicesMap.put(device.getDeviceCode(), device.getId());
            devicesProductMap.put(device.getDeviceCode(), device.getProductCode());
        });
        //查询所有产品
        Map<String, Long> productMap = new HashMap<>();
        Map<String, Long> productSpaceMap = new HashMap<>();
        productMapper.selectList().forEach(product -> {
            productMap.put(product.getProductCode(), product.getId());
            productSpaceMap.put(product.getProductCode(), product.getResourceSpaceId());
        });

        List<SceneRuleActionDO> ruleActionList = sceneRuleActionMapper.selectList(new LambdaQueryWrapperX<SceneRuleActionDO>()
                .eq(SceneRuleActionDO::getCreator, userId)
                .eq(SceneRuleActionDO::getActionType, ActionTypeEnum.DEVICE_TYPE.getType())
                .eq(SceneRuleActionDO::getIsInvalid, 1));

        List<SceneRuleActionDO> InvalidList = new ArrayList<>();
        //如果设备没匹配上，找全部设备（-产品），全部设备没匹配上，找所有产品(-资源空间id),还没匹配上就更改状态
        if (!CollectionUtils.isEmpty(ruleActionList)) {
            ruleActionList.forEach(item -> {
                if (devicesMap.containsKey(item.getDeviceCode())) {
                    //获取规则的设备id
                    Long deviceId = devicesMap.get(item.getDeviceCode());
                    //获取产品编码
                    String productCode = devicesProductMap.get(item.getDeviceCode());
                    //通过设备id与权限中的设备id比对
                    boolean isValid = dataPermissionsMap.containsKey(String.valueOf(deviceId))
                            || dataPermissionsMap.containsKey("-" + productMap.get(productCode))
                            || dataPermissionsMap.containsKey("-" + productSpaceMap.get(productCode));
                    if (isValid) {
                        item.setIsInvalid(0);
                        InvalidList.add(item);
                    }
                }
                if ("-1".equals(item.getDeviceCode())) {
                    Long productId = productMap.get(item.getProductCode());
                    Long resourceSpaceId = productSpaceMap.get(item.getProductCode());

                    if (dataPermissionsMap.containsKey("-" + productId)
                            || dataPermissionsMap.containsKey("-" + resourceSpaceId)) {
                        item.setIsInvalid(0);
                        InvalidList.add(item);
                    }
                }
            });
        }
        if (!CollectionUtils.isEmpty(InvalidList)) {
            sceneRuleActionMapper.updateBatch(InvalidList);
        }
        return InvalidList.stream()
                .map(SceneRuleActionDO::getRuleId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    public List<SceneRuleActionDO> getSceneRuleActionByInvalid(Long ruleId, Integer invalid) {
        return sceneRuleActionMapper.selectList(new LambdaQueryWrapperX<SceneRuleActionDO>()
                .eq(SceneRuleActionDO::getRuleId, ruleId)
                .eq(SceneRuleActionDO::getIsInvalid, invalid)
                .orderByAsc(SceneRuleActionDO::getSort));
    }

}