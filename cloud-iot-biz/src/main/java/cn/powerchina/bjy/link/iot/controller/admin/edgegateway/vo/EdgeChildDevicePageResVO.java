package cn.powerchina.bjy.link.iot.controller.admin.edgegateway.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

@Schema(description = "管理后台 - 边缘网关子设备分页 Response VO")
@Data
@ToString(callSuper = true)
public class EdgeChildDevicePageResVO {

    @Schema(description = "主键")
    private Long id;

    @Schema(description = "设备名称")
    private String deviceName;

    @Schema(description = "设备ID")
    private String deviceCode;

    @Schema(description = "设备唯一标识")
    private String deviceSerial;

    @Schema(description = "产品编码")
    private String productCode;

    @Schema(description = "产品名称")
    private String productName;

    @Schema(description = "节点类型(0直连，1网关，2网关子设备）")
    private Integer nodeType;

    @Schema(description = "节点类型文字")
    private String nodeTypeCN;

    @Schema(description = "状态（0:未激活; 1:离线; 2:在线）")
    private Integer linkState;

    @Schema(description = "状态文字（0:未激活; 1:离线; 2:在线）")
    private String linkStateCN;

    @Schema(description = "驱动编码")
    private String driveCode;

    @Schema(description = "驱动名称")
    private String driveName;
}
