package cn.powerchina.bjy.link.iot.controller.admin.scenerule.vo;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.time.LocalDate;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 场景规则分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SceneRulePageReqVO extends PageParam {

    @Schema(description = "规则名称", example = "李四")
    private String ruleName;

    @Schema(description = "所属资源空间ID", example = "15362")
    private Long resourceSpaceId;

    @Schema(description = "所属资源空间ID", example = "15362")
    private List<Long> resourceSpaceIds;

    @Schema(description = "状态:0-禁用,1-启用", example = "1")
    private Integer status;

    @Schema(description = "是否抑制:0-禁用抑制,1-启用抑制")
    private Integer inhibition;

    @Schema(description = "生效时段类型:1-全天,2-自定义", example = "2")
    private Integer effectiveType;

    @Schema(description = "生效开始时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private String[] effectiveStartTime;

    @Schema(description = "生效结束时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private String[] effectiveEndTime;

    @Schema(description = "重复类型:1-每天,2-指定日期,3-指定周期,4-自定义", example = "2")
    private Integer repeatType;

    @Schema(description = "开始日期/指定日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDate[] repeatStartDate;

    @Schema(description = "结束日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDate[] repeatEndDate;

    @Schema(description = "每周重复的星期几,如:1,2,3,4,5,6,7")
    private String repeatWeekDays;

    @Schema(description = "规则表达式")
    private String ruleExpression;

    @Schema(description = "规则优先级")
    private Integer rulePriority;

    @Schema(description = "规则描述")
    private String ruleDesc;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}