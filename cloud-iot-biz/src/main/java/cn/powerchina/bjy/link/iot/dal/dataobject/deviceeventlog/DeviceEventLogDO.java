package cn.powerchina.bjy.link.iot.dal.dataobject.deviceeventlog;

import cn.powerchina.bjy.cloud.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 设备事件日志 DO
 *
 * <AUTHOR>
 */
@TableName("iot_device_event_log")
@KeySequence("iot_device_event_log_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeviceEventLogDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 设备编号
     */
    private String deviceCode;
    /**
     * 设备名称
     */
    private String deviceName;
    /**
     * 物模型标识符
     */
    private String thingIdentity;
    /**
     * 物模型名称
     */
    private String thingName;
    /**
     * 类型
     */
    private Integer eventType;
    /**
     * 物模型日志值
     */
    private String thingValue;
    /**
     * 模式(1=影子模式，2=在线模式，3=其他)
     */
    private Integer deviceMode;
    /**
     * 备注
     */
    private String remark;

}