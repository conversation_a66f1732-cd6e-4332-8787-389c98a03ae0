package cn.powerchina.bjy.link.iot.dal.dataobject.scenealarmrecord;

import cn.powerchina.bjy.cloud.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 告警记录 DO
 *
 * <AUTHOR>
 */
@TableName("iot_scene_alarm_record")
@KeySequence("iot_scene_alarm_record_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SceneAlarmRecordDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 资源空间ID
     */
    private Long resourceSpaceId;
    /**
     * 规则ID
     */
    private Long ruleId;
    /**
     * 规则名称
     */
    private String ruleName;
    /**
     * 关联的告警模板ID
     */
    private Long alarmTemplateId;
    /**
     * 告警名称
     */
    private String alarmName;
    /**
     * 告警内容
     */
    private String alarmContent;
    /**
     * 告警等级(1-轻微,2-中等,3-严重,4-非常严重)
     */
    private Integer alarmLevel;
    /**
     * 告警状态(1-触发,2-待验证,3-恢复)
     */
    private Integer alarmStatus;
    /**
     * 触发时间
     */
    private LocalDateTime triggerTime;
    /**
     * 设备编码
     */
    private String deviceCode;
    /**
     * 产品编码
     */
    private String productCode;
    /**
     * 处理时间
     */
    private LocalDateTime processTime;
    /**
     * 告警处理
     */
    private String processRecord;
    /**
     * 处理用户ID
     */
    private Long processUserId;
    /**
     * 恢复类型(0-自动恢复,1-人工恢复)
     */
    private Integer recoveryType;
    /**
     * 恢复时间
     */
    private LocalDateTime recoveryTime;
    /**
     * 恢复用户ID
     */
    private Long recoveryUserId;
    /**
     * 告警次数
     */
    private Integer alarmNum;
    /**
     * 最后告警时间
     */
    private LocalDateTime lastAlarmTime;

}
