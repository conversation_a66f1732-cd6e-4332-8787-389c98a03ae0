package cn.powerchina.bjy.link.iot.job;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.hc.client5.http.classic.methods.HttpPost;
import org.apache.hc.client5.http.cookie.BasicCookieStore;
import org.apache.hc.client5.http.cookie.Cookie;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.CloseableHttpResponse;
import org.apache.hc.client5.http.impl.classic.HttpClients;
import org.apache.hc.core5.http.ContentType;
import org.apache.hc.core5.http.HttpEntity;
import org.apache.hc.core5.http.ParseException;
import org.apache.hc.core5.http.io.entity.EntityUtils;
import org.apache.hc.core5.http.io.entity.StringEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class XxlJobAdminClient {
    private static final ObjectMapper objectMapper = new ObjectMapper();

    @Value("${xxl.job.admin.addresses}")
    private String ADMIN_URL;
    @Value("${xxl.job.admin.userName}")
    private String USERNAME;
    @Value("${xxl.job.admin.password}")
    private String PASSWORD;
    @Value("${xxl.job.admin.jobGroup}")
    private String jobGroup;
    @Value("${xxl.job.admin.jobHandler}")
    private String jobHandler;

    private final CloseableHttpClient httpClient; // 注入共享的HttpClient

    @Autowired
    public XxlJobAdminClient(CloseableHttpClient httpClient) {
        this.httpClient = httpClient;
    }
    public void removeJob(int jobId) {
        try {
            // 1. 创建带Cookie管理的HTTP客户端
            BasicCookieStore cookieStore = new BasicCookieStore();
            // 2. 登录并获取Cookie
            boolean loginSuccess = login();
            if (!loginSuccess) {
                System.out.println("登录失败，退出程序");
                return;
            }

            // 3. 打印获取到的Cookie信息
            printCookies(cookieStore);

            // 4. 使用同一客户端创建任务（自动携带Cookie）
            boolean createSuccess = deleteJob(jobId);
            System.out.println("任务创建结果：" + (createSuccess ? "成功" : "失败"));

        } catch (Exception e) {
            log.error("删除xxljob失败", e);
        }
    }


    public int addJob(XxlJobInfo jobInfo) {
        int jobId = 0;
        try {
            // 1. 创建带Cookie管理的HTTP客户端
            BasicCookieStore cookieStore = new BasicCookieStore();

            // 2. 登录并获取Cookie
            boolean loginSuccess = login();
            if (!loginSuccess) {
                System.out.println("登录失败，退出程序");
            }

            // 3. 打印获取到的Cookie信息
            printCookies(cookieStore);

            Map<String, String> jobParams = new HashMap<>();
            jobParams.put("jobGroup", jobGroup);                          // 执行器ID
            jobParams.put("jobDesc", URLEncoder.encode(jobInfo.getJobDesc(), "UTF-8"));
            jobParams.put("author", URLEncoder.encode(jobInfo.getAuthor(), "UTF-8"));                        // 任务描述         // 负责人
            jobParams.put("alarmEmail", "");                           // 告警邮箱
            jobParams.put("scheduleType", "CRON");                     // 调度类型
            jobParams.put("scheduleConf", jobInfo.getScheduleConf());            // Cron表达式（每分钟执行）
            jobParams.put("misfireStrategy", "DO_NOTHING");            // 调度过期策略
            jobParams.put("executorRouteStrategy", "CONSISTENT_HASH"); // 路由策略
            jobParams.put("executorHandler",jobHandler );    // 执行器Handler
            jobParams.put("executorParam", "");                       // 执行器参数
            jobParams.put("executorBlockStrategy", "SERIAL_EXECUTION");// 阻塞策略
            jobParams.put("executorTimeout", "0");                     // 超时时间（0=不限制）
            jobParams.put("executorFailRetryCount", "3");              // 失败重试次数
            jobParams.put("glueType", "BEAN");                         // GLUE类型
            jobParams.put("glueRemark", URLEncoder.encode("GLUE代码初始化", "UTF-8")); // 中文备注编码
            jobParams.put("glueSource", "");                           // GLUE源代码


            // 4. 使用同一客户端创建任务（自动携带Cookie）
            jobId = createJob(jobParams);
        } catch (Exception e) {
            log.error("创建xxljob失败", e);
        }
        return jobId;
    }


    /**
     * 登录XXL-JOB管理端
     */
    public boolean login() throws IOException {
        String loginUrl = ADMIN_URL + "/login";
        System.out.println("尝试登录：" + loginUrl);

        HttpPost httpPost = new HttpPost(loginUrl);
        httpPost.setHeader("Content-Type", "application/x-www-form-urlencoded");

        // 设置登录参数
        StringEntity entity = new StringEntity(
                "userName=" + USERNAME + "&password=" + PASSWORD,
                ContentType.APPLICATION_FORM_URLENCODED
        );
        httpPost.setEntity(entity);

        // 执行登录请求
        try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
            int statusCode = response.getCode();
            System.out.println("登录响应状态码：" + statusCode);

            if (statusCode == 200) {
                // 解析响应内容
                HttpEntity responseEntity = response.getEntity();
                if (responseEntity != null) {
                    try {
                        String responseBody = EntityUtils.toString(responseEntity, "UTF-8");
                        System.out.println("登录响应内容：" + responseBody);

                        // 检查登录是否成功
                        return responseBody.contains("\"code\":200");
                    } catch (ParseException e) {
                        System.out.println("解析响应内容失败：" + e.getMessage());
                        return false;
                    } finally {
                        EntityUtils.consumeQuietly(responseEntity);
                    }
                }
            }
            return false;
        }
    }


    public int createJob(Map<String, String> jobParams) throws IOException, ParseException {
        String createUrl = ADMIN_URL + "/jobinfo/add";
        System.out.println("尝试创建任务：" + createUrl);

        HttpPost httpPost = new HttpPost(createUrl);
        httpPost.setHeader("Content-Type", "application/x-www-form-urlencoded;charset=UTF-8"); // 添加charset声明

        // 构建请求体
        StringBuilder paramBuilder = new StringBuilder();
        for (Map.Entry<String, String> entry : jobParams.entrySet()) {
            paramBuilder.append(entry.getKey())
                    .append("=")
                    .append(entry.getValue())
                    .append("&");
        }
        if (paramBuilder.length() > 0) {
            paramBuilder.deleteCharAt(paramBuilder.length() - 1);
        }

        httpPost.setEntity(new StringEntity(paramBuilder.toString(), ContentType.APPLICATION_FORM_URLENCODED));

        // 执行请求
        try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
            if (response.getCode() != 200) {
                log.error("创建任务失败，状态码：{}", response.getCode());
                return -1;
            }

            String responseBody = EntityUtils.toString(response.getEntity(), "UTF-8");
            XxlJobResult result = objectMapper.readValue(responseBody, XxlJobResult.class);

            if (result.getCode() == XxlJobResult.SUCCESS_CODE) {
                //添加任务成功 开启job
                startJob(Integer.parseInt(result.getContent()));
                return Integer.parseInt(result.getContent());
            } else {
                log.error("创建任务失败：{}", result.getMsg());
                return -1;
            }
        }
    }

    /**
     * 打印Cookie信息（用于调试）
     */
    private static void printCookies(BasicCookieStore cookieStore) {
        List<Cookie> cookies = cookieStore.getCookies();
        System.out.println("获取到的Cookie数量：" + cookies.size());

        for (Cookie cookie : cookies) {
            System.out.println("Cookie: " + cookie.getName() + "=" + cookie.getValue() +
                    ", Domain=" + cookie.getDomain() +
                    ", Path=" + cookie.getPath());
        }
    }

    /**
     * 删除任务
     *
     * @param  （携带认证Cookie）
     * @param jobId      任务ID
     * @return 删除是否成功
     * @throws IOException 网络异常
     */
    public boolean deleteJob( int jobId) throws IOException {
        String deleteUrl = ADMIN_URL + "/jobinfo/remove";
        System.out.println("执行删除任务：" + deleteUrl);

        HttpPost httpPost = new HttpPost(deleteUrl);
        httpPost.setHeader("Content-Type", "application/x-www-form-urlencoded");

        // 构建删除参数
        StringEntity entity = new StringEntity(
                "id=" + jobId,
                ContentType.APPLICATION_FORM_URLENCODED
        );
        httpPost.setEntity(entity);

        // 执行删除请求
        try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
            validateResponseStatus(response, "删除任务");

            String responseBody = parseResponseEntity(response);
            return responseBody.contains("\"code\":200");
        }
    }

    // 复用XxlJobAuthClient中的验证和解析方法（实际开发中可提取公共工具类）
    private void validateResponseStatus(CloseableHttpResponse response, String operation) throws IOException {
        int statusCode = response.getCode();
        if (statusCode != 200) {
            throw new IOException(operation + "请求失败，状态码：" + statusCode);
        }
    }

    private String parseResponseEntity(CloseableHttpResponse response) throws IOException {
        HttpEntity entity = response.getEntity();
        if (entity == null) {
            throw new IOException("响应内容为空");
        }
        try {
            return EntityUtils.toString(entity, "UTF-8");
        } catch (ParseException e) {
            throw new IOException("解析响应失败：" + e.getMessage());
        } finally {
            EntityUtils.consumeQuietly(entity);
        }
    }


    /**
     * 开启（激活）任务
     * @param jobId 任务ID
     * @return 是否成功
     * @throws IOException 网络异常
     */
    public boolean startJob(int jobId) throws IOException {
        String startUrl = ADMIN_URL + "/jobinfo/start";
        log.info("尝试开启任务：{}，任务ID：{}", startUrl, jobId);

        HttpPost httpPost = new HttpPost(startUrl);
        httpPost.setHeader("Content-Type", "application/x-www-form-urlencoded");

        // 构建参数：id=任务ID
        StringEntity entity = new StringEntity(
                "id=" + jobId,
                ContentType.APPLICATION_FORM_URLENCODED
        );
        httpPost.setEntity(entity);

        try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
            // 验证响应状态码
            int statusCode = response.getCode();
            if (statusCode != 200) {
                log.error("开启任务失败，状态码：{}", statusCode);
                return false;
            }

            // 解析响应内容
            String responseBody = parseResponseEntity(response);
            return responseBody.contains("\"code\":200");
        }
    }



}