package cn.powerchina.bjy.link.iot.controller.admin.device.vo;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


@Schema(description = "管理后台 - 设备分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DevicePageReqVO extends PageParam {

    @Schema(description = "产品编码")
    private String productCode;

    @Schema(description = "产品名称")
    private String productName;

    @Schema(description = "项目编码")
    private String projectCode;

    @Schema(description = "设备名称")
    private String deviceName;

    @Schema(description = "设备编号")
    private String deviceCode;

    @Schema(description = "父设备号（网关）")
    private String parentCode;

    @Schema(description = "设备唯一标识")
    private String deviceSerial;

    @Schema(description = "是否启用设备影子(0=禁用，1=启用)，默认启用")
    private Boolean shadow;

    @Schema(description = "通道编码")
    private String channelCode;

    @Schema(description = "mcu通道号")
    private String mcuChannel;

    @Schema(description = "从站号")
    private String slaveId;

    @Schema(description = "下发状态（0-未下发；1-已下发；）")
    private Integer distributeState;

    @Schema(description = "连接状态（0：未激活 1：离线 2；在线 3：禁用）")
    private Integer linkState;

    @Schema(description = "节点类型(0直连，1网关，2网关子设备）")
    private Integer nodeType;

    @Schema(description = "非节点类型(0直连，1网关，2网关子设备）")
    private Integer neNodeType;

    @Schema(description = "差异化扩展")
    private String extra;

    @Schema(description = "最后上线时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] lastUpTime;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "通道编码")
    private String edgeCode;

    @Schema(description = "驱动编码")
    private String driverCode;

    @Schema(description = "排序字段", example = "deviceCode#desc")
    private String orderColumn;

    @Schema(description = "描述")
    private String remark;

    @Schema(description = "设备密钥")
    private String deviceSecret;

    @Schema(description = "启用状态（0:禁用 1：启用）")
    private Integer status;

    @Schema(description = "资源空间id")
    private Long resourceSpaceId;

    @Schema(description = "分组id")
    private Long deviceGroupId;

    @Schema(description = "某分组下的设备集合", hidden = true)
    private List<String> deviceCodeList;

    @Schema(description = "设备id集合")
    private List<String> codes;

    @Schema(description = "标识")
    private String deviceFlag;

    @Schema(description = "类型(0:edge实例; 1:设备）")
    private Integer deviceType;

    @Schema(description = "是否是场景联动(0:否; 1:是）")
    private Integer isSceneRule;

}