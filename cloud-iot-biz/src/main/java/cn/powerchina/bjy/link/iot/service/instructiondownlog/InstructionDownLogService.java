package cn.powerchina.bjy.link.iot.service.instructiondownlog;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.link.iot.controller.admin.instructiondownlog.vo.InstructionDownLogPageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.instructiondownlog.vo.InstructionDownLogRespVO;
import cn.powerchina.bjy.link.iot.controller.admin.instructiondownlog.vo.InstructionDownLogSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.instructiondownlog.InstructionDownLogDO;
import jakarta.validation.Valid;

/**
 * 指令下发操作记录 Service 接口
 *
 * <AUTHOR>
 */
public interface InstructionDownLogService {

    /**
     * 创建指令下发操作记录
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createInstructionDownLog(@Valid InstructionDownLogSaveReqVO createReqVO);

    /**
     * 更新指令下发操作记录
     *
     * @param updateReqVO 更新信息
     */
    void updateInstructionDownLog(@Valid InstructionDownLogSaveReqVO updateReqVO);

    /**
     * 删除指令下发操作记录
     *
     * @param id 编号
     */
    void deleteInstructionDownLog(Long id);

    /**
     * 获得指令下发操作记录
     *
     * @param id 编号
     * @return 指令下发操作记录
     */
    InstructionDownLogDO getInstructionDownLog(Long id);

    /**
     * 根据messageId获取指令下发操作记录
     * @param messageId messageId
     * @return 指令下发操作记录
     */
    InstructionDownLogDO getByMessageId(String messageId);

    /**
     * 根据设备编码和物模型标识符获取最新的指令下发操作记录
     * @param deviceCode 设备编码
     * @param thingIdentity 物模型标识符
     * @return 指令下发操作记录
     */
    InstructionDownLogDO getLatestByDeviceCodeAndThingIdentity(String deviceCode, String thingIdentity);

    /**
     * 获得指令下发操作记录分页
     *
     * @param pageReqVO 分页查询
     * @return 指令下发操作记录分页
     */
    PageResult<InstructionDownLogDO> getInstructionDownLogPage(InstructionDownLogPageReqVO pageReqVO);

    /**
     * 获得指令下发操作记录分页VO
     *
     * @param pageReqVO 分页查询
     * @return 指令下发操作记录分页
     */
    PageResult<InstructionDownLogRespVO> getInstructionDownLogVOPage(InstructionDownLogPageReqVO pageReqVO);

    /**
     * 创建指令下发操作记录
     *
     * @param instruction
     * @return
     */
    String createInstructionDown(@Valid InstructionDownLogSaveReqVO instruction);

    /**
     * 根据msgid更新操作日志
     *
     * @param messageId
     * @param outParams
     * @param downConsumeTime
     * @param upConsumeTime
     */
    void updateInstructionLogByMsgId(String messageId, String outParams, Integer downConsumeTime, Integer upConsumeTime);
}