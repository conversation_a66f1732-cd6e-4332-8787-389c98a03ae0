package cn.powerchina.bjy.link.iot.service.devicegroup;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.link.iot.controller.admin.devicegroup.vo.DeviceGroupPageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.devicegroup.vo.DeviceGroupSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.devicegroup.DeviceGroupDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 设备分组 Service 接口
 *
 * <AUTHOR>
 */
public interface DeviceGroupService {

    /**
     * 创建设备分组
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createDeviceGroup(@Valid DeviceGroupSaveReqVO createReqVO);

    /**
     * 更新设备分组
     *
     * @param updateReqVO 更新信息
     */
    void updateDeviceGroup(@Valid DeviceGroupSaveReqVO updateReqVO);

    /**
     * 删除设备分组
     *
     * @param id 编号
     */
    void deleteDeviceGroup(Long id);

    /**
     * 获得设备分组
     *
     * @param id 编号
     * @return 设备分组
     */
    DeviceGroupDO getDeviceGroup(Long id);

    /**
     * 校验分组是否存在
     *
     * @param id
     * @return
     */
    DeviceGroupDO validateDeviceGroupExists(Long id);

    /**
     * 获得设备分组分页
     *
     * @param pageReqVO 分页查询
     * @return 设备分组分页
     */
    PageResult<DeviceGroupDO> getDeviceGroupPage(DeviceGroupPageReqVO pageReqVO);

    /**
     * 资源空间id
     *
     * @param resourceSpaceId
     * @return
     */
    List<DeviceGroupDO> getDeviceGroupListByResourceSpaceId(Long resourceSpaceId);

    /**
     * 根据分组id查找资源空间id
     *
     * @param ids
     * @return
     */
    List<DeviceGroupDO> getDeviceGroupListByIds(List<Long> ids);

}