package cn.powerchina.bjy.link.iot.controller.admin.datapermissions.vo;


import lombok.*;

import java.util.*;

import io.swagger.v3.oas.annotations.media.Schema;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;

@Schema(description = "管理后台 - 数据权限分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DataPermissionsPageReqVO extends PageParam {

    @Schema(description = "角色ID", example = "27371")
    private Long roleId;

    @Schema(description = "资源空间id", example = "30079")
    private Long resourceSpaceId;

    @Schema(description = "设备ID或者边缘实例ID", example = "25685")
    private String deviceOrGatewayId;

    @Schema(description = "产品ID或者边缘计算ID", example = "12186")
    private String productOrEdgeId;

}