package cn.powerchina.bjy.link.iot.controller.admin.alarmtemplate.vo;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 告警模板分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AlarmTemplatePageReqVO extends PageParam {

    @Schema(description = "规则ID", example = "25655")
    private Long ruleId;

    @Schema(description = "告警名称", example = "王五")
    private String alarmName;

    @Schema(description = "告警等级(1-轻微,2-中等,3-严重,4-非常严重)")
    private Integer alarmLevel;

    @Schema(description = "告警描述")
    private String alarmContent;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
