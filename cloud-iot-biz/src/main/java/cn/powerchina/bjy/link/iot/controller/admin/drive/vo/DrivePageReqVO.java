package cn.powerchina.bjy.link.iot.controller.admin.drive.vo;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


@Schema(description = "管理后台 - 驱动分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DrivePageReqVO extends PageParam {

    @Schema(description = "驱动名称", example = "芋艿")
    private String driveName;

    @Schema(description = "驱动编码")
    private String driveCode;

    @Schema(description = "备注名称", example = "张三")
    private String remark;

    @Schema(description = "驱动版本")
    private String version;

    @Schema(description = "状态(1:运行中;0:未启动)", example = "2")
    private Integer status;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}