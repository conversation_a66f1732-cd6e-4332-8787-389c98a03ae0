package cn.powerchina.bjy.link.iot.controller.admin.scenerule.vo;

import cn.powerchina.bjy.link.iot.controller.admin.sceneruleaction.vo.SceneRuleActionSaveReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.sceneruletrigger.vo.SceneRuleTriggerSaveReqVO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Schema(description = "管理后台 - 场景规则新增/修改 Request VO")
@Data
public class RuleSaveReqVO extends SceneRuleSaveReqVO{

    @Schema(description = "触发条件")
    private List<SceneRuleTriggerSaveReqVO> ruleTriggerSaveList = new ArrayList<>();

    @Schema(description = "限制条件")
    private List<SceneRuleTriggerSaveReqVO> ruleConditionSaveList = new ArrayList<>();

    @Valid
    @Schema(description = "执行动作")
    private List<SceneRuleActionSaveReqVO> ruleActionSaveList = new ArrayList<>();

}