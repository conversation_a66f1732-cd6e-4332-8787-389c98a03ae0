package cn.powerchina.bjy.link.iot.listener.device;

import cn.powerchina.bjy.link.iot.controller.admin.deviceeventlog.vo.DeviceEventLogSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.device.DeviceDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.devicecurrentattribute.DeviceShadowDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.productmodel.ProductModelDO;
import cn.powerchina.bjy.link.iot.dto.message.DeviceEventModel;
import cn.powerchina.bjy.link.iot.enums.ShadowTypeEnum;
import cn.powerchina.bjy.link.iot.enums.TransportSourceTypeEnum;
import cn.powerchina.bjy.link.iot.framework.rule.RuleActionService;
import cn.powerchina.bjy.link.iot.service.device.DeviceService;
import cn.powerchina.bjy.link.iot.service.devicecurrentattribute.DeviceShadowService;
import cn.powerchina.bjy.link.iot.service.deviceeventlog.DeviceEventLogService;
import cn.powerchina.bjy.link.iot.service.productmodel.ProductModelService;
import cn.powerchina.bjy.link.iot.service.transportsource.TransportSourceService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeasy.rules.api.Facts;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;

/**
 * @Description: 监听设备上报的事件
 * @Author: handl
 * @CreateDate: 2025/07/23
 */
@Slf4j
@Component
public class DeviceEventHandler {

    @Resource
    private RuleActionService ruleActionService;

    @Resource
    private DeviceShadowService deviceShadowService;

    @Resource
    private ProductModelService productModelService;

    @Resource
    private DeviceService deviceService;

    @Resource
    private DeviceEventLogService eventLogService;

    @Resource
    private TransportSourceService transportSourceService;

    public void handler(DeviceEventModel deviceEventModel) {
        //设备影子，事件日志
        this.insertReportData(deviceEventModel);
        //规则匹配
        try {
            Facts facts = new Facts();
            facts.put("productCode", deviceEventModel.getProductCode());
            facts.put("deviceCode", deviceEventModel.getDeviceCode());
            facts.put("event", deviceEventModel.getThingIdentity());
            ruleActionService.matchRuleAndAction(facts);
        } catch (Exception e) {
            log.error("根据上报的事件，匹配规则异常：{}", e.getMessage());
        }
        //数据转发
        transportSourceService.dataForwarding(TransportSourceTypeEnum.DEVICE_EVENT_REPORT, deviceEventModel);
    }

    private void insertReportData(DeviceEventModel deviceEventModel) {
        //更新设备影子
        LocalDateTime reportTime = deviceEventModel.getCurrentTime() == null ? LocalDateTime.now() : Instant.ofEpochMilli(deviceEventModel.getCurrentTime())
                .atZone(ZoneId.systemDefault())
                .toLocalDateTime();
        List<DeviceShadowDO> shadowDOList = new ArrayList<>();
        DeviceShadowDO deviceShadowDO = deviceShadowService.getShadowByDeviceCodeAndThingIdentity(deviceEventModel.getDeviceCode(), "event");
        if (deviceShadowDO == null) {
            deviceShadowDO = new DeviceShadowDO();
            deviceShadowDO.setDeviceCode(deviceEventModel.getDeviceCode());
            deviceShadowDO.setProductCode(deviceEventModel.getProductCode());
            deviceShadowDO.setReportTime(reportTime);
            deviceShadowDO.setThingIdentity("event");
            deviceShadowDO.setThingValue(deviceEventModel.getThingIdentity());
            deviceShadowDO.setShadowType(ShadowTypeEnum.STATUS.getType());
            deviceShadowDO.setShadowPayload(JSON.toJSONString(deviceEventModel.getParams()));
            shadowDOList.add(deviceShadowDO);
            deviceShadowService.createShadow(shadowDOList);
        } else {
            deviceShadowDO.setReportTime(reportTime);
            deviceShadowDO.setThingIdentity("event");
            deviceShadowDO.setThingValue(deviceEventModel.getThingIdentity());
            deviceShadowDO.setShadowPayload(JSON.toJSONString(deviceEventModel.getParams()));
            deviceShadowDO.setUpdateTime(LocalDateTime.now());
            shadowDOList.add(deviceShadowDO);
            deviceShadowService.updateShadow(shadowDOList);
        }
        DeviceDO device = deviceService.getDevice(deviceEventModel.getDeviceCode());
        ProductModelDO productModelDO = productModelService.getProductModel(device.getProductCode(), deviceEventModel.getThingIdentity());

        // 事件日志的thingValue只能记录在物模型中配置的输出属性，上报上来的可能比配置的多
        List<String> thingIdentityList = new ArrayList<>();
        String outputParam = productModelDO.getOutputParams();
        if (StringUtils.isNotBlank(outputParam)) {
            JSON.parseArray(outputParam, JSONObject.class).forEach(j -> {
                thingIdentityList.add(j.getString("thingIdentity"));
            });
        }
        JSONObject newJsonObject = new JSONObject();
        JSONObject originJsonObject = JSON.parseObject(JSON.toJSONString(deviceEventModel.getParams()));
        for (String thingIdentity : thingIdentityList) {
            newJsonObject.put(thingIdentity, originJsonObject.get(thingIdentity));
        }

        //事件日志
        DeviceEventLogSaveReqVO eventLogVO = new DeviceEventLogSaveReqVO();
        eventLogVO.setDeviceCode(deviceEventModel.getDeviceCode());
        eventLogVO.setDeviceName(device.getDeviceName());
        eventLogVO.setThingIdentity(deviceEventModel.getThingIdentity());
        eventLogVO.setThingName(productModelDO.getThingName());
        eventLogVO.setEventType(Integer.valueOf(deviceEventModel.getEventType()));
        eventLogVO.setThingValue(JSON.toJSONString(newJsonObject));
        eventLogVO.setRemark(productModelDO.getRemark());
        eventLogService.createDeviceEventLog(eventLogVO);
    }

}
