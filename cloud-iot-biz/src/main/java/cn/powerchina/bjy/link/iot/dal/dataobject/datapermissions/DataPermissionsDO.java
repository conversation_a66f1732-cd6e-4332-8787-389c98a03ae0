package cn.powerchina.bjy.link.iot.dal.dataobject.datapermissions;

import cn.powerchina.bjy.cloud.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;
import java.util.*;
import com.baomidou.mybatisplus.annotation.*;

/**
 * 数据权限 DO
 *
 * <AUTHOR>
 */
@TableName("iot_data_permissions")
@KeySequence("iot_data_permissions_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DataPermissionsDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     *设备唯一标识
     */
    private String dataId;
    /**
     * 用户ID
     */
    private Long roleId;
    /**
     * 名称
     */
    private String name;
    /**
     * 父级ID
     */
    private String parentId;
    /**
     * 等级
     */
    private Integer level;


}