package cn.powerchina.bjy.link.iot.controller.admin.devicelog.vo;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 设备日志分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DeviceLogPageReqVO extends PageParam {

    @Schema(description = "设备编号")
    private String deviceCode;

    @Schema(description = "日志大小")
    private Integer fileSize;

    @Schema(description = "日志文件路径")
    private String path;

    @Schema(description = "备注", example = "你说的对")
    private String remark;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;
    @Schema(description = "日志级别(error=0,warn=1,info=2,debug=3,other=4)", example = "2")
    private Integer logLevel;

}