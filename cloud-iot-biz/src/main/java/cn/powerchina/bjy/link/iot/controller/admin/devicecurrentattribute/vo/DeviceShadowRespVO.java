package cn.powerchina.bjy.link.iot.controller.admin.devicecurrentattribute.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 设备上报的最新属性 Response VO")
@Data
@ExcelIgnoreUnannotated
public class DeviceShadowRespVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "13009")
    @ExcelProperty("主键id")
    private Long id;

    @Schema(description = "产品code", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("产品code")
    private String productCode;

    @Schema(description = "设备code", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("设备code")
    private String deviceCode;

    @Schema(description = "传感器模型code")
    @ExcelProperty("传感器模型code")
    private String modelCode;

    @Schema(description = "属性标识")
    @ExcelProperty("属性标识")
    private String thingIdentity;

    @Schema(description = "属性值")
    @ExcelProperty("属性值")
    private String thingValue;

    @Schema(description = "类型（0-属性，1-事件，2-状态变更）")
    @ExcelProperty("类型")
    private Integer shadowType;

    @Schema(description = "负载数据")
    @ExcelProperty("负载数据")
    private String shadowPayload;

    @Schema(description = "上报时间")
    @ExcelProperty("上报时间")
    private LocalDateTime reportTime;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}