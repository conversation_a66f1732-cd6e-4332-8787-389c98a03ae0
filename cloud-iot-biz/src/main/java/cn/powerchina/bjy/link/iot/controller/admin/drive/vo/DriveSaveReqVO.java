package cn.powerchina.bjy.link.iot.controller.admin.drive.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

@Schema(description = "管理后台 - 驱动新增/修改 Request VO")
@Data
public class DriveSaveReqVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "2468")
    private Long id;

    @Schema(description = "驱动名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @NotEmpty(message = "驱动名称不能为空")
    private String driveName;

    @Schema(description = "驱动编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "驱动编码不能为空")
    private String driveCode;

    @Schema(description = "备注名称", example = "张三")
    private String remark;

    @Schema(description = "驱动版本")
    private String version;

    @Schema(description = "状态(1:运行中;0:未启动)", example = "2")
    private Integer status;

}