package cn.powerchina.bjy.link.iot.strategy.impl;

import cn.powerchina.bjy.cloud.framework.common.util.json.JsonUtils;
import cn.powerchina.bjy.link.iot.dto.up.EdgeReadPropertyValue;
import cn.powerchina.bjy.link.iot.strategy.ProductModelStrategy;
import cn.powerchina.bjy.link.iot.strategy.vo.IntegerLimitVO;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 整数型规则校验
 * 校验值是否小于或大于配置区间
 *
 * <AUTHOR>
 **/
@Slf4j
@Component("intStrategy")
public class IntegerStrategy implements ProductModelStrategy {

    @Override
    public boolean shouldIgnore(Object context, String strategy) {
        if (StringUtils.isBlank(strategy) || null == context) {
            return false;
        }
        try {
            IntegerLimitVO limit = JsonUtils.parseObject(strategy, new TypeReference<>() {
            });
            if (null == limit) {
                return false;
            }
            int data = (int) context;
            return data < limit.getMin() || data > limit.getMax();
        } catch (Exception e) {
            log.error("物模型数据类型【int】配置转换异常，配置【{}】", strategy, e);
        }
        return true;
    }

    @Override
    public boolean shouldIgnore(String context, String strategy, EdgeReadPropertyValue.EdgeDevicePropertyValueDTO valueDTO) {
        if (StringUtils.isBlank(strategy) || null == context) {
            return false;
        }
        try {
            IntegerLimitVO limit = JsonUtils.parseObject(strategy, new TypeReference<>() {
            });
            if (null == limit) {
                return false;
            }
            int data = Integer.parseInt(context);
            return data < limit.getMin() || data > limit.getMax();
        } catch (Exception e) {
            log.error("物模型数据类型【int】配置转换异常，值【{}】配置【{}】", context, strategy, e);
        }
        return true;
    }
}
