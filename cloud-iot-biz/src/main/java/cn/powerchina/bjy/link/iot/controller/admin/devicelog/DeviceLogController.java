package cn.powerchina.bjy.link.iot.controller.admin.devicelog;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.link.iot.controller.admin.devicelog.vo.DeviceLogPageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.devicelog.vo.DeviceLogRespVO;
import cn.powerchina.bjy.link.iot.controller.admin.devicelog.vo.DeviceLogSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.devicelog.DeviceLogDO;
import cn.powerchina.bjy.link.iot.service.devicelog.DeviceLogService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 设备日志")
@RestController
@RequestMapping("/iot/device-log")
@Validated
public class DeviceLogController {

    @Resource
    private DeviceLogService deviceLogService;

    @PostMapping("/create")
    @Operation(summary = "创建设备日志")
//    @PreAuthorize("@ss.hasPermission('iot:device-log:create')")
    public CommonResult<Long> createDeviceLog(@Valid @RequestBody DeviceLogSaveReqVO createReqVO) {
        return success(deviceLogService.createDeviceLog(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新设备日志")
//    @PreAuthorize("@ss.hasPermission('iot:device-log:update')")
    public CommonResult<Boolean> updateDeviceLog(@Valid @RequestBody DeviceLogSaveReqVO updateReqVO) {
        deviceLogService.updateDeviceLog(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除设备日志")
    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('iot:device-log:delete')")
    public CommonResult<Boolean> deleteDeviceLog(@RequestParam("id") Long id) {
        deviceLogService.deleteDeviceLog(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得设备日志")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('iot:device-log:query')")
    public CommonResult<DeviceLogRespVO> getDeviceLog(@RequestParam("id") Long id) {
        DeviceLogDO deviceLog = deviceLogService.getDeviceLog(id);
        return success(BeanUtils.toBean(deviceLog, DeviceLogRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得设备日志分页")
//    @PreAuthorize("@ss.hasPermission('iot:device-log:query')")
    public CommonResult<PageResult<DeviceLogRespVO>> getDeviceLogPage(@Valid DeviceLogPageReqVO pageReqVO) {
        PageResult<DeviceLogDO> pageResult = deviceLogService.getDeviceLogPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, DeviceLogRespVO.class));
    }

}