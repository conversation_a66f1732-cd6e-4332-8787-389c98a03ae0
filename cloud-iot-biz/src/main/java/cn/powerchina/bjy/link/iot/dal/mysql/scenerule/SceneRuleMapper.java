package cn.powerchina.bjy.link.iot.dal.mysql.scenerule;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.iot.controller.admin.scenerule.vo.SceneRulePageReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.scenerule.SceneRuleDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 场景规则 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface SceneRuleMapper extends BaseMapperX<SceneRuleDO> {

    default PageResult<SceneRuleDO> selectPage(SceneRulePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<SceneRuleDO>()
                .likeIfPresent(SceneRuleDO::getRuleName, reqVO.getRuleName())
                .eqIfPresent(SceneRuleDO::getResourceSpaceId, reqVO.getResourceSpaceId())
                .eqIfPresent(SceneRuleDO::getStatus, reqVO.getStatus())
                .eqIfPresent(SceneRuleDO::getInhibition, reqVO.getInhibition())
                .eqIfPresent(SceneRuleDO::getEffectiveType, reqVO.getEffectiveType())
                .betweenIfPresent(SceneRuleDO::getEffectiveStartTime, reqVO.getEffectiveStartTime())
                .betweenIfPresent(SceneRuleDO::getEffectiveEndTime, reqVO.getEffectiveEndTime())
                .eqIfPresent(SceneRuleDO::getRepeatType, reqVO.getRepeatType())
                .betweenIfPresent(SceneRuleDO::getRepeatStartDate, reqVO.getRepeatStartDate())
                .betweenIfPresent(SceneRuleDO::getRepeatEndDate, reqVO.getRepeatEndDate())
                .eqIfPresent(SceneRuleDO::getRepeatWeekDays, reqVO.getRepeatWeekDays())
                .eqIfPresent(SceneRuleDO::getRuleExpression, reqVO.getRuleExpression())
                .eqIfPresent(SceneRuleDO::getRulePriority, reqVO.getRulePriority())
                .eqIfPresent(SceneRuleDO::getRuleDesc, reqVO.getRuleDesc())
                .betweenIfPresent(SceneRuleDO::getCreateTime, reqVO.getCreateTime())
                .inIfPresent(SceneRuleDO::getResourceSpaceId,reqVO.getResourceSpaceIds())
                .orderByDesc(SceneRuleDO::getCreateTime));
    }

}