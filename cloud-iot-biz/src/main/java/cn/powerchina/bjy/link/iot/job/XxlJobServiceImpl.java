package cn.powerchina.bjy.link.iot.job;

import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import java.util.HashMap;
import java.util.Map;

@Service
public class XxlJobServiceImpl implements XxlJobService {

    @Resource
    private XxlJobAdminClient xxlJobAdminClient;

    @Override
    public int addJob(JobCreateRequest jobCreateRequest) {
        XxlJobInfo jobInfo = new XxlJobInfo();
        jobInfo.setJobDesc(jobCreateRequest.getJobDesc());
        jobInfo.setAuthor(jobCreateRequest.getAuthor());
        jobInfo.setScheduleConf(jobCreateRequest.getCronExpression());
        return xxlJobAdminClient.addJob(jobInfo);

    }

    @Override
    public void removeJob(int jobId) {
        xxlJobAdminClient.removeJob(jobId);
    }
}
