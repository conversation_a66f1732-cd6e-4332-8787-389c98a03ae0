package cn.powerchina.bjy.link.iot.service.devicegroupdetail;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.link.iot.controller.admin.devicegroupdetail.bo.DeviceGroupDetailBO;
import cn.powerchina.bjy.link.iot.controller.admin.devicegroupdetail.vo.DeviceGroupDetailPageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.devicegroupdetail.vo.DeviceGroupDetailSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.devicegroupdetail.DeviceGroupDetailDO;
import jakarta.validation.Valid;

import java.util.List;
import java.util.Map;

/**
 * 设备分组明细 Service 接口
 *
 * <AUTHOR>
 */
public interface DeviceGroupDetailService {

    /**
     * 创建设备分组明细
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    void createDeviceGroupDetail(@Valid DeviceGroupDetailSaveReqVO createReqVO);

    /**
     * 删除设备分组明细
     *
     * @param id 编号
     */
    void deleteDeviceGroupDetail(Long id);

    /**
     * 获得设备分组明细
     *
     * @param id 编号
     * @return 设备分组明细
     */
    DeviceGroupDetailDO getDeviceGroupDetail(Long id);

    /**
     * 获得设备分组明细分页
     *
     * @param pageReqVO 分页查询
     * @return 设备分组明细分页
     */
    PageResult<DeviceGroupDetailDO> getDeviceGroupDetailPage(DeviceGroupDetailPageReqVO pageReqVO);

    /**
     * 获得设备分组明细分页
     *
     * @param pageReqVO
     * @return
     */
    PageResult<DeviceGroupDetailBO> getDeviceGroupDetailBOPage(DeviceGroupDetailPageReqVO pageReqVO);

    /**
     * 批量移除设备
     *
     * @param id
     */
    void batchDeleteDeviceGroupDetail(List<Long> id);

    /**
     * 根据分组id查找
     *
     * @param deviceGroupId
     * @return
     */
    Long selectCountByDeviceGroupId(Long deviceGroupId);

    /**
     * 删除设备
     *
     * @param deviceCodeDeleteList
     */
    void deleteDeviceGroupDetailByDeviceCode(List<String> deviceCodeDeleteList);

    /**
     * 批量查找设备所属分组id
     *
     * @param deviceCodeList
     * @return
     */
    Map<String, List<Long>> batchFindDeviceGroupId(List<String> deviceCodeList);
}