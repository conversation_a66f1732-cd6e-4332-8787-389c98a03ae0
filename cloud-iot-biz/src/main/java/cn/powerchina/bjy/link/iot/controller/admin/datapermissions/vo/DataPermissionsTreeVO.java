package cn.powerchina.bjy.link.iot.controller.admin.datapermissions.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "管理后台 - 数据权限 Response VO")
@Data
@ExcelIgnoreUnannotated
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DataPermissionsTreeVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private String id;

    @Schema(description = "名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "")
    private String name;

    @Schema(description = "父ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private String parentId;

    @Schema(description = "层级", example = "")
    private Integer level;

}
