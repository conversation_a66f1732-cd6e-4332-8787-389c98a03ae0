package cn.powerchina.bjy.link.iot.controller.admin.device.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 设备 Response VO")
@Data
@ExcelIgnoreUnannotated
public class DeviceRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "产品编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("产品编码")
    private String productCode;

    @Schema(description = "项目编码")
    @ExcelProperty("项目编码")
    private String projectCode;

    @Schema(description = "设备名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("设备名称")
    private String deviceName;

    @Schema(description = "设备编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("设备编号")
    private String deviceCode;

    @Schema(description = "父设备号（网关）")
    @ExcelProperty("父设备号（网关）")
    private String parentCode;

    @Schema(description = "网关名称")
    @ExcelProperty("网关名称")
    private String parentName;


    @Schema(description = "设备唯一标识")
    @ExcelProperty("设备唯一标识")
    private String deviceSerial;

    @Schema(description = "是否启用设备影子(0=禁用，1=启用)，默认启用")
    @ExcelProperty("是否启用设备影子(0=禁用，1=启用)，默认启用")
    private Boolean shadow;

    @Schema(description = "通道编码")
    @ExcelProperty("通道编码")
    private String channelCode;

    @Schema(description = "mcu通道号")
    @ExcelProperty("mcu通道号")
    private String mcuChannel;

    @Schema(description = "从站号")
    @ExcelProperty("从站号")
    private String slaveId;

    @Schema(description = "下发状态（0-未下发；1-已下发；）")
    @ExcelProperty("下发状态（0-未下发；1-已下发；）")
    private Integer distributeState;

    @Schema(description = "连接状态（0-离线；1-在线；）")
    @ExcelProperty("连接状态（0：未激活 1：离线 2；在线 3：禁用）")
    private Integer linkState;

    @Schema(description = "节点类型(0直连，1网关，2网关子设备）")
    @ExcelProperty("节点类型(0直连，1网关，2网关子设备）")
    private Integer nodeType;

    @Schema(description = "差异化扩展")
    @ExcelProperty("差异化扩展")
    private String extra;

    @Schema(description = "最后上线时间")
    @ExcelProperty("最后上线时间")
    private LocalDateTime lastUpTime;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "网关实例编码")
    @ExcelProperty("网关实例编码")
    private String edgeCode;

    @Schema(description = "网关实例名称")
    @ExcelProperty("网关实例名称")
    private String edgeName;

    @Schema(description = "驱动编码")
    @ExcelProperty("驱动编码")
    private String driverCode;

    @Schema(description = "产品名称")
    @ExcelProperty("产品名称")
    private String productName;

    @Schema(description = "描述")
    @ExcelProperty("描述")
    private String remark;

    @Schema(description = "设备密钥")
    @ExcelProperty("设备密钥")
    private String deviceSecret;

    @Schema(description = "启用状态（0:禁用 1：启用）")
    private Integer status;

    @Schema(description = "厂商")
    @ExcelProperty("厂商")
    private String firmName;

    @ExcelProperty("产品型号")
    @Schema(description = "产品型号")
    private String productModel;

    @Schema(description = "资源空间id")
    @ExcelProperty("资源空间id")
    private Long resourceSpaceId;

    @Schema(description = "资源空间名称")
    @ExcelProperty("资源空间名称")
    private String spaceName;

    @Schema(description = "经度")
    private String longitude;

    @Schema(description = "维度")
    private String latitude;
}