package cn.powerchina.bjy.link.iot.service.resourcespace;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.link.iot.controller.admin.resourcespace.vo.ResourceSpacePageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.resourcespace.vo.ResourceSpaceSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.resourcespace.ResourceSpaceDO;
import jakarta.validation.Valid;

import java.util.List;
import java.util.Map;

/**
 * 资源空间 Service 接口
 *
 * <AUTHOR>
 */
public interface ResourceSpaceService {

    /**
     * 创建资源空间
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createResourceSpace(@Valid ResourceSpaceSaveReqVO createReqVO);

    /**
     * 更新资源空间
     *
     * @param updateReqVO 更新信息
     */
    void updateResourceSpace(@Valid ResourceSpaceSaveReqVO updateReqVO);

    /**
     * 删除资源空间
     *
     * @param id 编号
     */
    void deleteResourceSpace(Long id);

    /**
     * 获得资源空间
     *
     * @param id 编号
     * @return 资源空间
     */
    ResourceSpaceDO getResourceSpace(Long id);

    /**
     * 获得资源空间分页
     *
     * @param pageReqVO 分页查询
     * @return 资源空间分页
     */
    PageResult<ResourceSpaceDO> getResourceSpacePage(ResourceSpacePageReqVO pageReqVO);

    /**
     * 查询所有空间
     * @return
     */
    List<ResourceSpaceDO> getResourceSpaceList();

    /**
     * 切换数据转发启用状态
     *
     * @param id
     * @param state
     * @return
     */
    boolean switchResourceSpaceState(Long id, Integer state);

    /**
     * 根据设备编码批量查找设备所属资源空间id
     *
     * @param deviceCodeList
     * @return
     */
    Map<String, List<Long>> batchFindDeviceResourceId(List<String> deviceCodeList);
}