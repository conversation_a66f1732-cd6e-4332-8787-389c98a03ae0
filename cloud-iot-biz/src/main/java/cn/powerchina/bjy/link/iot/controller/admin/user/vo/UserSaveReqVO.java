package cn.powerchina.bjy.link.iot.controller.admin.user.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

@Schema(description = "管理后台 - 用户信息新增/修改 Request VO")
@Data
public class UserSaveReqVO {

    @Schema(description = "主键id，system_user关联用户id", requiredMode = Schema.RequiredMode.REQUIRED, example = "13440")
    private Long id;

    @Schema(description = "用户账号", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @NotEmpty(message = "用户账号不能为空")
    private String username;

    @Schema(description = "密码",  example = "123456")
    @Length(max = 32)
    private String password;

    @Schema(description = "确认密码",  example = "123456")
    @Length(max = 32)
    private String confirmPassword;

    @Schema(description = "姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @NotEmpty(message = "姓名不能为空")
    private String name;

    @Schema(description = "手机号码")
    private String mobile;

    @Schema(description = "部门名称", example = "张三")
    private String deptName;

    @Schema(description = "职务", example = "李四")
    private String postName;

    @Schema(description = "用户邮箱")
    private String email;

    @Schema(description = "启用状态（0正常 1停用）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "启用状态（0正常 1停用）不能为空")
    private Integer status;

}