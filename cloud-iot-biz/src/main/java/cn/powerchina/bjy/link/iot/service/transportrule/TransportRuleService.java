package cn.powerchina.bjy.link.iot.service.transportrule;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.link.iot.controller.admin.transportrule.vo.TransportRulePageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.transportrule.vo.TransportRuleSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.transportrule.TransportRuleDO;
import cn.powerchina.bjy.link.iot.model.DeviceTransportModel;
import jakarta.validation.Valid;

import java.util.List;
import java.util.Map;

/**
 * 数据转发规则 Service 接口
 *
 * <AUTHOR>
 */
public interface TransportRuleService {

    /**
     * 创建数据转发规则
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createTransportRule(@Valid TransportRuleSaveReqVO createReqVO);

    /**
     * 更新数据转发规则
     *
     * @param updateReqVO 更新信息
     */
    void updateTransportRule(@Valid TransportRuleSaveReqVO updateReqVO);

    /**
     * 删除数据转发规则
     *
     * @param id 编号
     */
    void deleteTransportRule(Long id);

    /**
     * 获得数据转发规则
     *
     * @param id 编号
     * @return 数据转发规则
     */
    TransportRuleDO getTransportRule(Long id);



    /**
     * 获得数据转发规则分页
     *
     * @param pageReqVO 分页查询
     * @return 数据转发规则分页
     */
    PageResult<TransportRuleDO> getTransportRulePage(TransportRulePageReqVO pageReqVO);

    /**
     * 切换数据转发启用状态
     *
     * @param id    主键id
     * @param status 切换后的状态值
     * @return
     */
    boolean switchTransportRuleStatus(Long id, Integer status);
    /**
     * 数据转发
     *
     * @param transportModelList
     */
    void transportData(List<DeviceTransportModel> transportModelList);

    /**
     *
     * @param productCodeList
     * @return
     */
    Map<String,Long> findResourceIdByProductCode(List<String>productCodeList);
}