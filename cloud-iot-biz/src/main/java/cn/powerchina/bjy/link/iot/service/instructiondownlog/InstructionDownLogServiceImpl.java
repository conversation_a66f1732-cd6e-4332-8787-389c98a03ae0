package cn.powerchina.bjy.link.iot.service.instructiondownlog;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.cloud.system.api.user.AdminUserApi;
import cn.powerchina.bjy.cloud.system.api.user.dto.AdminUserRespDTO;
import cn.powerchina.bjy.link.iot.controller.admin.instructiondownlog.vo.InstructionDownLogPageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.instructiondownlog.vo.InstructionDownLogRespVO;
import cn.powerchina.bjy.link.iot.controller.admin.instructiondownlog.vo.InstructionDownLogSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.instructiondownlog.InstructionDownLogDO;
import cn.powerchina.bjy.link.iot.dal.mysql.instructiondownlog.InstructionDownLogMapper;
import cn.powerchina.bjy.link.iot.util.CodeGenerator;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.link.iot.enums.ErrorCodeConstants.INSTRUCTION_DOWN_LOG_NOT_EXISTS;

/**
 * 指令下发操作记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class InstructionDownLogServiceImpl implements InstructionDownLogService {

    @Resource
    private InstructionDownLogMapper instructionDownLogMapper;

    @Resource
    private AdminUserApi adminUserApi;

    @Override
    public Long createInstructionDownLog(InstructionDownLogSaveReqVO createReqVO) {
        createReqVO.setMessageId(CodeGenerator.createMsgUUID());//生成msgId
        // 插入
        InstructionDownLogDO instructionDownLog = BeanUtils.toBean(createReqVO, InstructionDownLogDO.class);
        instructionDownLogMapper.insert(instructionDownLog);
        // 返回
        return instructionDownLog.getId();
    }

    @Override
    public void updateInstructionDownLog(InstructionDownLogSaveReqVO updateReqVO) {
        // 校验存在
        validateInstructionDownLogExists(updateReqVO.getId());
        // 更新
        InstructionDownLogDO updateObj = BeanUtils.toBean(updateReqVO, InstructionDownLogDO.class);
        instructionDownLogMapper.updateById(updateObj);
    }

    @Override
    public void deleteInstructionDownLog(Long id) {
        // 校验存在
        validateInstructionDownLogExists(id);
        // 删除
        instructionDownLogMapper.deleteById(id);
    }

    private void validateInstructionDownLogExists(Long id) {
        if (instructionDownLogMapper.selectById(id) == null) {
            throw exception(INSTRUCTION_DOWN_LOG_NOT_EXISTS);
        }
    }

    @Override
    public InstructionDownLogDO getInstructionDownLog(Long id) {
        return instructionDownLogMapper.selectById(id);
    }

    @Override
    public InstructionDownLogDO getByMessageId(String messageId) {
        List<InstructionDownLogDO> instructionDownLogDOList = instructionDownLogMapper.selectList(new LambdaQueryWrapperX<InstructionDownLogDO>().eq(InstructionDownLogDO::getMessageId, messageId));
        if (!CollectionUtils.isEmpty(instructionDownLogDOList)) {
            return instructionDownLogDOList.get(0);
        } else {
            return null;
        }
    }

    /**
     * 根据设备编码和物模型标识符获取最新的指令下发操作记录
     * @param deviceCode 设备编码
     * @param thingIdentity 物模型标识符
     * @return 指令下发操作记录
     */
    @Override
    public InstructionDownLogDO getLatestByDeviceCodeAndThingIdentity(String deviceCode, String thingIdentity) {
        List<InstructionDownLogDO> instructionDownLogDOList = instructionDownLogMapper.selectList(
                new LambdaQueryWrapperX<InstructionDownLogDO>()
                        .eq(InstructionDownLogDO::getDeviceCode, deviceCode)
                        .eq(InstructionDownLogDO::getThingIdentity, thingIdentity)
        );
        if (!CollectionUtils.isEmpty(instructionDownLogDOList)) {
            return instructionDownLogDOList.get(0);
        }
        return null;
    }

    @Override
    public PageResult<InstructionDownLogDO> getInstructionDownLogPage(InstructionDownLogPageReqVO pageReqVO) {
        return instructionDownLogMapper.selectPage(pageReqVO);
    }

    @Override
    public PageResult<InstructionDownLogRespVO> getInstructionDownLogVOPage(InstructionDownLogPageReqVO pageReqVO) {
        PageResult<InstructionDownLogDO> doPageResult = instructionDownLogMapper.selectPage(pageReqVO);
        PageResult<InstructionDownLogRespVO> voPageResult = BeanUtils.toBean(doPageResult, InstructionDownLogRespVO.class);
        //填充用户名
        if (!CollectionUtils.isEmpty(voPageResult.getList())) {
            CommonResult<List<AdminUserRespDTO>> getUserList = adminUserApi.getUserList(voPageResult.getList().stream().map(InstructionDownLogRespVO::getCreator).filter(StringUtils::isNotBlank).map(Long::valueOf).distinct().collect(Collectors.toList()));
            List<AdminUserRespDTO> userList = getUserList.getCheckedData();
            if (!CollectionUtils.isEmpty(userList)) {
                Map<Long, String> userNameMap = new HashMap<>(userList.stream().collect(Collectors.toMap(AdminUserRespDTO::getId, AdminUserRespDTO::getName)));
                voPageResult.getList().forEach(item -> {
                    if (StringUtils.isNotBlank(item.getCreator())) {
                        item.setCreatorName(userNameMap.get(Long.valueOf(item.getCreator())));
                    }
                });
            }
        }
        return voPageResult;
    }

    @Override
    public String createInstructionDown(InstructionDownLogSaveReqVO createReqVO) {
        createReqVO.setMessageId(CodeGenerator.createMsgUUID());//生成msgId
        // 插入
        InstructionDownLogDO instructionDownLog = BeanUtils.toBean(createReqVO, InstructionDownLogDO.class);
        instructionDownLogMapper.insert(instructionDownLog);
        // 返回
        return instructionDownLog.getMessageId();
    }

    @Override
    public void updateInstructionLogByMsgId(String messageId, String outParams, Integer downConsumeTime, Integer upConsumeTime) {
        UpdateWrapper<InstructionDownLogDO> wrapper = new UpdateWrapper<>();
        wrapper.lambda()
                .set(InstructionDownLogDO::getOutputParams, outParams)
                .set(InstructionDownLogDO::getDownConsumeTime, downConsumeTime)
                .set(InstructionDownLogDO::getUpConsumeTime, upConsumeTime)
                .eq(InstructionDownLogDO::getMessageId, messageId);
        instructionDownLogMapper.update(wrapper);
    }

}