package cn.powerchina.bjy.link.iot.dal.mysql.device;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.MPJLambdaWrapperX;
import cn.powerchina.bjy.link.iot.controller.admin.device.vo.DeviceAndProductVO;
import cn.powerchina.bjy.link.iot.controller.admin.device.vo.DevicePageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.edgegateway.bo.EdgeGatewayBO;
import cn.powerchina.bjy.link.iot.controller.admin.edgegateway.vo.*;
import cn.powerchina.bjy.link.iot.dal.dataobject.device.DeviceDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.drive.DriveDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.product.ProductDO;
import cn.powerchina.bjy.link.iot.enums.DeviceFlagEnum;
import cn.powerchina.bjy.link.iot.enums.DeviceTypeEnum;
import cn.powerchina.bjy.link.iot.enums.LinkStateEnum;
import cn.powerchina.bjy.link.iot.enums.NodeTypeEnum;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.dromara.hutool.core.reflect.FieldUtil;
import org.springframework.util.CollectionUtils;

import java.util.List;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.link.iot.enums.ErrorCodeConstants.DEVICE_ORDERCOLUMN_NOT_EXISTS;

/**
 * 设备 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface DeviceMapper extends BaseMapperX<DeviceDO> {

    void saveDevice(@Param("deviceDO") DeviceDO deviceDO);

    default PageResult<DeviceDO> selectPage(DevicePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<DeviceDO>()
                .eqIfPresent(DeviceDO::getProductCode, reqVO.getProductCode())
                .eqIfPresent(DeviceDO::getProjectCode, reqVO.getProjectCode())
                .likeIfPresent(DeviceDO::getDeviceName, reqVO.getDeviceName())
                .eqIfPresent(DeviceDO::getEdgeCode, reqVO.getEdgeCode())
                .eqIfPresent(DeviceDO::getDeviceCode, reqVO.getDeviceCode())
                .eqIfPresent(DeviceDO::getParentCode, reqVO.getParentCode())
                .eqIfPresent(DeviceDO::getDeviceSerial, reqVO.getDeviceSerial())
                .eqIfPresent(DeviceDO::getShadow, reqVO.getShadow())
                .eqIfPresent(DeviceDO::getChannelCode, reqVO.getChannelCode())
                .eqIfPresent(DeviceDO::getDistributeState, reqVO.getDistributeState())
                .eqIfPresent(DeviceDO::getLinkState, reqVO.getLinkState())
                .eqIfPresent(DeviceDO::getStatus, reqVO.getStatus())
                .eqIfPresent(DeviceDO::getNodeType, reqVO.getNodeType())
                .eqIfPresent(DeviceDO::getExtra, reqVO.getExtra())
                .betweenIfPresent(DeviceDO::getLastUpTime, reqVO.getLastUpTime())
                .betweenIfPresent(DeviceDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(DeviceDO::getId));
    }

    /**
     * 查询设备分页并关联产品名称
     *
     * @param reqVO
     * @return
     */
    default PageResult<DeviceAndProductVO> selectDeviceAndProductPage(DevicePageReqVO reqVO) {
        MPJLambdaWrapperX<DeviceDO> wrapper = (MPJLambdaWrapperX<DeviceDO>) new MPJLambdaWrapperX<DeviceDO>()
                .selectAll(DeviceDO.class)
                .selectAs(ProductDO::getProductName, DeviceAndProductVO::getProductName)
                .selectAs(ProductDO::getResourceSpaceId, DeviceAndProductVO::getResourceSpaceId)
                .eqIfPresent(DeviceDO::getProductCode, reqVO.getProductCode())
                .eqIfPresent(DeviceDO::getProjectCode, reqVO.getProjectCode())
                .likeIfPresent(DeviceDO::getDeviceName, reqVO.getDeviceName())
                .eqIfPresent(DeviceDO::getEdgeCode, reqVO.getEdgeCode())
                .eqIfPresent(DeviceDO::getParentCode, reqVO.getParentCode())
                .likeIfPresent(DeviceDO::getDeviceSerial, reqVO.getDeviceSerial())
                .eqIfPresent(DeviceDO::getShadow, reqVO.getShadow())
                .eqIfPresent(DeviceDO::getChannelCode, reqVO.getChannelCode())
                .eqIfPresent(DeviceDO::getDistributeState, reqVO.getDistributeState())
                .eqIfPresent(DeviceDO::getStatus, reqVO.getStatus())
                .eqIfPresent(DeviceDO::getNodeType, reqVO.getNodeType())
                .eqIfPresent(DeviceDO::getExtra, reqVO.getExtra())
                .eqIfPresent(DeviceDO::getDriverCode, reqVO.getDriverCode())
                .eqIfPresent(DeviceDO::getRemark, reqVO.getRemark())
                .eqIfPresent(DeviceDO::getDeviceType, reqVO.getIsSceneRule() == null ? DeviceTypeEnum.DEVICE.getType() : null)//如果是场景联动，则不区分边缘设备和普通设备
                .betweenIfPresent(DeviceDO::getLastUpTime, reqVO.getLastUpTime())
                .betweenIfPresent(DeviceDO::getCreateTime, reqVO.getCreateTime())
                .leftJoin(ProductDO.class, "p", on -> on.eq(ProductDO::getProductCode, DeviceDO::getProductCode))
                .eqIfExists(ProductDO::getResourceSpaceId, reqVO.getResourceSpaceId());

        if (StringUtils.isNotBlank(reqVO.getProductName())) {
            wrapper.like(ProductDO::getProductName, "%" + reqVO.getProductName() + "%");
        }

        if (LinkStateEnum.IS_DISABLE.getType().equals(reqVO.getLinkState())) {
            wrapper.eqIfPresent(DeviceDO::getStatus, 0);
        } else {
            wrapper.eqIfPresent(DeviceDO::getLinkState, reqVO.getLinkState());
        }

        if (!CollectionUtils.isEmpty(reqVO.getCodes())) {
            wrapper.in(DeviceDO::getId, reqVO.getCodes());
        }
        //过滤已分组的设备
        if (CollectionUtil.isNotEmpty(reqVO.getDeviceCodeList())) {
            wrapper.notIn(DeviceDO::getDeviceCode, reqVO.getDeviceCodeList());
        }

        if (StringUtils.isBlank(reqVO.getDeviceFlag())) {
            wrapper.likeIfPresent(DeviceDO::getDeviceCode, reqVO.getDeviceCode());
        }

        if (DeviceFlagEnum.DEVICE_ADD.getType().equals(reqVO.getDeviceFlag())) {
            wrapper.eq(DeviceDO::getParentCode, "");
            wrapper.ne(DeviceDO::getNodeType, NodeTypeEnum.DIRECT.getType());
            wrapper.ne(DeviceDO::getDeviceCode, reqVO.getDeviceCode());
        }

        if (DeviceFlagEnum.DEVICE_REPLACE.getType().equals(reqVO.getDeviceFlag())) {
            wrapper.ne(DeviceDO::getDeviceCode, reqVO.getDeviceCode());
        }

        if (reqVO.getNeNodeType() != null) {
            wrapper.ne(DeviceDO::getNodeType, NodeTypeEnum.EDGE_SUB.getType());
        }

        if (StringUtils.isNotBlank(reqVO.getOrderColumn())) {
            //字段小驼峰格式转数据库字段名格式
            String[] split = reqVO.getOrderColumn().split("#");
            if (!FieldUtil.hasField(DeviceDO.class, split[0])) {//校验是否存在这个字段
                throw exception(DEVICE_ORDERCOLUMN_NOT_EXISTS);
            }
            wrapper.orderBy(true, "asc".equals(split[1]), StringUtils.camelToUnderline(split[0]));
        } else {
            wrapper.orderByDesc(DeviceDO::getId);
        }

        return selectJoinPage(reqVO, DeviceAndProductVO.class, wrapper);
    }

    default List<DeviceAndProductVO> selectDeviceAndProductList(DevicePageReqVO reqVO) {
        MPJLambdaWrapperX<DeviceDO> wrapper = (MPJLambdaWrapperX<DeviceDO>) new MPJLambdaWrapperX<DeviceDO>()
                .selectAs(DeviceDO::getId, DeviceAndProductVO::getId)
                .selectAs(ProductDO::getProductName, DeviceAndProductVO::getProductName)
                .selectAs(ProductDO::getResourceSpaceId, DeviceAndProductVO::getResourceSpaceId)
                .eqIfPresent(DeviceDO::getDeviceType, DeviceTypeEnum.EDGE.getType())
                .leftJoin(ProductDO.class, "p", on -> on.eq(ProductDO::getProductCode, DeviceDO::getProductCode))
                .eqIfExists(ProductDO::getResourceSpaceId, reqVO.getResourceSpaceId());

        if (StringUtils.isNotBlank(reqVO.getProductName())) {
            wrapper.like(ProductDO::getProductName, "%" + reqVO.getProductName() + "%");
        }
        if (!CollectionUtils.isEmpty(reqVO.getCodes())) {
            wrapper.in(DeviceDO::getId, reqVO.getCodes());
        }
        return selectJoinList(DeviceAndProductVO.class, wrapper);
    }


    /**
     * 分页查询edge实例设备
     *
     * @param reqVO
     * @return
     */
    default PageResult<EdgeGatewayBO> listEdge(EdgeGatewayPageReqVO reqVO) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(reqVO.getCodes())) {
            MPJLambdaWrapperX<DeviceDO> wrapper = (MPJLambdaWrapperX<DeviceDO>) new MPJLambdaWrapperX<DeviceDO>()
                    .selectAs(DeviceDO::getId, EdgeGatewayBO::getId)
                    .selectAs(DeviceDO::getDeviceCode, EdgeGatewayBO::getEdgeCode)
                    .selectAs(DeviceDO::getDeviceName, EdgeGatewayBO::getEdgeName)
                    .selectAs(DeviceDO::getExtra, EdgeGatewayBO::getExtra)
                    .selectAs(DeviceDO::getRemark, EdgeGatewayBO::getDescription)
                    .selectAs(DeviceDO::getDeviceSerial, EdgeGatewayBO::getEdgeIdentifier)
                    .selectAs(DeviceDO::getLinkState, EdgeGatewayBO::getEdgeStatus)
                    .selectAs(DeviceDO::getRegisterTime, EdgeGatewayBO::getActiveTime)
                    .selectAs(DeviceDO::getCreateTime, EdgeGatewayBO::getCreateTime)
                    .selectAs(DeviceDO::getLastUpTime, EdgeGatewayBO::getOnlineTime)
                    .selectAs(ProductDO::getProductName, EdgeGatewayBO::getProductName)
                    .selectAs(ProductDO::getProductCode, EdgeGatewayBO::getProductCode)
                    .selectAs(ProductDO::getId, EdgeGatewayBO::getProductId)
                    .selectAs(ProductDO::getResourceSpaceId, EdgeGatewayBO::getResourceSpaceId)
                    .eq(DeviceDO::getDeviceType, 0)
                    .likeIfPresent(DeviceDO::getDeviceName, reqVO.getEdgeName())
                    .leftJoin(ProductDO.class, "p", on -> on.eq(ProductDO::getProductCode, DeviceDO::getProductCode))
                    .eqIfExists(ProductDO::getResourceSpaceId, reqVO.getResourceSpaceId()).orderByDesc(DeviceDO::getId);
            return selectJoinPage(reqVO, EdgeGatewayBO.class, wrapper);
        } else {
            MPJLambdaWrapperX<DeviceDO> wrapper = (MPJLambdaWrapperX<DeviceDO>) new MPJLambdaWrapperX<DeviceDO>()
                    .selectAs(DeviceDO::getId, EdgeGatewayBO::getId)
                    .selectAs(DeviceDO::getDeviceCode, EdgeGatewayBO::getEdgeCode)
                    .selectAs(DeviceDO::getDeviceName, EdgeGatewayBO::getEdgeName)
                    .selectAs(DeviceDO::getExtra, EdgeGatewayBO::getExtra)
                    .selectAs(DeviceDO::getRemark, EdgeGatewayBO::getDescription)
                    .selectAs(DeviceDO::getDeviceSerial, EdgeGatewayBO::getEdgeIdentifier)
                    .selectAs(DeviceDO::getLinkState, EdgeGatewayBO::getEdgeStatus)
                    .selectAs(DeviceDO::getRegisterTime, EdgeGatewayBO::getActiveTime)
                    .selectAs(DeviceDO::getCreateTime, EdgeGatewayBO::getCreateTime)
                    .selectAs(DeviceDO::getLastUpTime, EdgeGatewayBO::getOnlineTime)
                    .selectAs(ProductDO::getProductName, EdgeGatewayBO::getProductName)
                    .selectAs(ProductDO::getProductCode, EdgeGatewayBO::getProductCode)
                    .selectAs(ProductDO::getId, EdgeGatewayBO::getProductId)
                    .selectAs(ProductDO::getResourceSpaceId, EdgeGatewayBO::getResourceSpaceId)
                    .inIfPresent(DeviceDO::getId, reqVO.getCodes())
                    .eq(DeviceDO::getDeviceType, 0)
                    .likeIfPresent(DeviceDO::getDeviceName, reqVO.getEdgeName())
                    .leftJoin(ProductDO.class, "p", on -> on.eq(ProductDO::getProductCode, DeviceDO::getProductCode))
                    .eqIfExists(ProductDO::getResourceSpaceId, reqVO.getResourceSpaceId()).orderByDesc(DeviceDO::getId);
            return selectJoinPage(reqVO, EdgeGatewayBO.class, wrapper);
        }
    }

    /**
     * 分页查询边缘实例下一级的设备
     *
     * @param reqVO
     * @return
     */
    default PageResult<EdgeDevicePageResVO> listEdgeDevice(EdgeDevicePageReqVO reqVO) {
        MPJLambdaWrapperX<DeviceDO> wrapper = (MPJLambdaWrapperX<DeviceDO>) new MPJLambdaWrapperX<DeviceDO>()
                .selectAs(DeviceDO::getId, EdgeDevicePageResVO::getId)
                .selectAs(DeviceDO::getDeviceName, EdgeDevicePageResVO::getDeviceName)
                .selectAs(DeviceDO::getDeviceCode, EdgeDevicePageResVO::getDeviceCode)
                .selectAs(DeviceDO::getDeviceSerial, EdgeDevicePageResVO::getDeviceSerial)
                .selectAs(ProductDO::getProductCode, EdgeDevicePageResVO::getProductCode)
                .selectAs(ProductDO::getProductName, EdgeDevicePageResVO::getProductName)
                .selectAs(DeviceDO::getNodeType, EdgeDevicePageResVO::getNodeType)
                .selectAs(DeviceDO::getLinkState, EdgeDevicePageResVO::getLinkState)
                .selectAs(DriveDO::getDriveCode, EdgeDevicePageResVO::getDriveCode)
                .selectAs(DriveDO::getDriveName, EdgeDevicePageResVO::getDriveName)
                .eq(DeviceDO::getDeviceType, 1)
                .eq(DeviceDO::getEdgeCode, reqVO.getEdgeCode())
                .eqIfPresent(DeviceDO::getProductCode, reqVO.getProductCode())
                .eqIfPresent(DeviceDO::getDeviceCode, reqVO.getDeviceCode())
                .eqIfPresent(DeviceDO::getDeviceSerial, reqVO.getDeviceSerial())
                .and(temp -> temp.isNull(DeviceDO::getParentCode).or().eq(DeviceDO::getParentCode, ""))
                .leftJoin(ProductDO.class, "p", on -> on.eq(ProductDO::getProductCode, DeviceDO::getProductCode))
                .leftJoin(DriveDO.class, "d", on -> on.eq(DriveDO::getDriveCode, DeviceDO::getDriverCode));
        return selectJoinPage(reqVO, EdgeDevicePageResVO.class, wrapper);

    }

    /**
     * 分页查询边缘实例下一级的设备的子设备
     *
     * @param reqVO
     * @return
     */
    default PageResult<EdgeChildDevicePageResVO> listEdgeChildDevice(EdgeChildDevicePageReqVO reqVO) {
        MPJLambdaWrapperX<DeviceDO> wrapper = (MPJLambdaWrapperX<DeviceDO>) new MPJLambdaWrapperX<DeviceDO>()
                .selectAs(DeviceDO::getId, EdgeDevicePageResVO::getId)
                .selectAs(DeviceDO::getDeviceName, EdgeDevicePageResVO::getDeviceName)
                .selectAs(DeviceDO::getDeviceCode, EdgeDevicePageResVO::getDeviceCode)
                .selectAs(DeviceDO::getDeviceSerial, EdgeDevicePageResVO::getDeviceSerial)
                .selectAs(ProductDO::getProductCode, EdgeDevicePageResVO::getProductCode)
                .selectAs(ProductDO::getProductName, EdgeDevicePageResVO::getProductName)
                .selectAs(DeviceDO::getNodeType, EdgeDevicePageResVO::getNodeType)
                .selectAs(DeviceDO::getLinkState, EdgeDevicePageResVO::getLinkState)
                .selectAs(DriveDO::getDriveCode, EdgeDevicePageResVO::getDriveCode)
                .selectAs(DriveDO::getDriveName, EdgeDevicePageResVO::getDriveName)
                .eq(DeviceDO::getDeviceType, 1)
                .eq(DeviceDO::getEdgeCode, reqVO.getEdgeCode())
                .eq(DeviceDO::getParentCode, reqVO.getParentCode())
                .eqIfPresent(DeviceDO::getProductCode, reqVO.getProductCode())
                .eqIfPresent(DeviceDO::getDeviceCode, reqVO.getDeviceCode())
                .eqIfPresent(DeviceDO::getDeviceSerial, reqVO.getDeviceSerial())
                .leftJoin(ProductDO.class, "p", on -> on.eq(ProductDO::getProductCode, DeviceDO::getProductCode))
                .leftJoin(DriveDO.class, "d", on -> on.eq(DriveDO::getDriveCode, DeviceDO::getDriverCode));
        return selectJoinPage(reqVO, EdgeChildDevicePageResVO.class, wrapper);

    }

    default Long selectCountByEdgeCode(String edgeCode) {
        return selectCount(new LambdaQueryWrapperX<DeviceDO>().eq(DeviceDO::getEdgeCode, edgeCode));
    }

    @Update("update iot_device set register_state = #{state},register_time = #{registerTime} where device_code = #{deviceCode} and register_state = 0")
    void updateRegisterState(@Param("deviceCode") String deviceCode, @Param("state") Integer state, @Param("registerTime") DateTime registerTime);

    @Update("update iot_device set link_state = #{linkState},register_time = #{registerTime} where id = #{id}")
    void updateLinkStateWithRegisterTime(@Param("id") Long id, @Param("linkState") Integer state, @Param("registerTime") DateTime registerTime);

    @Update("update iot_device set deleted = #{deleted} where parent_code = #{parentCode} and deleted != #{deleted}")
    void deleteByParentCode(@Param("parentCode") String parentCode, @Param("deleted") Boolean deleted);

    @Select("select * from iot_device where device_code = #{deviceCode} and deleted = 0")
    DeviceDO selectByCode(@Param("deviceCode") String deviceCode);

    @Update("update iot_device set link_state = #{state} where device_code = #{deviceCode}")
    void updateLinkState(@Param("deviceCode") String deviceCode, @Param("state") Integer state);

    @Update("update iot_device set link_state = #{linkState} where edge_code = #{edgeCode}")
    Integer updateLinkStateByEdgeCode(@Param("edgeCode") String edgeCode, @Param("linkState") Integer linkState);

    @Select("select device_code from iot_device where device_serial = #{deviceSerial} and deleted = 1 LIMIT 1")
    String selectCodeIsDelBySerial(@Param("deviceSerial") String deviceSerial);
}