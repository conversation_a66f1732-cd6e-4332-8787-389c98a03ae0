package cn.powerchina.bjy.link.iot.dal.mysql.drive;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.iot.controller.admin.drive.vo.DrivePageReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.drive.DriveDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 驱动 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface DriveMapper extends BaseMapperX<DriveDO> {

    default PageResult<DriveDO> selectPage(DrivePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<DriveDO>()
                .likeIfPresent(DriveDO::getDriveName, reqVO.getDriveName())
                .eqIfPresent(DriveDO::getDriveCode, reqVO.getDriveCode())
                .likeIfPresent(DriveDO::getRemark, reqVO.getRemark())
                .eqIfPresent(DriveDO::getVersion, reqVO.getVersion())
                .eqIfPresent(DriveDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(DriveDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(DriveDO::getId));
    }

}