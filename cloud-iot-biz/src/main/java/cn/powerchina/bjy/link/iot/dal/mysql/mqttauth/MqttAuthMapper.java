package cn.powerchina.bjy.link.iot.dal.mysql.mqttauth;


import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.iot.controller.admin.mqttauth.vo.MqttAuthPageReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.mqttauth.MqttAuthDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * mqtt认证 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface MqttAuthMapper extends BaseMapperX<MqttAuthDO> {

    default PageResult<MqttAuthDO> selectPage(MqttAuthPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<MqttAuthDO>()
                .likeIfPresent(MqttAuthDO::getUserName, reqVO.getUserName())
                .eqIfPresent(MqttAuthDO::getSecret, reqVO.getSecret())
                .eqIfPresent(MqttAuthDO::getType, reqVO.getType())
                .eqIfPresent(MqttAuthDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(MqttAuthDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(MqttAuthDO::getId));
    }

}