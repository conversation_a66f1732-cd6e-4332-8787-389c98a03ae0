package cn.powerchina.bjy.link.iot.framework.redis;

import cn.powerchina.bjy.link.iot.framework.rule.RuleUpdateSubscriber;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.listener.ChannelTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;

@Configuration
public class RedisPubSubConfig {
    
    @Resource
    private RedisConnectionFactory connectionFactory;
    
    @Resource
    private RuleUpdateSubscriber redisMessageSubscriber;
    
    @Bean
    public RedisMessageListenerContainer redisContainer() {
        RedisMessageListenerContainer container = new RedisMessageListenerContainer();
        container.setConnectionFactory(connectionFactory);
        container.addMessageListener(redisMessageSubscriber, channelTopic());
        return container;
    }
    
    @Bean
    public ChannelTopic channelTopic() {
        return new ChannelTopic("RULE_UPDATE_TOPIC");
    }
}