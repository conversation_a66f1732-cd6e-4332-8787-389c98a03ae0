package cn.powerchina.bjy.link.iot.controller.admin.notificationrecord.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 消息记录 Response VO")
@Data
@ExcelIgnoreUnannotated
public class NotificationRecordRespVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "27681")
    @ExcelProperty("主键id")
    private Long id;

    @Schema(description = "告警id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long alarmId;

    @Schema(description = "告警详情id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long alarmDetailId;

    @Schema(description = "主题")
    @ExcelProperty("主题")
    private String subject;

    @Schema(description = "收件箱")
    @ExcelProperty("收件箱")
    private String inbox;

    @Schema(description = "发送状态，1成功 0失败", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("发送状态，1成功 0失败")
    private Boolean sendStatus;

    @Schema(description = "发件箱")
    @ExcelProperty("收件箱")
    private String outbox;

    @Schema(description = "发送时间")
    @ExcelProperty("发送时间")
    private LocalDateTime sendTime;

    @Schema(description = "邮件内容")
    @ExcelProperty("邮件内容")
    private String emailContent;

    @Schema(description = "备注", example = "你猜")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}