package cn.powerchina.bjy.link.iot.dal.dataobject.resourcespace;

import cn.powerchina.bjy.cloud.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 资源空间 DO
 *
 * <AUTHOR>
 */
@TableName("iot_resource_space")
@KeySequence("iot_resource_space_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ResourceSpaceDO extends BaseDO {

    /**
     * 主键id
     */
    @TableId
    private Long id;
    /**
     * 空间名称
     */
    private String spaceName;
    /**
     * 空间APPID
     */
    private String spaceAppid;

    /**
     * 启用状态（0不启用，1启用，默认0）
     */
    private Integer state;

    /**
     * 是否勾选所有产品按钮
     */
    private Boolean isAllProducts;
    /**
     * 是否勾选所有边缘实例按钮
     */
    private Boolean isAllGateway;

}