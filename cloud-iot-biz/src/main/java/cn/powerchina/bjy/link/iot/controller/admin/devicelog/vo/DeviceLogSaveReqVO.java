package cn.powerchina.bjy.link.iot.controller.admin.devicelog.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(description = "管理后台 - 设备日志新增/修改 Request VO")
@Data
public class DeviceLogSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "17775")
    private Long id;

    @Schema(description = "设备编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "设备编号不能为空")
    private String deviceCode;

    @Schema(description = "日志级别(error=0,warn=1,info=2,debug=3,other=4)", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private Integer logLevel;

    @Schema(description = "日志大小", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "日志大小不能为空")
    private Integer fileSize;

    @Schema(description = "日志文件路径", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "日志文件路径不能为空")
    private String path;

    @Schema(description = "备注", example = "你说的对")
    private String remark;

}