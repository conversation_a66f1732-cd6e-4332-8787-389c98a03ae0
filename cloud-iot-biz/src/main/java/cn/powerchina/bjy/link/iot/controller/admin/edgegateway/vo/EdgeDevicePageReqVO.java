package cn.powerchina.bjy.link.iot.controller.admin.edgegateway.vo;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理后台 - 边缘网关设备分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class EdgeDevicePageReqVO extends PageParam {

    @Schema(description = "边缘实例编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "边缘实例编码不能为空")
    private String edgeCode;

    @Schema(description = "产品编码")
    private String productCode;

    @Schema(description = "设备ID")
    private String deviceCode;

    @Schema(description = "设备唯一标识")
    private String deviceSerial;
}
