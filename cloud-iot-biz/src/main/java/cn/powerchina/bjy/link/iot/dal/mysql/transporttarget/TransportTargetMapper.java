package cn.powerchina.bjy.link.iot.dal.mysql.transporttarget;


import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.iot.controller.admin.transporttarget.vo.TransportTargetPageReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.transporttarget.TransportTargetDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 转发规则-转发目标 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TransportTargetMapper extends BaseMapperX<TransportTargetDO> {

    default PageResult<TransportTargetDO> selectPage(TransportTargetPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<TransportTargetDO>()
                .eqIfPresent(TransportTargetDO::getRuleId, reqVO.getRuleId())
                .likeIfPresent(TransportTargetDO::getName, reqVO.getName())
                .eqIfPresent(TransportTargetDO::getTransportType, reqVO.getTransportType())
                .eqIfPresent(TransportTargetDO::getUrlAddress, reqVO.getUrlAddress())
                .eqIfPresent(TransportTargetDO::getToken, reqVO.getToken())
                .eqIfPresent(TransportTargetDO::getTopic, reqVO.getTopic())
                .betweenIfPresent(TransportTargetDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(TransportTargetDO::getId));
    }

}