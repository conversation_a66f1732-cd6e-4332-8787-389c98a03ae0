package cn.powerchina.bjy.link.iot.controller.admin.device.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.util.Date;

/**
 * @Description: 描述
 * @Author: yhx
 * @CreateDate: 2024/8/19
 */
@Data
@Builder
public class DeviceCountReqVO {

    @Schema(description = "开始时间")
    private Date startDate;

    @Schema(description = "结束时间")
    private Date endDate;

    @Schema(description = "连接状态（0：未激活 1：离线 2；在线 3：禁用）")
    private Integer linkState;

    @Schema(description = "节点类型(0直连，1网关，2网关子设备）")
    private Integer nodeType;
}
