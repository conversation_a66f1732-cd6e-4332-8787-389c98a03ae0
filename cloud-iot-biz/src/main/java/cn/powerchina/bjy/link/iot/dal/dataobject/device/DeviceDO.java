package cn.powerchina.bjy.link.iot.dal.dataobject.device;

import cn.powerchina.bjy.cloud.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 设备 DO
 *
 * <AUTHOR>
 */
@TableName("iot_device")
@KeySequence("iot_device_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeviceDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 产品编码
     */
    private String productCode;
    /**
     * 项目编码
     */
    private String projectCode;
    /**
     * 设备名称
     */
    private String deviceName;
    /**
     * 设备编号
     */
    private String deviceCode;
    /**
     * 父设备号（网关）
     */
    private String parentCode;
    /**
     * 设备唯一标识
     */
    private String deviceSerial;

    /**
     * 启用状态（0:禁用 1：启用）
     */
    private Integer status;
    /**
     * 是否启用设备影子(0=禁用，1=启用)，默认启用
     */
    private Boolean shadow;
    /**
     * 通道编码
     */
    private String channelCode;
    /**
     * mcu通道号
     */
    private String mcuChannel;
    /**
     * 从站号
     */
    private String slaveId;
    /**
     * 下发状态（0-未下发；1-已下发；）
     */
    private Integer distributeState;
    /**
     * 连接状态（0：未激活 1：离线 2；在线 3：禁用）
     */
    private Integer linkState;
    /**
     * 注册时间
     */
    private LocalDateTime registerTime;
    /**
     * 节点类型(0直连，1网关，2网关子设备）
     */
    private Integer nodeType;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 维度
     */
    private String latitude;
    /**
     * 差异化扩展
     */
    private String extra;
    /**
     * 最后上线时间
     */
    private LocalDateTime lastUpTime;
    /**
     * 网关实例编码
     */
    private String edgeCode;

    /**
     * 驱动编码
     */
    private String driverCode;

    /**
     * 描述
     */
    private String remark;

    /**
     * 设备密钥
     */
    private String deviceSecret;

    /**
     * 类型(0:edge实例; 1:设备）
     */
    private Integer deviceType;

}