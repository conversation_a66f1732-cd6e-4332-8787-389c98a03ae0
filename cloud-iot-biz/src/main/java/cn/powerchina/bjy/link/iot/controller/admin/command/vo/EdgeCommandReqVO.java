package cn.powerchina.bjy.link.iot.controller.admin.command.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(description = "管理后台 - 设备指令 Request VO")
@Data
public class EdgeCommandReqVO {

    /**
     * 产品编码
     */
    @Schema(description = "产品编码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String productCode;

    /**
     * 设备编码
     */
    @Schema(description = "设备编码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String deviceCode;

    /**
     * 控制源（0-web；1-ios；2-android）
     */
    @Schema(description = "控制源（0-web；1-ios；2-android）", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "控制源不能为空")
    private Integer operatorSource;


    /**
     * 物模型标识符
     */
    @Schema(description = "物模型标识符", requiredMode = Schema.RequiredMode.REQUIRED)
    private String thingIdentity;

    /**
     * 物模型名称
     */
    @Schema(description = "物模型名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String thingName;

    /**
     * 物模型类型
     */
    @Schema(description = "物模型类型", requiredMode = Schema.RequiredMode.REQUIRED,example = "1")
    @NotNull(message = "物模型类型不能为空")
    private Integer thingType;


    /**
     * 输入参数
     */
    @Schema(description = "输入参数", requiredMode = Schema.RequiredMode.REQUIRED)
    private String inputParams;

    /**
     * 输出参数
     */
    @Schema(description = "输出参数")
    private String outputParams;

}
