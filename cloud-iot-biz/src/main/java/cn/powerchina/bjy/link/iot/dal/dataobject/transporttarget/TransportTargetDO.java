package cn.powerchina.bjy.link.iot.dal.dataobject.transporttarget;

import cn.powerchina.bjy.cloud.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 转发规则-转发目标 DO
 *
 * <AUTHOR>
 */
@TableName("iot_transport_target")
@KeySequence("iot_transport_target_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TransportTargetDO extends BaseDO {

    /**
     * 主键id
     */
    @TableId
    private Long id;
    /**
     * 规则id
     */
    private Long ruleId;
    /**
     * 目标名称
     */
    private String name;
    /**
     * 转发类型(1:HTTP推送 2MQTT推送)
     */
    private Integer transportType;
    /**
     * url地址
     */
    private String urlAddress;
    /**
     * token
     */
    private String token;
    /**
     * 转发topic
     */
    private String topic;

    /**
     * 描述
     */
    private String remark;

}