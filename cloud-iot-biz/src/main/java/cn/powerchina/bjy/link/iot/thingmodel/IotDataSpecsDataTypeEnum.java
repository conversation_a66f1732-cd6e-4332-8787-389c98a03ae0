package cn.powerchina.bjy.link.iot.thingmodel;


import cn.powerchina.bjy.link.iot.framework.tdengine.core.annotation.ArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * IoT 数据定义的数据类型枚举类
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum IotDataSpecsDataTypeEnum implements ArrayValuable<String> {

    INT("INT"),
    FLOAT("FLOAT"),
    DOUBLE("DOUBLE"),
    ENUM("ENUM"),
    BOOL("BOOL"),
    TEXT("STRING"),
    DATE("DATE");

    public static final String[] ARRAYS = Arrays.stream(values()).map(IotDataSpecsDataTypeEnum::getDataType).toArray(String[]::new);

    private final String dataType;

    @Override
    public String[] array() {
        return ARRAYS;
    }

}
