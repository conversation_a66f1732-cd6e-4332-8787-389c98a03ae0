package cn.powerchina.bjy.link.iot.controller.admin.scenealarmrecord.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import jakarta.validation.constraints.*;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 告警记录新增/修改 Request VO")
@Data
public class SceneAlarmRecordSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "10391")
    private Long id;

    @Schema(description = "资源空间ID", example = "14286")
    private Long resourceSpaceId;

    @Schema(description = "规则ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "19931")
    @NotNull(message = "规则ID不能为空")
    private Long ruleId;

    @Schema(description = "规则名称", example = "赵六")
    private String ruleName;

    @Schema(description = "关联的告警模板ID", example = "9071")
    private Long alarmTemplateId;

    @Schema(description = "告警名称", example = "王五")
    private String alarmName;

    @Schema(description = "告警内容")
    private String alarmContent;

    @Schema(description = "告警等级(1-轻微,2-中等,3-严重,4-非常严重)")
    private Integer alarmLevel;

    @Schema(description = "告警状态(1-触发,2-待验证,3-恢复)", example = "2")
    private Integer alarmStatus;

    @Schema(description = "触发时间")
    private LocalDateTime triggerTime;

    @Schema(description = "设备编码")
    private String deviceCode;

    @Schema(description = "产品编码")
    private String productCode;

    @Schema(description = "处理时间")
    private LocalDateTime processTime;

    @Schema(description = "告警处理")
    private String processRecord;

    @Schema(description = "处理用户ID", example = "28553")
    private Long processUserId;

    @Schema(description = "恢复类型(0-自动恢复,1-人工恢复)", example = "2")
    private Integer recoveryType;

    @Schema(description = "恢复时间")
    private LocalDateTime recoveryTime;

    @Schema(description = "恢复用户ID", example = "5245")
    private Long recoveryUserId;

    @Schema(description = "告警次数", example = "1")
    private Integer alarmNum;

    @Schema(description = "最后告警时间")
    private LocalDateTime lastAlarmTime;


}
