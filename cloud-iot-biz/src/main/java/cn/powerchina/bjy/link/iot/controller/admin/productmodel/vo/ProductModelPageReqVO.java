package cn.powerchina.bjy.link.iot.controller.admin.productmodel.vo;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 产品物模型分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ProductModelPageReqVO extends PageParam {

    @Schema(description = "产品编码")
    private String productCode;

    @Schema(description = "物模型标识符")
    private String thingIdentity;

    @Schema(description = "物模型名称", example = "")
    private String thingName;

    @Schema(description = "物模型类型，1-属性；2-服务；3-事件；", example = "1")
    private Integer thingType;

    @Schema(description = "数据类型（integer、decimal、string、bool、array、enum）", example = "2")
    private String datatype;

    @Schema(description = "读写类型，thing_type为1时必填", example = "1")
    private Integer readWriteType;

    @Schema(description = "事件类型，thing_type为3时必填", example = "2")
    private Integer eventType;

    @Schema(description = "输入参数")
    private String inputParams;

    @Schema(description = "输出参数")
    private String outputParams;

    @Schema(description = "属性扩展信息")
    private String extra;

    @Schema(description = "描述", example = "你说的对")
    private String remark;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}