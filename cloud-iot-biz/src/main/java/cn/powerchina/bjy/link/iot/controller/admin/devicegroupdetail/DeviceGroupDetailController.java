package cn.powerchina.bjy.link.iot.controller.admin.devicegroupdetail;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.link.iot.controller.admin.devicegroupdetail.bo.DeviceGroupDetailBO;
import cn.powerchina.bjy.link.iot.controller.admin.devicegroupdetail.vo.DeviceGroupDetailDelReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.devicegroupdetail.vo.DeviceGroupDetailPageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.devicegroupdetail.vo.DeviceGroupDetailRespVO;
import cn.powerchina.bjy.link.iot.controller.admin.devicegroupdetail.vo.DeviceGroupDetailSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.devicegroupdetail.DeviceGroupDetailDO;
import cn.powerchina.bjy.link.iot.service.devicegroupdetail.DeviceGroupDetailService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 设备分组明细")
@RestController
@RequestMapping("/iot/device/group/detail")
@Validated
public class DeviceGroupDetailController {

    @Resource
    private DeviceGroupDetailService deviceGroupDetailService;

    @PostMapping("/add")
    @Operation(summary = "设备分组添加设备")
//    @PreAuthorize("@ss.hasPermission('iot:device-group-detail:create')")
    public CommonResult<Boolean> createDeviceGroupDetail(@Valid @RequestBody DeviceGroupDetailSaveReqVO createReqVO) {
        deviceGroupDetailService.createDeviceGroupDetail(createReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "设备分组移除设备")
//    @PreAuthorize("@ss.hasPermission('iot:device-group-detail:delete')")
    public CommonResult<Boolean> deleteDeviceGroupDetail(@Valid @RequestBody DeviceGroupDetailDelReqVO reqVO) {
        deviceGroupDetailService.batchDeleteDeviceGroupDetail(reqVO.getIdList());
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得设备分组明细")
    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('iot:device-group-detail:query')")
    public CommonResult<DeviceGroupDetailRespVO> getDeviceGroupDetail(@RequestParam("id") Long id) {
        DeviceGroupDetailDO deviceGroupDetail = deviceGroupDetailService.getDeviceGroupDetail(id);
        return success(BeanUtils.toBean(deviceGroupDetail, DeviceGroupDetailRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得设备分组明细分页")
//    @PreAuthorize("@ss.hasPermission('iot:device-group-detail:query')")
    public CommonResult<PageResult<DeviceGroupDetailRespVO>> getDeviceGroupDetailPage(@Valid DeviceGroupDetailPageReqVO pageReqVO) {
        PageResult<DeviceGroupDetailBO> pageResult = deviceGroupDetailService.getDeviceGroupDetailBOPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, DeviceGroupDetailRespVO.class));
    }

}