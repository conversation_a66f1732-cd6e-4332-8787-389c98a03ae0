package cn.powerchina.bjy.link.iot.dal.dataobject.transportrule;

import cn.powerchina.bjy.cloud.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;
import com.baomidou.mybatisplus.annotation.*;

/**
 * 转发规则 DO
 *
 * <AUTHOR>
 */
@TableName("iot_transport_rule")
@KeySequence("iot_transport_rule_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TransportRuleDO extends BaseDO {

    /**
     * 主键id
     */
    @TableId
    private Long id;
    /**
     * 规则名称
     */
    private String name;
    /**
     * 转发规则编码
     */
    private String ruleCode;
    /**
     * 启用状态（0:未启动 1：运行中）
     */
    private Integer status;
    /**
     * 规则描述
     */
    private String remark;

}