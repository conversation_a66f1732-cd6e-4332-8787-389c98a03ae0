package cn.powerchina.bjy.link.iot.listener.register;

import cn.powerchina.bjy.link.iot.controller.admin.device.vo.DeviceSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.device.DeviceDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.product.ProductDO;
import cn.powerchina.bjy.link.iot.dto.register.GatewayRegisterModel;
import cn.powerchina.bjy.link.iot.enums.LinkStateEnum;
import cn.powerchina.bjy.link.iot.model.EdgeRegisterResultModel;
import cn.powerchina.bjy.link.iot.model.IotDeviceRegister;
import cn.powerchina.bjy.link.iot.mq.MqttPublisher;
import cn.powerchina.bjy.link.iot.service.device.DeviceService;
import cn.powerchina.bjy.link.iot.service.product.ProductService;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.UUID;

import static cn.powerchina.bjy.link.iot.enums.ErrorCodeConstants.*;

/**
 * @Description: 边缘测设备注册
 * @Author: handl
 * @CreateDate: 2025/08/22
 */
@Slf4j
@Component
public class GatewayRegisterHandler {

    @Resource
    private MqttPublisher mqttPublisher;

    @Resource
    private ProductService productService;

    @Resource
    private DeviceService deviceService;

    public void handler(GatewayRegisterModel gatewayRegisterModel) {
        try {
            //产品是否存在
            ProductDO product = productService.getProductByCode(gatewayRegisterModel.getProductId());
            if (product == null) {
                log.error("产品 {} 不存在，丢弃消息 {}", gatewayRegisterModel.getProductId(), gatewayRegisterModel.getDeviceId());
                return;
            }
            this.isDeviceRegister(product, gatewayRegisterModel);
            if (CollectionUtils.isEmpty(gatewayRegisterModel.getSubDevices())) {
                gatewayRegisterModel.getSubDevices().forEach(subDevice -> {
                    ProductDO subProduct = productService.getProductByCode(subDevice.getProductId());
                    this.isDeviceRegister(subProduct, subDevice);
                });
            }
        } catch (Exception e) {
            log.error("根据上报的设备信息，设备注册异常：{}", e.getMessage());
        }
    }


    public void isDeviceRegister(ProductDO product, GatewayRegisterModel gatewayRegisterModel) {
        //设备是否存在
        DeviceDO device = deviceService.getDevice(gatewayRegisterModel.getDeviceId());
        EdgeRegisterResultModel edgeRegisterResultModel = new EdgeRegisterResultModel();
        String respTopic = IotDeviceRegister.TOPIC_IOT_DEVICE_GATEWAY_REGISTER_RESPONSE.replace("+", gatewayRegisterModel.getEdgeCode());
        //1. 开启动态注册&&需要预注册
        if (product.getDynamicRegister() == 1 && product.getPreRegister() == 1) {
            if (device == null) {
                //设备不存在注册失败
                this.sendRegisterResponse(edgeRegisterResultModel, respTopic, DEVICE_NOT_EXISTS.getMsg());
                return;
            }

            if (LinkStateEnum.NO_ACTIVE.getType().equals(device.getLinkState())) {
                //设备状态为未激活，激活成功。返回设备密钥
                this.sendRegisterSecret(edgeRegisterResultModel, respTopic, device.getDeviceCode(), device.getDeviceSecret());

                //设备状态置为离线状态
                deviceService.updateLinkState(device.getId(), LinkStateEnum.OFF_LINE.getType());
            } else {
                //设备状态不是未激活，注册失败
                this.sendRegisterResponse(edgeRegisterResultModel, respTopic, DEVICE_STATUS_EXISTS.getMsg());
            }
            return;
        }
        //2. 开启动态注册&&不需要预注册
        if (product.getDynamicRegister() == 1 && product.getPreRegister() == 0) {
            if (device == null) {
                //设备不存在，插入设备数据，返回设备密钥
                //插入设备
                DeviceSaveReqVO deviceSaveReqVO = new DeviceSaveReqVO();
                deviceSaveReqVO.setDeviceCode(gatewayRegisterModel.getDeviceId());
                deviceSaveReqVO.setDeviceName(gatewayRegisterModel.getDeviceName());
                deviceSaveReqVO.setProductCode(gatewayRegisterModel.getProductId());
                deviceSaveReqVO.setNodeType(product.getNodeType());
                deviceSaveReqVO.setDeviceSerial(UUID.randomUUID().toString().replaceAll("-", ""));
                deviceSaveReqVO.setDeviceSecret(UUID.randomUUID().toString().replaceAll("-", ""));

                Long deviceId = null;
                try {
                    deviceId = deviceService.createDevice(deviceSaveReqVO);
                } catch (Exception e) {
                    log.info("设备注册异常：{}", e.getMessage());
                    this.sendRegisterResponse(edgeRegisterResultModel, respTopic, e.getMessage());
                    return;
                }

                this.sendRegisterSecret(edgeRegisterResultModel, respTopic, deviceSaveReqVO.getDeviceCode(), deviceSaveReqVO.getDeviceSecret());
                //设备状态置为离线状态
                deviceService.updateLinkState(deviceId, LinkStateEnum.OFF_LINE.getType());
                return;
            }
            if (LinkStateEnum.NO_ACTIVE.getType().equals(device.getLinkState())) {
                this.sendRegisterSecret(edgeRegisterResultModel, respTopic, device.getDeviceCode(), device.getDeviceSecret());
                //设备状态置为离线状态
                deviceService.updateLinkState(device.getId(), LinkStateEnum.OFF_LINE.getType());
            } else {
                //设备存在，不能重复注册
                this.sendRegisterResponse(edgeRegisterResultModel, respTopic, DEVICE_REGISTER_EXISTS.getMsg());
            }
            return;
        }

        //3. 关闭动态注册
        if (product.getDynamicRegister() == 0) {
            if (device != null) {
                //设备状态未激活，返回设备密钥
                this.sendRegisterSecret(edgeRegisterResultModel, respTopic, device.getDeviceCode(), device.getDeviceSecret());

                //设备状态置为离线状态
                deviceService.updateLinkState(device.getId(), LinkStateEnum.OFF_LINE.getType());
            } else {
                //设备未预注册，注册失败
                this.sendRegisterResponse(edgeRegisterResultModel, respTopic, DEVICE_NOT_REGISTER_EXISTS.getMsg());
            }
            return;
        }
        log.info("发送边缘设备注册消息: {}", edgeRegisterResultModel);
    }

    /**
     * 注册成功，返回密钥
     */
    private void sendRegisterSecret(EdgeRegisterResultModel edgeRegisterResultModel, String respTopic, String deviceCode, String secret) {
        edgeRegisterResultModel.setResultCode(0);
        edgeRegisterResultModel.setDeviceId(deviceCode);
        edgeRegisterResultModel.setSecret(secret);
        mqttPublisher.publish(respTopic, JSON.toJSONString(edgeRegisterResultModel));
    }

    /**
     * 注册失败，发送设备注册结果
     */
    private void sendRegisterResponse(EdgeRegisterResultModel edgeRegisterResultModel, String respTopic, String msg) {
        edgeRegisterResultModel.setResultCode(1);
        edgeRegisterResultModel.setMsg(msg);
        mqttPublisher.publish(respTopic, JSON.toJSONString(edgeRegisterResultModel));
    }


}
