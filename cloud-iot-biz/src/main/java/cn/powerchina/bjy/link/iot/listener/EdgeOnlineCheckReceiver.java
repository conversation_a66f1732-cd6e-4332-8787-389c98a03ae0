package cn.powerchina.bjy.link.iot.listener;

import cn.hutool.core.util.ObjectUtil;
import cn.powerchina.bjy.link.iot.dto.up.EdgeCheckOnlineStatus;
import cn.powerchina.bjy.link.iot.enums.IotTopicConstant;
import cn.powerchina.bjy.link.iot.service.device.DeviceService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.annotation.RocketMQMessageListener;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.apache.rocketmq.client.core.RocketMQListener;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;

/**
 * 边缘网关检测设备在线状态信息
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = IotTopicConstant.TOPIC_DEVICE_ONLINE_RESULT, consumerGroup = IotTopicConstant.GROUP_DEVICE_ONLINE_RESULT, requestTimeout = 10, consumptionThreadCount = 10)
public class EdgeOnlineCheckReceiver implements RocketMQListener {

    @Resource
    private DeviceService deviceService;


    @Override
    public ConsumeResult consume(MessageView messageView) {
        if (ObjectUtil.isNull(messageView)) {
            log.error("MessageView is null, skip consumption");
        }
        try {
            // 解析消息体
            EdgeCheckOnlineStatus entity = parseMessageBody(messageView);
            if (entity == null) {
                log.error("MessageView is null, skip consumption");
                return ConsumeResult.SUCCESS;
            }
            // 更新状态
            deviceService.edgeOnlineCheck(entity);
        } catch (Exception e) {
            log.error("edgeOnlineCheckReceive--->error,msg={}", e.getMessage(), e);
        }
        return ConsumeResult.SUCCESS;
    }

    /**
     * 解析消息体为实体类
     */
    private EdgeCheckOnlineStatus parseMessageBody(MessageView messageView) {
        try {
            ByteBuffer body = messageView.getBody();
            if (body.remaining() == 0) {
                log.error("Message body is empty, messageId: {}", messageView.getMessageId());
                return null;
            }

            String message = StandardCharsets.UTF_8.decode(body).toString();
            return JSON.parseObject(message, EdgeCheckOnlineStatus.class);

        } catch (JSONException e) {
            log.error("JSON解析失败, messageId: {}, content: {}",
                    messageView.getMessageId(),
                    messageView.getBody(),
                    e
            );
            return null;
        }
    }
}
