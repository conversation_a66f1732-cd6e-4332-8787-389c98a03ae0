package cn.powerchina.bjy.link.iot.service.sceneruletimetrigger;

import cn.powerchina.bjy.link.iot.controller.admin.sceneruletimetrigger.vo.SceneRuleTimeTriggerSaveReqVO;

import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public class CronExpressionGenerator {

    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("HH:mm");

    /**
     * 根据定时规则生成Cron表达式
     *
     * @param reqVO 定时规则请求对象
     * @return 生成的Cron表达式
     */
    public static String generateCron(SceneRuleTimeTriggerSaveReqVO reqVO) {
        if (reqVO.getExecutionTime() == null || reqVO.getRepeatType() == null) {
            throw new IllegalArgumentException("执行时间和重复类型不能为空");
        }

        String[] timeParts = reqVO.getExecutionTime().split(":");
        if (timeParts.length != 2) {
            throw new IllegalArgumentException("执行时间格式不正确，应为HH:mm");
        }

        String hour = timeParts[0];
        String minute = timeParts[1];
        String seconds = "0";

        switch (reqVO.getRepeatType()) {
            case 1: // 每天
                return String.format("%s %s %s * * ?", seconds, minute, hour);

            case 2: // 指定日期
                if (reqVO.getRepeatStartDate() == null) {
                    throw new IllegalArgumentException("指定日期模式需要设置开始日期");
                }
                return String.format("%s %s %s %d %d ? %d",
                        seconds, minute, hour,
                        reqVO.getRepeatStartDate().getDayOfMonth(),
                        reqVO.getRepeatStartDate().getMonthValue(),
                        reqVO.getRepeatStartDate().getYear());

            case 3: // 指定周期
                if (reqVO.getRepeatStartDate() == null || reqVO.getRepeatEndDate() == null) {
                    throw new IllegalArgumentException("指定周期模式需要设置开始和结束日期");
                }
                // 这种周期性的在Cron中不好直接表示，这里生成从开始日期到结束日期每天的Cron表达式
                // 实际应用中可能需要拆分为多个定时任务或使用其他调度方式
                return String.format("%s %s %s * * ?", seconds, minute, hour);

            case 4: // 自定义(每周特定几天)
                if (reqVO.getRepeatWeekDays() == null || reqVO.getRepeatWeekDays().isEmpty()) {
                    throw new IllegalArgumentException("自定义模式需要设置重复的星期几");
                }
                String weekDays = parseWeekDays(reqVO.getRepeatWeekDays());
                return String.format("%s %s %s ? * %s *", seconds, minute, hour, weekDays);

            default:
                throw new IllegalArgumentException("不支持的重复类型: " + reqVO.getRepeatType());
        }
    }

    private static String parseWeekDays(String weekDaysStr) {
        List<String> days = Arrays.stream(weekDaysStr.split(","))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .collect(Collectors.toList());

        if (days.isEmpty()) {
            throw new IllegalArgumentException("至少需要选择一个星期几");
        }

        return days.stream()
                .map(day -> {
                    switch (day) {
                        case "1":
                            return "MON";
                        case "2":
                            return "TUE";
                        case "3":
                            return "WED";
                        case "4":
                            return "THU";
                        case "5":
                            return "FRI";
                        case "6":
                            return "SAT";
                        case "7":
                            return "SUN";
                        default:
                            throw new IllegalArgumentException("无效的星期几: " + day);
                    }
                })
                .collect(Collectors.joining(","));
    }
}