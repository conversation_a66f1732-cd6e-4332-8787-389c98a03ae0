package cn.powerchina.bjy.link.iot.controller.admin.sceneruleaction.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @Description: 描述
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @CreateDate: 2025/5/30
 */
@Schema(description = "管理后台 - 指令 VO")
@Data
public class CommandVO {

    @Schema(description = "指令类型:1-属性,2-服务", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer commandType;

    @Schema(description = "指令唯一标识：属性/服务")
    private String commandIdentity;

    @Schema(description = "属性值")
    private String thingValue;

    @Schema(description = "服务：配置参数")
    private List<ParamsModelVO> paramsConfig;

}
