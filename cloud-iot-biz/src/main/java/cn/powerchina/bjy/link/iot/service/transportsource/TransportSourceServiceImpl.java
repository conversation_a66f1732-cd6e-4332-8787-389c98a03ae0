package cn.powerchina.bjy.link.iot.service.transportsource;

import cn.hutool.core.collection.CollectionUtil;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.iot.common.role.RoleCommon;
import cn.powerchina.bjy.link.iot.controller.admin.datapermissions.vo.DataPermissionsTreeVO;
import cn.powerchina.bjy.link.iot.controller.admin.resourcespace.vo.ResourceSpacePageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.transportsource.vo.*;
import cn.powerchina.bjy.link.iot.dal.dataobject.device.DeviceDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.product.ProductDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.resourcespace.ResourceSpaceDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.transportrule.TransportRuleDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.transportsource.TransportSourceDO;
import cn.powerchina.bjy.link.iot.dal.mysql.device.DeviceMapper;
import cn.powerchina.bjy.link.iot.dal.mysql.product.ProductMapper;
import cn.powerchina.bjy.link.iot.dal.mysql.resourcespace.ResourceSpaceMapper;
import cn.powerchina.bjy.link.iot.dal.mysql.transportrule.TransportRuleMapper;
import cn.powerchina.bjy.link.iot.dal.mysql.transportsource.TransportSourceMapper;
import cn.powerchina.bjy.link.iot.dto.message.DevicePropertiesReportModel;
import cn.powerchina.bjy.link.iot.dto.message.DeviceStatusModel;
import cn.powerchina.bjy.link.iot.enums.EnableStateEnum;
import cn.powerchina.bjy.link.iot.enums.TransportSourceTypeEnum;
import cn.powerchina.bjy.link.iot.service.product.ProductService;
import cn.powerchina.bjy.link.iot.service.resourcespace.ResourceSpaceService;
import cn.powerchina.bjy.link.iot.service.transporttarget.TransportTargetService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import java.util.stream.Collectors;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.link.iot.enums.ErrorCodeConstants.TRANSPORT_SOURCE_NOT_EXISTS;

/**
 * 转发规则-数据源 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class TransportSourceServiceImpl implements TransportSourceService {

    @Resource
    private TransportRuleMapper transportRuleMapper;
    @Resource
    private TransportSourceMapper transportSourceMapper;
    @Resource
    private ResourceSpaceMapper resourceSpaceMapper;
    @Resource
    @Lazy
    private ResourceSpaceService resourceSpaceService;
    @Resource
    private ProductMapper productMapper;
    @Resource
    private DeviceMapper deviceMapper;
    @Resource
    @Lazy
    private ProductService productService;
    @Resource
    private TransportTargetService transportTargetService;


    @Override
    public Long createTransportSource(TransportSourceSaveReqVO createReqVO) {
        // 插入
        TransportSourceDO transportSource = BeanUtils.toBean(createReqVO, TransportSourceDO.class);
        Map<String, String> resourceSpaceMap = getRangeResourceSpace().stream()
                .collect(Collectors.toMap(
                        DataPermissionsTreeVO::getId,
                        DataPermissionsTreeVO::getName,
                        (existing, replacement) -> existing
                ));
        Map<String, String> productMap = getRangeProduct(transportSource.getResourceSpaceId()).stream()
                .collect(Collectors.toMap(
                        DataPermissionsTreeVO::getId,
                        DataPermissionsTreeVO::getName,
                        (existing, replacement) -> existing
                ));
        Map<String, String> deviceMap = getRangeDevice(transportSource.getProductCode()).stream()
                .collect(Collectors.toMap(
                        DataPermissionsTreeVO::getId,
                        DataPermissionsTreeVO::getName,
                        (existing, replacement) -> existing
                ));
        if (resourceSpaceMap.containsKey(String.valueOf(transportSource.getResourceSpaceId()))) {
            transportSource.setResourceSpaceName(resourceSpaceMap.get(String.valueOf(transportSource.getResourceSpaceId())));
        }
        if (productMap.containsKey(transportSource.getProductCode())) {
            transportSource.setProductName(productMap.get(transportSource.getProductCode()));
        }
        if (deviceMap.containsKey(transportSource.getDeviceCode())) {
            transportSource.setDeviceName(deviceMap.get(transportSource.getDeviceCode()));
        }

        transportSourceMapper.insert(transportSource);
        // 返回
        return transportSource.getId();
    }

    @Override
    public void updateTransportSource(TransportSourceSaveReqVO updateReqVO) {
        // 校验存在
        validateTransportSourceExists(updateReqVO.getId());
        // 更新
        TransportSourceDO updateObj = BeanUtils.toBean(updateReqVO, TransportSourceDO.class);
        Map<String, String> resourceSpaceMap = getRangeResourceSpace().stream()
                .collect(Collectors.toMap(
                        DataPermissionsTreeVO::getId,
                        DataPermissionsTreeVO::getName,
                        (existing, replacement) -> existing
                ));
        Map<String, String> productMap = getRangeProduct(updateObj.getResourceSpaceId()).stream()
                .collect(Collectors.toMap(
                        DataPermissionsTreeVO::getId,
                        DataPermissionsTreeVO::getName,
                        (existing, replacement) -> existing
                ));
        Map<String, String> deviceMap = getRangeDevice(updateObj.getProductCode()).stream()
                .collect(Collectors.toMap(
                        DataPermissionsTreeVO::getId,
                        DataPermissionsTreeVO::getName,
                        (existing, replacement) -> existing
                ));
        if (resourceSpaceMap.containsKey(String.valueOf(updateObj.getResourceSpaceId()))) {
            updateObj.setResourceSpaceName(resourceSpaceMap.get(String.valueOf(updateObj.getResourceSpaceId())));
        }
        if (productMap.containsKey(updateObj.getProductCode())) {
            updateObj.setProductName(productMap.get(updateObj.getProductCode()));
        }
        if (deviceMap.containsKey(updateObj.getDeviceCode())) {
            updateObj.setDeviceName(deviceMap.get(updateObj.getDeviceCode()));
        }
        transportSourceMapper.updateById(updateObj);
    }

    @Override
    public void deleteTransportSource(Long id) {
        // 校验存在
        validateTransportSourceExists(id);
        // 删除
        transportSourceMapper.deleteById(id);
    }

    private void validateTransportSourceExists(Long id) {
        if (transportSourceMapper.selectById(id) == null) {
            throw exception(TRANSPORT_SOURCE_NOT_EXISTS);
        }
    }

    @Override
    public TransportSourceDO getTransportSource(Long id) {
        return transportSourceMapper.selectById(id);
    }

    @Override
    public PageResult<TransportSourceRespVO> getTransportSourcePage(TransportSourcePageReqVO pageReqVO) {
        PageResult<TransportSourceDO> transportSourcePage = transportSourceMapper.selectPage(pageReqVO);
        List<TransportSourceRespVO> pageRuleList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(transportSourcePage.getList())) {
            transportSourcePage.getList().forEach(item -> {
                TransportSourceRespVO sceneRulePageRespVO = BeanUtils.toBean(item, TransportSourceRespVO.class);
                Arrays.stream(sceneRulePageRespVO.getDataType().split(",")).toList().forEach(dataType -> {
                    TransportSourceTypeEnum transportSourceTypeEnum = TransportSourceTypeEnum.getByCode(dataType);
                    if (transportSourceTypeEnum != null) {
                        if (sceneRulePageRespVO.getDataTypeName() == null) {
                            sceneRulePageRespVO.setDataTypeName(transportSourceTypeEnum.getDesc());
                        } else {
                            sceneRulePageRespVO.setDataTypeName(sceneRulePageRespVO.getDataTypeName() + "," + transportSourceTypeEnum.getDesc());
                        }
                    }
                });

                Map<String, String> resourceSpaceMap = getRangeResourceSpace().stream()
                        .collect(Collectors.toMap(
                                DataPermissionsTreeVO::getId,
                                DataPermissionsTreeVO::getName,
                                (existing, replacement) -> existing
                        ));
                Map<String, String> productMap = getRangeProduct(item.getResourceSpaceId()).stream()
                        .collect(Collectors.toMap(
                                DataPermissionsTreeVO::getId,
                                DataPermissionsTreeVO::getName,
                                (existing, replacement) -> existing
                        ));
                Map<String, String> deviceMap = getRangeDevice(item.getProductCode()).stream()
                        .collect(Collectors.toMap(
                                DataPermissionsTreeVO::getId,
                                DataPermissionsTreeVO::getName,
                                (existing, replacement) -> existing
                        ));

                if (resourceSpaceMap.containsKey(String.valueOf(item.getResourceSpaceId()))) {
                    sceneRulePageRespVO.setResourceSpaceName(resourceSpaceMap.get(String.valueOf(item.getResourceSpaceId())));
                }
                if (productMap.containsKey(item.getProductCode())) {
                    sceneRulePageRespVO.setProductName(productMap.get(item.getProductCode()));
                }
                if (deviceMap.containsKey(item.getDeviceCode())) {
                    sceneRulePageRespVO.setDeviceName(deviceMap.get(item.getDeviceCode()));
                }
                pageRuleList.add(sceneRulePageRespVO);
            });
        }
        return new PageResult<>(pageRuleList, transportSourcePage.getTotal());
    }

    public List<DataPermissionsTreeVO> getRangeResourceSpace() {
        //查询资源空间数据
        ResourceSpacePageReqVO ResourceSpaceVO = new ResourceSpacePageReqVO();
        ResourceSpaceVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        ResourceSpaceVO.setState(EnableStateEnum.YES.getType());
        PageResult<ResourceSpaceDO> page = resourceSpaceService.getResourceSpacePage(ResourceSpaceVO);
        List<Long> resourceSpaceIds = new ArrayList<>();
        for (ResourceSpaceDO resourceSpaceDO : page.getList()) {
            resourceSpaceIds.add(resourceSpaceDO.getId());
        }
        List<ResourceSpaceDO> resourceSpaceDOList = resourceSpaceMapper.selectList(new LambdaQueryWrapperX<ResourceSpaceDO>().inIfPresent(ResourceSpaceDO::getId, resourceSpaceIds));
        List<DataPermissionsTreeVO> list = new ArrayList<>();
        list.add(DataPermissionsTreeVO.builder().id("-1").name("全部资源空间").parentId("-1").level(0).build());
        if (!CollectionUtils.isEmpty(resourceSpaceDOList)) {
            resourceSpaceDOList.forEach(resoure -> {
                list.add(DataPermissionsTreeVO.builder().id(resoure.getId() + "").name(resoure.getSpaceName()).parentId("0").level(1).build());
            });
        }
        return list;
    }

    @Override
    public List<DataPermissionsTreeVO> getRangeProduct(Long resourceSpaceId) {
        //查询产品数据
        List<ProductDO> productDOList = productMapper.selectList(new LambdaQueryWrapperX<ProductDO>()
                .eq(ProductDO::getResourceSpaceId, resourceSpaceId));
        List<DataPermissionsTreeVO> productlist = new ArrayList<>();
        productlist.add(DataPermissionsTreeVO.builder().id("-1").name("(+)所有产品").parentId(resourceSpaceId.toString()).level(2).build());
        if (!CollectionUtils.isEmpty(productDOList)) {
            productDOList.forEach(product -> {
                productlist.add(DataPermissionsTreeVO.builder().id(product.getProductCode()).name(product.getProductName()).parentId(resourceSpaceId.toString()).level(3).build());
            });
        }
        return productlist;
    }

    @Override
    public List<DataPermissionsTreeVO> getRangeDevice(String productCode) {
        //查询所有设备数据
        List<DeviceDO> deviceDOList = deviceMapper.selectList(new LambdaQueryWrapperX<DeviceDO>()
                .eq(DeviceDO::getProductCode, productCode));
        List<DataPermissionsTreeVO> deviceList = new ArrayList<>();
        deviceList.add(DataPermissionsTreeVO.builder().id("-1").name("(+)所有设备").parentId(productCode).level(4).build());
        if (!CollectionUtils.isEmpty(deviceDOList)) {
            deviceDOList.forEach(device -> {
                deviceList.add(DataPermissionsTreeVO.builder().id(device.getDeviceCode()).name(device.getDeviceName()).parentId(productCode).level(5).build());
            });
        }
        return deviceList;
    }

    @Override
    public void dataForwarding(TransportSourceTypeEnum dataType, Object obj) {
        //查询所有已经启动的规则
        List<TransportRuleDO> ruleList = transportRuleMapper.selectList(TransportRuleDO::getStatus, 1);
        if (ruleList.isEmpty()) {
            return;
        }
        ruleList.forEach(rule -> {
        });
        List<Long> ruleIds = ruleList.stream()
                .map(TransportRuleDO::getId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        //产品创建、更新、删除
        if (dataType.equals(TransportSourceTypeEnum.PRODUCT_CREATE)
                || dataType.equals(TransportSourceTypeEnum.PRODUCT_UPDATE)
                || dataType.equals(TransportSourceTypeEnum.PRODUCT_DELETE)) {
            this.sendByProduct(dataType, obj, ruleIds);
        }
        //设备创建、更新、删除、设备状态变更
        if (dataType.equals(TransportSourceTypeEnum.DEVICE_CREATE)
                || dataType.equals(TransportSourceTypeEnum.DEVICE_UPDATE)
                || dataType.equals(TransportSourceTypeEnum.DEVICE_DELETE)) {
            this.sendByDevice(dataType, obj, ruleIds);
        }
        //设备属性上报、设备事件上报、设备指令控制结果
        if (dataType.equals(TransportSourceTypeEnum.DEVICE_STATUS_CHANGE)
                || dataType.equals(TransportSourceTypeEnum.DEVICE_ATTRIBUTE_REPORT)
                || dataType.equals(TransportSourceTypeEnum.DEVICE_EVENT_REPORT)
                || dataType.equals(TransportSourceTypeEnum.DEVICE_COMMAND_RESULT)) {
            this.sendByOther(dataType, obj, ruleIds);
        }
    }

    public void sendByProduct(TransportSourceTypeEnum dataType, Object obj, List<Long> ruleIds) {
        ProductRelayVO productRelayVO = (ProductRelayVO) obj;
        //查询所有资源空间数据源
        List<TransportSourceDO> allResourceSpaceList = transportSourceMapper.selectList(new LambdaQueryWrapperX<TransportSourceDO>()
                .likeIfPresent(TransportSourceDO::getDataType, dataType.getCode())
                .eqIfPresent(TransportSourceDO::getResourceSpaceId, -1)
                .inIfPresent(TransportSourceDO::getRuleId, ruleIds));
        //查询所有产品数据源
        List<TransportSourceDO> allProductList = transportSourceMapper.selectList(new LambdaQueryWrapperX<TransportSourceDO>()
                .likeIfPresent(TransportSourceDO::getDataType, dataType.getCode())
                .eqIfPresent(TransportSourceDO::getResourceSpaceId, productRelayVO.getResourceSpaceId())
                .inIfPresent(TransportSourceDO::getRuleId, ruleIds)
                .eqIfPresent(TransportSourceDO::getProductCode, -1));
        //查询指定产品数据源
        List<TransportSourceDO> productList = transportSourceMapper.selectList(new LambdaQueryWrapperX<TransportSourceDO>()
                .likeIfPresent(TransportSourceDO::getDataType, dataType.getCode())
                .eqIfPresent(TransportSourceDO::getResourceSpaceId, productRelayVO.getResourceSpaceId())
                .inIfPresent(TransportSourceDO::getRuleId, ruleIds)
                .eqIfPresent(TransportSourceDO::getProductCode, productRelayVO.getProductCode()));

        List<TransportSourceDO> transportSourceList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(allResourceSpaceList)) {
            transportSourceList.addAll(allResourceSpaceList);
        }
        if (!CollectionUtils.isEmpty(allProductList)) {
            transportSourceList.addAll(allProductList);
        }
        if (!CollectionUtils.isEmpty(productList)) {
            transportSourceList.addAll(productList);
        }
        if (!CollectionUtils.isEmpty(transportSourceList)) {
            log.info("产品消息转发开始，产品code：{}", productRelayVO.getProductCode());
            transportTargetService.dataTransportTarget(dataType, transportSourceList, obj);
        }
    }

    public void sendByDevice(TransportSourceTypeEnum dataType, Object obj, List<Long> ruleIds) {
        DeviceRelayVO deviceRelayVO = (DeviceRelayVO) obj;
        //查询所有资源空间数据源
        List<TransportSourceDO> allResourceSpaceList = transportSourceMapper.selectList(new LambdaQueryWrapperX<TransportSourceDO>()
                .likeIfPresent(TransportSourceDO::getDataType, dataType.getCode())
                .eqIfPresent(TransportSourceDO::getResourceSpaceId, -1)
                .inIfPresent(TransportSourceDO::getRuleId, ruleIds));
        //查询所有产品数据源
        List<TransportSourceDO> allProductList = transportSourceMapper.selectList(new LambdaQueryWrapperX<TransportSourceDO>()
                .likeIfPresent(TransportSourceDO::getDataType, dataType.getCode())
                .eqIfPresent(TransportSourceDO::getResourceSpaceId, deviceRelayVO.getResourceSpaceId())
                .inIfPresent(TransportSourceDO::getRuleId, ruleIds)
                .eqIfPresent(TransportSourceDO::getProductCode, -1));
        //查询指定产品数据源
        List<TransportSourceDO> productList = transportSourceMapper.selectList(new LambdaQueryWrapperX<TransportSourceDO>()
                .likeIfPresent(TransportSourceDO::getDataType, dataType.getCode())
                .eqIfPresent(TransportSourceDO::getResourceSpaceId, deviceRelayVO.getResourceSpaceId())
                .inIfPresent(TransportSourceDO::getRuleId, ruleIds)
                .eqIfPresent(TransportSourceDO::getProductCode, deviceRelayVO.getProductCode()));
        //查询全部设备
        List<TransportSourceDO> allDeviceList = transportSourceMapper.selectList(new LambdaQueryWrapperX<TransportSourceDO>()
                .likeIfPresent(TransportSourceDO::getDataType, dataType.getCode())
                .eqIfPresent(TransportSourceDO::getResourceSpaceId, deviceRelayVO.getResourceSpaceId())
                .inIfPresent(TransportSourceDO::getRuleId, ruleIds)
                .eqIfPresent(TransportSourceDO::getProductCode, deviceRelayVO.getProductCode())
                .eqIfPresent(TransportSourceDO::getDeviceCode, -1));
        //查询指定设备
        List<TransportSourceDO> deviceList = transportSourceMapper.selectList(new LambdaQueryWrapperX<TransportSourceDO>()
                .likeIfPresent(TransportSourceDO::getDataType, dataType.getCode())
                .eqIfPresent(TransportSourceDO::getResourceSpaceId, deviceRelayVO.getResourceSpaceId())
                .inIfPresent(TransportSourceDO::getRuleId, ruleIds)
                .eqIfPresent(TransportSourceDO::getProductCode, deviceRelayVO.getProductCode())
                .eqIfPresent(TransportSourceDO::getDeviceCode, deviceRelayVO.getDeviceCode()));

        List<TransportSourceDO> transportSourceList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(allResourceSpaceList)) {
            transportSourceList.addAll(allResourceSpaceList);
        }
        if (!CollectionUtils.isEmpty(allProductList)) {
            transportSourceList.addAll(allProductList);
        }
        if (!CollectionUtils.isEmpty(productList)) {
            transportSourceList.addAll(productList);
        }
        if (!CollectionUtils.isEmpty(allDeviceList)) {
            transportSourceList.addAll(allDeviceList);
        }
        if (!CollectionUtils.isEmpty(deviceList)) {
            transportSourceList.addAll(deviceList);
        }
        if (!CollectionUtils.isEmpty(transportSourceList)) {
            log.info("设备消息转发开始，设备code：{}", deviceRelayVO.getDeviceCode());
            transportTargetService.dataTransportTarget(dataType, transportSourceList, obj);
        }
    }

    public void sendByOther(TransportSourceTypeEnum dataType, Object obj, List<Long> ruleIds) {
        String productCode = null;
        String deviceCode = null;
        Long resourceSpaceId = null;
        if (dataType == TransportSourceTypeEnum.DEVICE_STATUS_CHANGE) {
            DeviceStatusModel deviceStatusChangeVO = (DeviceStatusModel) obj;
            productCode = deviceStatusChangeVO.getProductCode();
            deviceCode = deviceStatusChangeVO.getDeviceCode();
        }
        if (dataType == TransportSourceTypeEnum.DEVICE_ATTRIBUTE_REPORT) {
            DevicePropertiesReportModel devicePropertiesReportModel = (DevicePropertiesReportModel) obj;
            productCode = devicePropertiesReportModel.getProductCode();
            deviceCode = devicePropertiesReportModel.getDeviceCode();
        }
        // 通过productCode查询资源空间id
        ProductDO productDO = productService.getProductByCode(productCode);
        if (productDO != null) {
            resourceSpaceId = productDO.getResourceSpaceId();
        }
        //查询所有资源空间数据源
        List<TransportSourceDO> allResourceSpaceList = transportSourceMapper.selectList(new LambdaQueryWrapperX<TransportSourceDO>()
                .likeIfPresent(TransportSourceDO::getDataType, dataType.getCode())
                .eqIfPresent(TransportSourceDO::getResourceSpaceId, -1)
                .inIfPresent(TransportSourceDO::getRuleId, ruleIds));
        //查询所有产品数据源
        List<TransportSourceDO> allProductList = transportSourceMapper.selectList(new LambdaQueryWrapperX<TransportSourceDO>()
                .likeIfPresent(TransportSourceDO::getDataType, dataType.getCode())
                .eqIfPresent(TransportSourceDO::getResourceSpaceId, resourceSpaceId)
                .inIfPresent(TransportSourceDO::getRuleId, ruleIds)
                .eqIfPresent(TransportSourceDO::getProductCode, -1));
        //查询指定产品数据源
        List<TransportSourceDO> productList = transportSourceMapper.selectList(new LambdaQueryWrapperX<TransportSourceDO>()
                .likeIfPresent(TransportSourceDO::getDataType, dataType.getCode())
                .eqIfPresent(TransportSourceDO::getResourceSpaceId, resourceSpaceId)
                .inIfPresent(TransportSourceDO::getRuleId, ruleIds)
                .eqIfPresent(TransportSourceDO::getProductCode, productCode));
        //查询全部设备
        List<TransportSourceDO> allDeviceList = transportSourceMapper.selectList(new LambdaQueryWrapperX<TransportSourceDO>()
                .likeIfPresent(TransportSourceDO::getDataType, dataType.getCode())
                .eqIfPresent(TransportSourceDO::getResourceSpaceId, resourceSpaceId)
                .inIfPresent(TransportSourceDO::getRuleId, ruleIds)
                .eqIfPresent(TransportSourceDO::getProductCode, productCode)
                .eqIfPresent(TransportSourceDO::getDeviceCode, -1));
        //查询指定设备
        List<TransportSourceDO> deviceList = transportSourceMapper.selectList(new LambdaQueryWrapperX<TransportSourceDO>()
                .likeIfPresent(TransportSourceDO::getDataType, dataType.getCode())
                .eqIfPresent(TransportSourceDO::getResourceSpaceId, resourceSpaceId)
                .inIfPresent(TransportSourceDO::getRuleId, ruleIds)
                .eqIfPresent(TransportSourceDO::getProductCode, productCode)
                .eqIfPresent(TransportSourceDO::getDeviceCode, deviceCode));

        List<TransportSourceDO> transportSourceList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(allResourceSpaceList)) {
            transportSourceList.addAll(allResourceSpaceList);
        }
        if (!CollectionUtils.isEmpty(allProductList)) {
            transportSourceList.addAll(allProductList);
        }
        if (!CollectionUtils.isEmpty(productList)) {
            transportSourceList.addAll(productList);
        }
        if (!CollectionUtils.isEmpty(allDeviceList)) {
            transportSourceList.addAll(allDeviceList);
        }
        if (!CollectionUtils.isEmpty(deviceList)) {
            transportSourceList.addAll(deviceList);
        }

        if (!CollectionUtils.isEmpty(transportSourceList)) {
            log.info("消息转发开始，数据：{}", obj);
            transportTargetService.dataTransportTarget(dataType, transportSourceList, obj);
        }
    }

}