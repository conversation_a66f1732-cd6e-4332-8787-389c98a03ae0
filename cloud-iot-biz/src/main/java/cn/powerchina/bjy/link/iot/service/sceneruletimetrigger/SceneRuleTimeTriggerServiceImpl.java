package cn.powerchina.bjy.link.iot.service.sceneruletimetrigger;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.cloud.framework.web.core.util.WebFrameworkUtils;
import cn.powerchina.bjy.cloud.system.api.user.AdminUserApi;
import cn.powerchina.bjy.cloud.system.api.user.dto.AdminUserRespDTO;
import cn.powerchina.bjy.link.iot.controller.admin.sceneruletimetrigger.vo.SceneRuleTimeTriggerPageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.sceneruletimetrigger.vo.SceneRuleTimeTriggerSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.scenerule.SceneRuleDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.sceneruletimetrigger.SceneRuleTimeTriggerDO;
import cn.powerchina.bjy.link.iot.dal.mysql.sceneruletimetrigger.SceneRuleTimeTriggerMapper;
import cn.powerchina.bjy.link.iot.job.JobCreateRequest;
import cn.powerchina.bjy.link.iot.job.XxlJobService;
import cn.powerchina.bjy.link.iot.service.scenerule.SceneRuleService;
import cn.powerchina.bjy.link.iot.util.SnowFlakeUtil;
import com.alibaba.cloud.commons.lang.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.link.iot.enums.ErrorCodeConstants.SCENE_RULE_TIME_TRIGGER_ERROR;
import static cn.powerchina.bjy.link.iot.enums.ErrorCodeConstants.SCENE_RULE_TIME_TRIGGER_NOT_EXISTS;

/**
 * 定时触发 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class SceneRuleTimeTriggerServiceImpl implements SceneRuleTimeTriggerService {

    @Resource
    private SceneRuleTimeTriggerMapper sceneRuleTimeTriggerMapper;

    @Resource
    private XxlJobService xxlJobService;

    @Resource
    private AdminUserApi adminUserApi;

    @Resource
    @Lazy
    private SceneRuleService sceneRuleService;

    @Override
    @Transactional
    public void createAndUpdateSceneRuleTimeTrigger(SceneRuleTimeTriggerSaveReqVO ruleTimeTriggerSaveReqVO, Long ruleId, Long triggerId) {
        String cronExpression = CronExpressionGenerator.generateCron(ruleTimeTriggerSaveReqVO);
        Long loginUserId = WebFrameworkUtils.getLoginUserId();
        AdminUserRespDTO userRespDTO = adminUserApi.getUser(loginUserId).getData();
        int jobId = 0;
        try {
            //调用xxjob
            JobCreateRequest jobCreateRequest = new JobCreateRequest();
            jobCreateRequest.setAuthor(Objects.nonNull(userRespDTO) ? userRespDTO.getName() : "defaultAdmin");
            jobCreateRequest.setCronExpression(cronExpression);
            jobCreateRequest.setJobDesc("规则" + ruleId);
            jobId = xxlJobService.addJob(jobCreateRequest);
        } catch (Exception e) {
            log.error("定时触发器{}-{} 创建xxjob失败{}", triggerId, cronExpression, e.getMessage());
            throw exception(SCENE_RULE_TIME_TRIGGER_ERROR);
        }

        ruleTimeTriggerSaveReqVO.setRuleId(ruleId);
        ruleTimeTriggerSaveReqVO.setTriggerId(triggerId);
        //cron表达式
        ruleTimeTriggerSaveReqVO.setCronExpression(cronExpression);
        ruleTimeTriggerSaveReqVO.setJobId(String.valueOf(jobId));
        if (Objects.isNull(ruleTimeTriggerSaveReqVO.getId())) {
            createSceneRuleTimeTrigger(ruleTimeTriggerSaveReqVO);
        } else {
            updateSceneRuleTimeTrigger(ruleTimeTriggerSaveReqVO);
        }
    }

    @Override
    public List<SceneRuleTimeTriggerDO> getSceneRuleTimeTriggerByRuleId(Long ruleId) {
        return sceneRuleTimeTriggerMapper.selectList(new LambdaQueryWrapperX<SceneRuleTimeTriggerDO>().
                eq(SceneRuleTimeTriggerDO::getRuleId, ruleId)
                .orderByAsc(SceneRuleTimeTriggerDO::getSort));
    }

    @Override
    public SceneRuleTimeTriggerDO getSceneRuleTimeTriggerByTriggerId(Long triggerId) {
        return sceneRuleTimeTriggerMapper.selectOne(new LambdaQueryWrapperX<SceneRuleTimeTriggerDO>().
                eq(SceneRuleTimeTriggerDO::getTriggerId, triggerId));
    }

    @Override
    public SceneRuleDO getSceneRuleByJobId(Integer jobId) {
        SceneRuleTimeTriggerDO sceneRuleTimeTriggerDO = sceneRuleTimeTriggerMapper.selectOne(new LambdaQueryWrapperX<SceneRuleTimeTriggerDO>().
                eq(SceneRuleTimeTriggerDO::getJobId, jobId));
        if (null != sceneRuleTimeTriggerDO) {
            return sceneRuleService.getSceneRule(sceneRuleTimeTriggerDO.getRuleId());
        }
        return null;
    }

    @Override
    public Long createSceneRuleTimeTrigger(SceneRuleTimeTriggerSaveReqVO createReqVO) {
        // 插入
        SceneRuleTimeTriggerDO sceneRuleTimeTrigger = BeanUtils.toBean(createReqVO, SceneRuleTimeTriggerDO.class);
        sceneRuleTimeTriggerMapper.insert(sceneRuleTimeTrigger);
        // 返回
        return sceneRuleTimeTrigger.getId();
    }

    @Override
    public void updateSceneRuleTimeTrigger(SceneRuleTimeTriggerSaveReqVO updateReqVO) {
        // 校验存在
        SceneRuleTimeTriggerDO sceneRuleTimeTriggerDO = validateSceneRuleTimeTriggerExists(updateReqVO.getId());
        if (StringUtils.isNotBlank(sceneRuleTimeTriggerDO.getJobId())) {
            xxlJobService.removeJob(Integer.parseInt(sceneRuleTimeTriggerDO.getJobId()));
        }
        // 更新
        SceneRuleTimeTriggerDO updateObj = BeanUtils.toBean(updateReqVO, SceneRuleTimeTriggerDO.class);
        sceneRuleTimeTriggerMapper.updateById(updateObj);
    }

    @Override
    public void deleteSceneRuleTimeTrigger(Long id) {
        // 校验存在
        validateSceneRuleTimeTriggerExists(id);
        // 删除
        sceneRuleTimeTriggerMapper.deleteById(id);
    }

    private SceneRuleTimeTriggerDO validateSceneRuleTimeTriggerExists(Long id) {
        SceneRuleTimeTriggerDO sceneRuleTimeTriggerDO = sceneRuleTimeTriggerMapper.selectById(id);
        if (sceneRuleTimeTriggerDO == null) {
            throw exception(SCENE_RULE_TIME_TRIGGER_NOT_EXISTS);
        }
        return sceneRuleTimeTriggerDO;
    }

    @Override
    public SceneRuleTimeTriggerDO getSceneRuleTimeTrigger(Long id) {
        return sceneRuleTimeTriggerMapper.selectById(id);
    }

    @Override
    public PageResult<SceneRuleTimeTriggerDO> getSceneRuleTimeTriggerPage(SceneRuleTimeTriggerPageReqVO pageReqVO) {
        return sceneRuleTimeTriggerMapper.selectPage(pageReqVO);
    }

}