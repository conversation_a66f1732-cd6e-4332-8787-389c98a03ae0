package cn.powerchina.bjy.link.iot.controller.admin.devicepropertylog.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 设备属性关联物模型 Response VO")
@Data
@ExcelIgnoreUnannotated
public class DevicePropertyLogModelRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "29963")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "物模型标识符", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("物模型标识符")
    private String thingIdentity;

    @Schema(description = "物模型名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "")
    @ExcelProperty("物模型名称")
    private String thingName;

    @Schema(description = "物模型日志值", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("物模型日志值")
    private String thingValue;

    @Schema(description = "模式(1=影子模式，2=在线模式，3=其他)", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("模式(1=影子模式，2=在线模式，3=其他)")
    private Integer deviceMode;

    @Schema(description = "备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "你说的对")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "读写类型", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("读写类型")
    private Integer readWriteType;

    @Schema(description = "数据类型（integer、decimal、string、bool、array、enum）", example = "2")
    @ExcelProperty("数据类型（integer、decimal、string、bool、array、enum）")
    private String datatype;

    @Schema(description = "输入参数", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("输入参数")
    private String inputParams;

    @Schema(description = "输出参数", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("输出参数")
    private String outputParams;

    @Schema(description = "属性扩展信息", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("属性扩展信息")
    private String extra;


}