package cn.powerchina.bjy.link.iot.service.transportrule;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.json.JsonUtils;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.link.iot.common.role.RoleCommon;
import cn.powerchina.bjy.link.iot.controller.admin.transportrule.vo.TransportRulePageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.transportrule.vo.TransportRuleSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.product.ProductDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.transportrule.TransportRuleDO;
import cn.powerchina.bjy.link.iot.dal.mysql.datapermissions.DataPermissionsMapper;
import cn.powerchina.bjy.link.iot.dal.mysql.transportrule.TransportRuleMapper;
import cn.powerchina.bjy.link.iot.enums.IotTopicConstant;
import cn.powerchina.bjy.link.iot.enums.SceneTypeEnum;
import cn.powerchina.bjy.link.iot.enums.StatisticImageTypeEnum;
import cn.powerchina.bjy.link.iot.model.DeviceDataTransportModel;
import cn.powerchina.bjy.link.iot.model.DeviceTransportModel;
import cn.powerchina.bjy.link.iot.mq.RocketMQv5Client;
import cn.powerchina.bjy.link.iot.service.messagestatisticday.MessageStatisticDayService;
import cn.powerchina.bjy.link.iot.service.product.ProductService;
import cn.powerchina.bjy.link.iot.service.resourcespace.ResourceSpaceService;
import cn.powerchina.bjy.link.iot.util.CodeGenerator;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import java.util.stream.Collectors;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.link.iot.enums.ErrorCodeConstants.TRANSPORT_RULE_NOT_EXISTS;

/**
 * 数据转发规则 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class TransportRuleServiceImpl implements TransportRuleService {

    @Resource
    private TransportRuleMapper transportRuleMapper;

    @Resource
    private DataPermissionsMapper dataPermissionsMapper;

    @Autowired
    private ResourceSpaceService resourceSpaceService;

    @Autowired
    private ProductService productService;

    @Autowired
    private MessageStatisticDayService messageStatisticDayService;
    @Resource
    private RocketMQv5Client rocketMQv5Client;

    @Resource
    private RoleCommon roleCommon;


    @Override
    public Long createTransportRule(TransportRuleSaveReqVO createReqVO) {
        // 插入
        TransportRuleDO transportRule = BeanUtils.toBean(createReqVO, TransportRuleDO.class);
        transportRule.setRuleCode(CodeGenerator.createCode(SceneTypeEnum.TRANSPORT.getPrefix()));
        transportRuleMapper.insert(transportRule);
        // 返回
        return transportRule.getId();
    }

    @Override
    public void updateTransportRule(TransportRuleSaveReqVO updateReqVO) {
        // 校验存在
        validateTransportRuleExists(updateReqVO.getId());
        // 更新
        TransportRuleDO updateObj = BeanUtils.toBean(updateReqVO, TransportRuleDO.class);
        transportRuleMapper.updateById(updateObj);
    }

    @Override
    public void deleteTransportRule(Long id) {
        // 校验存在
        TransportRuleDO transportRuleDO = validateTransportRuleExists(id);
        // 删除
        transportRuleMapper.deleteById(id);
        //删除数据源
//        transportSourceService.deleteTransportSourceByTransportRuleCode(transportRuleDO.getTransportRuleCode());
        //todo 删除mqtt连接
    }

    private TransportRuleDO validateTransportRuleExists(Long id) {
        TransportRuleDO transportRuleDO = transportRuleMapper.selectById(id);
        if (Objects.isNull(transportRuleDO)) {
            throw exception(TRANSPORT_RULE_NOT_EXISTS);
        }
        return transportRuleDO;
    }

    @Override
    public TransportRuleDO getTransportRule(Long id) {
        return Objects.isNull(id) ? null : transportRuleMapper.selectById(id);
    }



    @Override
    public PageResult<TransportRuleDO> getTransportRulePage(TransportRulePageReqVO pageReqVO) {
        //List<Long> resourceSpaceIds = roleCommon.getResourceSpaceIds();
        //resourceSpaceIds.add(0L);
        //pageReqVO.setResourceSpaceIds(resourceSpaceIds);
        return transportRuleMapper.selectPage(pageReqVO);
    }

    @Override
    public boolean switchTransportRuleStatus(Long id, Integer status) {
        return transportRuleMapper.updateTransportRuleStatus(id, status) > 0;
    }

    @Override
    public void transportData(List<DeviceTransportModel> transportModelList) {
        Long currentTime = System.currentTimeMillis();
        //查找产品编码对应的资源空间id
        if (!CollectionUtils.isEmpty(transportModelList)) {
            Map<String, Long> productResourceIdMap = findResourceIdByProductCode(transportModelList.stream().map(DeviceTransportModel::getProductCode).distinct().toList());
            Map<String, List<DeviceTransportModel>> transportModelMap = transportModelList.stream().collect(Collectors.groupingBy(DeviceTransportModel::getDeviceCode));
            transportModelMap.forEach((key, value) -> {
                DeviceDataTransportModel transportModel = new DeviceDataTransportModel();
                transportModel.setDeviceCode(key);
                transportModel.setMcuChannel(value.get(0).getMcuChannel());
                transportModel.setCurrentTime(currentTime);
                transportModel.setDeviceDataList(value.stream().map(item -> {
                    DeviceDataTransportModel.DeviceData deviceData = new DeviceDataTransportModel.DeviceData();
                    deviceData.setThingIdentity(item.getThingIdentity());
                    deviceData.setThingValue(item.getThingValue());
                    return deviceData;
                }).toList());
                try {
                    rocketMQv5Client.syncSendFifoMessage(IotTopicConstant.TOPIC_DEVICE_COLLECT_DATA,  transportModel,  IotTopicConstant.GROUP_DEVICE_COLLECT_DATA);
                    log.info("数据转发-resourceSpaceId={}-data={}", productResourceIdMap.get(value.get(0).getProductCode()), JsonUtils.toJsonString(transportModel));
                    messageStatisticDayService.insertMessageStatisticDay(new Date(), StatisticImageTypeEnum.TRANSPORT.getType(), 1L);
                } catch (Exception e) {
                    log.error("send rocketmq message error {}", e.getMessage());
                }
            });
        }
    }

    @Override
    public Map<String, Long> findResourceIdByProductCode(List<String> productCodeList) {
        List<ProductDO> productDOList = productService.getProductByCodes(productCodeList);
        if (!CollectionUtils.isEmpty(productDOList)) {
            return productDOList.stream().collect(Collectors.toMap(ProductDO::getProductCode, ProductDO::getResourceSpaceId));
        }
        return new HashMap<>();
    }

}