package cn.powerchina.bjy.link.iot.dto.message;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Map;

/**
 * 设备属性
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DeviceAttributeModel implements Serializable {

    /**
     * 采集时刻时间
     */
    private Long currentTime;

    /**
     * 消息id
     */
    private String messageId;

    /**
     * 设备编码
     */
    private String deviceCode;

    /**
     * 属性信息
     */
    private Map<String, Object> data;

    private String productCode;

    private String thingIdentity;


}
