package cn.powerchina.bjy.link.iot.framework.rule;

import cn.hutool.core.collection.CollectionUtil;
import cn.powerchina.bjy.link.iot.dal.dataobject.scenerule.SceneRuleDO;
import cn.powerchina.bjy.link.iot.enums.EnableStateEnum;
import cn.powerchina.bjy.link.iot.enums.IotRedisConstant;
import cn.powerchina.bjy.link.iot.service.scenerule.SceneRuleService;
import jakarta.annotation.Resource;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 缓存生效时间相关配置
 * @Author: zhaoqiang
 * @CreateDate: 2025/5/29
 */
@Component
public class RuleRedisCache {

    @Resource
    private SceneRuleService sceneRuleService;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    public List<SceneRuleDO> getAllRulesFromRedisOrDB() {
        List<SceneRuleDO> allSceneRulesFromRedis = getAllSceneRulesFromRedis();
        if (CollectionUtil.isEmpty(allSceneRulesFromRedis)) {
            List<SceneRuleDO> dbRuleList = sceneRuleService.getSceneRuleByState(EnableStateEnum.YES.getType());
            saveSceneRules(dbRuleList);
            return dbRuleList;
        }
        return allSceneRulesFromRedis;
    }

    public void saveSceneRule(SceneRuleDO sceneRule) {
        if (sceneRule == null || sceneRule.getId() == null) {
            throw new IllegalArgumentException("SceneRuleDO or its ID cannot be null");
        }
        String key = String.format(IotRedisConstant.RULE_TIME_CONFIG_KEY_PREFIX, sceneRule.getId());
        redisTemplate.opsForValue().set(key, sceneRule);
    }

    /**
     * 批量存入SceneRuleDO对象到Redis
     *
     * @param sceneRules 场景规则对象列表
     */
    public void saveSceneRules(List<SceneRuleDO> sceneRules) {
        if (sceneRules == null || sceneRules.isEmpty()) {
            return;
        }
        Map<String, SceneRuleDO> map = sceneRules.stream()
                .filter(rule -> rule != null && rule.getId() != null)
                .collect(Collectors.toMap(
                        rule -> IotRedisConstant.RULE_TIME_CONFIG_KEY_PREFIX + rule.getId(),
                        rule -> rule
                ));
        redisTemplate.opsForValue().multiSet(map);
    }

    /**
     * 获取所有以iot开头的SceneRuleDO对象
     *
     * @return SceneRuleDO对象列表
     */
    private List<SceneRuleDO> getAllSceneRulesFromRedis() {
        Set<String> keys = redisTemplate.keys(IotRedisConstant.RULE_TIME_CONFIG_KEY_PREFIX + "*");
        if (keys == null || keys.isEmpty()) {
            return Collections.emptyList();
        }

        List<SceneRuleDO> result = new ArrayList<>();
        redisTemplate.executePipelined((RedisCallback<Object>) connection -> {
            for (String key : keys) {
                connection.get(key.getBytes());
            }
            return null;
        }).forEach(obj -> {
            if (obj instanceof SceneRuleDO) {
                result.add((SceneRuleDO) obj);
            }
        });

        return result;
    }

    public void clearAllSceneRules() {
        Set<String> keys = redisTemplate.keys(IotRedisConstant.RULE_TIME_CONFIG_KEY_PREFIX + "*");
        if (keys != null && !keys.isEmpty()) {
            redisTemplate.delete(keys);
        }
    }

    /**
     * 根据ID删除指定的SceneRuleDO缓存
     *
     * @param id 场景规则ID
     */
    public void deleteSceneRuleById(Long id) {
        if (id == null) {
            return;
        }
        redisTemplate.delete(IotRedisConstant.RULE_TIME_CONFIG_KEY_PREFIX + id);
    }
}
