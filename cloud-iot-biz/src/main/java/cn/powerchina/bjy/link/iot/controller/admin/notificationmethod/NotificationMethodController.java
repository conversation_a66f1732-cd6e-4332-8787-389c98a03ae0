package cn.powerchina.bjy.link.iot.controller.admin.notificationmethod;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.excel.core.util.ExcelUtils;
import cn.powerchina.bjy.link.iot.controller.admin.notificationmethod.vo.NotificationMethodPageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.notificationmethod.vo.NotificationMethodRespVO;
import cn.powerchina.bjy.link.iot.controller.admin.notificationmethod.vo.NotificationMethodSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.notificationmethod.NotificationMethodDO;
import cn.powerchina.bjy.link.iot.service.notificationmethod.NotificationMethodService;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;
import static cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 通知方式")
@RestController
@RequestMapping("/iot/notification-method")
@Validated
public class NotificationMethodController {

    @Resource
    private NotificationMethodService notificationMethodService;

    @PostMapping("/create")
    @Operation(summary = "创建通知方式")
//    @PreAuthorize("@ss.hasPermission('iot:notification-method:create')")
    public CommonResult<Long> createNotificationMethod(@Valid @RequestBody NotificationMethodSaveReqVO createReqVO) {
        return success(notificationMethodService.createNotificationMethod(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新通知方式")
//    @PreAuthorize("@ss.hasPermission('iot:notification-method:update')")
    public CommonResult<Boolean> updateNotificationMethod(@Valid @RequestBody NotificationMethodSaveReqVO updateReqVO) {
        notificationMethodService.updateNotificationMethod(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除通知方式")
    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('iot:notification-method:delete')")
    public CommonResult<Boolean> deleteNotificationMethod(@RequestParam("id") Long id) {
        notificationMethodService.deleteNotificationMethod(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得通知方式")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('iot:notification-method:query')")
    public CommonResult<NotificationMethodRespVO> getNotificationMethod(@RequestParam("id") Long id) {
        NotificationMethodDO notificationMethod = notificationMethodService.getNotificationMethod(id);
        return success(BeanUtils.toBean(notificationMethod, NotificationMethodRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得通知方式分页")
//    @PreAuthorize("@ss.hasPermission('iot:notification-method:query')")
    public CommonResult<PageResult<NotificationMethodRespVO>> getNotificationMethodPage(@Valid NotificationMethodPageReqVO pageReqVO) {
        PageResult<NotificationMethodDO> pageResult = notificationMethodService.getNotificationMethodPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, NotificationMethodRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出通知方式 Excel")
//    @PreAuthorize("@ss.hasPermission('iot:notification-method:export')")
//    @OperateLog(type = EXPORT)
    public void exportNotificationMethodExcel(@Valid NotificationMethodPageReqVO pageReqVO,
                                              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<NotificationMethodDO> list = notificationMethodService.getNotificationMethodPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "通知方式.xls", "数据", NotificationMethodRespVO.class,
                BeanUtils.toBean(list, NotificationMethodRespVO.class));
    }

}
