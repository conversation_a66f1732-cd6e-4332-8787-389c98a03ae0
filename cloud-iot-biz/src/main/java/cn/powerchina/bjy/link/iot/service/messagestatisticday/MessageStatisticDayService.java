package cn.powerchina.bjy.link.iot.service.messagestatisticday;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.link.iot.controller.admin.index.bo.StatisticDayBO;
import cn.powerchina.bjy.link.iot.controller.admin.messagestatisticday.vo.MessageStatisticDayPageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.messagestatisticday.vo.MessageStatisticDaySaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.messagestatisticday.MessageStatisticDayDO;
import jakarta.validation.Valid;

import java.util.Date;
import java.util.List;

/**
 * 设备消息数按日统计 Service 接口
 *
 * <AUTHOR>
 */
public interface MessageStatisticDayService {

    /**
     * 创建设备消息数按日统计
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createMessageStatisticDay(@Valid MessageStatisticDaySaveReqVO createReqVO);

    /**
     * 更新设备消息数按日统计
     *
     * @param updateReqVO 更新信息
     */
    void updateMessageStatisticDay(@Valid MessageStatisticDaySaveReqVO updateReqVO);

    /**
     * 删除设备消息数按日统计
     *
     * @param id 编号
     */
    void deleteMessageStatisticDay(Long id);

    /**
     * 获得设备消息数按日统计
     *
     * @param id 编号
     * @return 设备消息数按日统计
     */
    MessageStatisticDayDO getMessageStatisticDay(Long id);

    /**
     * 获得设备消息数按日统计分页
     *
     * @param pageReqVO 分页查询
     * @return 设备消息数按日统计分页
     */
    PageResult<MessageStatisticDayDO> getMessageStatisticDayPage(MessageStatisticDayPageReqVO pageReqVO);

    /**
     * 获得设备消息数
     *
     * @param statisticDay
     * @return
     */
    Long countMessage(Date statisticDay, Integer statisticType);

    /**
     * 根据区间查询统计数量
     *
     * @param startTime
     * @param endTime
     * @param statisticType
     * @return
     */
    List<StatisticDayBO> selectListByStatisticDay(Date startTime, Date endTime, Integer statisticType);

    /**
     * 插入设备消息数按日统计
     *
     * @param statisticDate
     * @param statisticType
     * @param staticCount
     */
    Long insertMessageStatisticDay(Date statisticDate, Integer statisticType, Long staticCount);

    /**
     * 根据消息日期和类型查询
     *
     * @param statisticDay
     * @param statisticType
     * @return
     */
    MessageStatisticDayDO findMessageStatisticDayByDayAndType(Date statisticDay, Integer statisticType);
}
