package cn.powerchina.bjy.link.iot.dal.dataobject.drive;

import cn.powerchina.bjy.cloud.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 驱动 DO
 *
 * <AUTHOR>
 */
@TableName("iot_drive")
@KeySequence("iot_drive_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DriveDO extends BaseDO {

    /**
     * 主键id
     */
    @TableId
    private Long id;
    /**
     * 驱动名称
     */
    private String driveName;
    /**
     * 驱动编码
     */
    private String driveCode;
    /**
     * 备注名称
     */
    private String remark;
    /**
     * 驱动版本
     */
    private String version;
    /**
     * 状态(1:运行中;0:未启动)
     */
    private Integer status;

}