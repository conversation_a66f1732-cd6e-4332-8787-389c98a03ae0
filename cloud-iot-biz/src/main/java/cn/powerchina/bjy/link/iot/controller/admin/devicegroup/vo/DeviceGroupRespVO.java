package cn.powerchina.bjy.link.iot.controller.admin.devicegroup.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 设备分组 Response VO")
@Data
@ExcelIgnoreUnannotated
public class DeviceGroupRespVO {

    @Schema(description = "主键id")
    @ExcelProperty("主键id")
    private Long id;

    @Schema(description = "资源空间id")
    @ExcelProperty("资源空间id")
    private Long resourceSpaceId;

    @Schema(description = "父节点id")
    @ExcelProperty("父节点id")
    private Long parentId;

    @Schema(description = "分组名称")
    @ExcelProperty("分组名称")
    private String groupName;

    @Schema(description = "分组描述")
    @ExcelProperty("分组描述")
    private String groupRemark;

    @Schema(description = "分组层级")
    @ExcelProperty("分组层级")
    private Integer groupLevel;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}