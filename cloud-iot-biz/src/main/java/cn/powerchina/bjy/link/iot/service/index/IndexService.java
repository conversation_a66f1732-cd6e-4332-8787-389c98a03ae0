package cn.powerchina.bjy.link.iot.service.index;

import cn.powerchina.bjy.link.iot.controller.admin.index.bo.IndexTotalBO;
import cn.powerchina.bjy.link.iot.controller.admin.index.bo.StatisticDayIndexBO;
import cn.powerchina.bjy.link.iot.controller.admin.index.vo.StatisticDayImageReqVO;

/**
 * @Description: 描述
 * @Author: yhx
 * @CreateDate: 2024/8/19
 */
public interface IndexService {
    /**
     * 查找首页统计数量
     *
     * @return
     */
    IndexTotalBO findIndexTotal();

    /**
     * 获取折线图
     *
     * @param reqVO
     * @return
     */
    StatisticDayIndexBO getStatisticDayList(StatisticDayImageReqVO reqVO);
}
