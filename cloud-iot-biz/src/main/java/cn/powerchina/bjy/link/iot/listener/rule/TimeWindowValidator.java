package cn.powerchina.bjy.link.iot.listener.rule;

import cn.powerchina.bjy.link.iot.dal.dataobject.scenerule.SceneRuleDO;
import lombok.extern.slf4j.Slf4j;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public class TimeWindowValidator {

    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("HH:mm");

    /**
     * 校验当前时间是否在时间窗口内
     *
     * @param config      时间窗口配置
     * @param currentTime 当前时间
     * @return 是否在时间窗口内
     */
    public static boolean isInTimeWindow(SceneRuleDO config, LocalDateTime currentTime) {
        // 1. 校验生效时段
        if (!checkEffectiveTime(config, currentTime.toLocalTime())) {
            return false;
        }

        // 2. 校验重复规则
        return checkRepeatRule(config, currentTime.toLocalDate());
    }

    /**
     * 校验时间是否在生效时段内
     */
    private static boolean checkEffectiveTime(SceneRuleDO config, LocalTime currentTime) {
        if (config.getEffectiveType() == 1) { // 全天
            return true;
        }

        try {
            // 自定义时段
            LocalTime startTime = LocalTime.parse(config.getEffectiveStartTime(), TIME_FORMATTER);
            LocalTime endTime = LocalTime.parse(config.getEffectiveEndTime(), TIME_FORMATTER);

            // 处理跨天情况(如23:00-01:00)
            if (endTime.isBefore(startTime)) {
                return !currentTime.isBefore(startTime) || !currentTime.isAfter(endTime);
            }
            return !currentTime.isBefore(startTime) && !currentTime.isAfter(endTime);
        } catch (Exception e) {
            log.error("生效时段转换异常{}", e.getMessage());
            return false;
        }
    }

    /**
     * 校验日期是否符合重复规则
     */
    private static boolean checkRepeatRule(SceneRuleDO config, LocalDate currentDate) {
        switch (config.getRepeatType()) {
            case 1: // 每天
                return true;

            case 2: // 指定日期
                return currentDate.equals(config.getRepeatStartDate());

            case 3: // 指定周期
                return !currentDate.isBefore(config.getRepeatStartDate()) &&
                        !currentDate.isAfter(config.getRepeatEndDate());

            case 4: // 自定义(每周特定几天)
                if (config.getRepeatWeekDays() == null || config.getRepeatWeekDays().isEmpty()) {
                    return false;
                }

                List<DayOfWeek> validDays = Arrays.stream(config.getRepeatWeekDays().split(","))
                        .map(String::trim)
                        .map(Integer::parseInt)
                        .map(DayOfWeek::of)
                        .collect(Collectors.toList());

                return validDays.contains(currentDate.getDayOfWeek());

            default:
                return false;
        }
    }
}