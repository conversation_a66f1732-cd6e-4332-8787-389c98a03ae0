package cn.powerchina.bjy.link.iot.mq;

import com.alibaba.fastjson2.JSONObject;
import jakarta.annotation.Resource;
import org.apache.rocketmq.client.apis.ClientException;
import org.apache.rocketmq.client.apis.producer.SendReceipt;
import org.apache.rocketmq.client.apis.producer.Transaction;
import org.apache.rocketmq.client.common.Pair;
import org.apache.rocketmq.client.core.RocketMQClientTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;


@Component
public class RocketMQv5Client {

    private static final Logger log = LoggerFactory.getLogger(RocketMQv5Client.class);

    @Resource
    private RocketMQClientTemplate template;

    /**
     * 发送普通消息
     *
     * @param topic
     * @param message
     */
    public void syncSendNormalMessage(String topic, Object message) {
        SendReceipt sendReceipt = template.syncSendNormalMessage(topic, message);
        log.info("普通消息发送完成：topic={},  message = {}, sendReceipt = {}", topic, message, sendReceipt);
    }

    /**
     * 发送异步普通消息
     *
     * @param topic
     * @param message
     */
    public void asyncSendNormalMessage(String topic, Object message) {
        CompletableFuture<SendReceipt> future = new CompletableFuture<>();
        ExecutorService sendCallbackExecutor = Executors.newCachedThreadPool();
        future.whenCompleteAsync((sendReceipt, throwable) -> {
            if (null != throwable) {
                log.error("发送消息失败", throwable);
                return;
            }
            log.info("发送异步消息消费成功5, messageId={}", sendReceipt.getMessageId());
        }, sendCallbackExecutor);
        CompletableFuture<SendReceipt> completableFuture = template.asyncSendNormalMessage(topic, message, future);
        log.info("发送异步消息成功1, topic={},  message = {},  sendReceipt={}", topic, message, completableFuture);

    }

    /**
     * 发送顺序消息
     *
     * @param topic
     * @param message
     * @param messageGroup
     */
    public void syncSendFifoMessage(String topic, Object message, String messageGroup) {
        try {
            SendReceipt sendReceipt = template.syncSendFifoMessage(topic, message, messageGroup);
            log.info("顺序消息发送完成：topic={},  message = {}, sendReceipt = {}", topic, message, sendReceipt);
        } catch (Exception e) {
            log.info("顺序消息发送失败：topic={},  message = {}, errorMsg = {}", topic, message, e.getMessage());
        }
    }


    /**
     * 发送延时消息
     *
     * @param topic
     * @param message
     * @param delay   单位：秒
     */
    public void syncSendDelayMessage(String topic, Object message, Long delay) {
        SendReceipt sendReceipt = template.syncSendDelayMessage(topic, message, Duration.ofSeconds(delay));
        log.info("延时消息发送完成 ：topic={},  message = {}, sendReceipt = {}", topic, message, sendReceipt);
    }

    /**
     * 发送延时消息
     *
     * @param topic
     * @param message
     * @param duration Duration.ofSeconds(秒)    Duration.ofMinutes(分钟)    Duration.ofHours(小时)
     */
    public void syncSendDelayMessage(String topic, Object message, Duration duration) {
        SendReceipt sendReceipt = template.syncSendDelayMessage(topic, message, duration);
        log.info("延时消息发送完成 ：topic={},  message = {}, sendReceipt = {}", topic, message, sendReceipt);
    }


}