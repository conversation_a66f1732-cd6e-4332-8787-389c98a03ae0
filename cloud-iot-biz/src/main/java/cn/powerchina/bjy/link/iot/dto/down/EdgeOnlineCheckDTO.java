package cn.powerchina.bjy.link.iot.dto.down;

import cn.powerchina.bjy.link.iot.model.MessageToEdge;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 边缘网关检测设备在线状态
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EdgeOnlineCheckDTO implements MessageToEdge, Serializable {

    /**
     * 下发时间
     */
    public Long currentTime;

    /**
     * 采集仪信息
     */
    private EdgeMcuDTO mcuDTO;

    /**
     * 设备信息
     */
    private List<EdgeDeviceDTO> deviceDTOList;
}
