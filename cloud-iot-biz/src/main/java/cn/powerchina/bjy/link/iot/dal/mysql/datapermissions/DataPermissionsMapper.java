package cn.powerchina.bjy.link.iot.dal.mysql.datapermissions;


import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.link.iot.controller.admin.datapermissions.vo.DataPermissionsPageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.modelCategorize.vo.ModelCategorizeListReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.datapermissions.DataPermissionsDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.modelCategorize.ModelCategorizeDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.modelTemplate.ModelTemplateDO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 数据权限 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface DataPermissionsMapper extends BaseMapperX<DataPermissionsDO> {

    default PageResult<DataPermissionsDO> selectPage(DataPermissionsPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<DataPermissionsDO>()
                .eqIfPresent(DataPermissionsDO::getRoleId, reqVO.getRoleId())
                .orderByDesc(DataPermissionsDO::getId));
    }
    @Select("select idp.data_id from iot_data_permissions idp where idp.role_id=#{roleId} and idp.`level` =#{level}")
    List<DataPermissionsDO>  selectListByRoleId(@Param("roleId") Long roleId,@Param("level") Integer level);

    @Select("select idp.data_id from iot_data_permissions idp where idp.role_id=#{roleId} and idp.parent_id =#{parentId} and idp.`level` =#{level}")
    List<DataPermissionsDO>  selectByRoleAndParentId(@Param("roleId") Long roleId,@Param("parentId") String parentId,@Param("level") Integer level);
    @Select("select idp.* from iot_data_permissions idp where idp.role_id=#{roleId}")
    List<DataPermissionsDO>  selectAllByRoleId(@Param("roleId") Long roleId);
    default List<DataPermissionsDO> selectList(Long roleId) {
        return selectList(new LambdaQueryWrapperX<DataPermissionsDO>()
                .eqIfPresent(DataPermissionsDO::getRoleId,roleId));
    }
    @Delete("DELETE from iot_data_permissions WHERE role_id=#{roleId}")
    int deleteByRoleId(@Param("roleId") Long roleId);

}
