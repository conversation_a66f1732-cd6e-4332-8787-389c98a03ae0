package cn.powerchina.bjy.link.iot.model;

import cn.powerchina.bjy.link.iot.controller.admin.modelCategorize.vo.ModelCategorizeRespVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.modelCategorize.ModelCategorizeDO;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Treeselect树结构实体类
 * 
 * <AUTHOR>
 */
public class TreeSelect implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 节点ID */
    private Long id;

    /** 节点名称 */
    private String label;

    /** 节点名称 */
    private Integer level;

    /** 子节点 */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<TreeSelect> children;

    public TreeSelect()
    {

    }

    public TreeSelect(ModelCategorizeRespVO respVO)
    {
        this.id = respVO.getId();
        this.label = respVO.getCategorizeName();
        this.level=respVO.getLevel();
        this.children = respVO.getChildren().stream().map(TreeSelect::new).collect(Collectors.toList());
    }
    public Long getId()
    {
        return id;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public String getLabel()
    {
        return label;
    }

    public void setLabel(String label)
    {
        this.label = label;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public List<TreeSelect> getChildren()
    {
        return children;
    }

    public void setChildren(List<TreeSelect> children)
    {
        this.children = children;
    }
}
