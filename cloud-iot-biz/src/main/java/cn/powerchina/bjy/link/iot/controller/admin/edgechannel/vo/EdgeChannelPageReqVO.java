package cn.powerchina.bjy.link.iot.controller.admin.edgechannel.vo;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 通道分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class EdgeChannelPageReqVO extends PageParam {

    @Schema(description = "关联的驱动code")
    private String driverCode;

    @Schema(description = "关联的边缘实例code")
    private String edgeCode;

    @Schema(description = "连接方式（1-RTU；2-TCP）", example = "2")
    private Integer connectType;

    @Schema(description = "通道编码")
    private String channelCode;

    @Schema(description = "通道名称", example = "")
    private String channelName;

    @Schema(description = "差异化扩展")
    private String extra;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}