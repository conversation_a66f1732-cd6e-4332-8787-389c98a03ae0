package cn.powerchina.bjy.link.iot.util;

import cn.powerchina.bjy.link.iot.controller.admin.alarmtemplate.vo.AlarmMsgRespVO;
import cn.powerchina.bjy.link.iot.controller.admin.notificationmethod.vo.NotificationAccountVo;
import cn.powerchina.bjy.link.iot.controller.admin.notificationrecord.vo.NotificationRecordSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.notificationconfig.NotificationConfigDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.notificationmethod.NotificationMethodDO;
import cn.powerchina.bjy.link.iot.enums.ErrorCodeConstants;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.dromara.hutool.extra.mail.MailAccount;
import org.dromara.hutool.extra.mail.MailUtil;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;

/**
 * @Description: 邮件发送工具类
 * @Author: handl
 * @CreateDate: 2025/6/03
 */
@Slf4j
@Component
public class EmailSendUtils {
    public static List<NotificationRecordSaveReqVO> send(NotificationMethodDO notificationMethodDo, NotificationConfigDO notificationConfig, AlarmMsgRespVO alarmMsgRespVO) {
        MailAccount mailAccount = new MailAccount();
        mailAccount.setFrom(notificationConfig.getUsername());
        mailAccount.setUser(notificationConfig.getUsername());
        mailAccount.setPass(notificationConfig.getNotificationPass().toCharArray());
        mailAccount.setHost(notificationConfig.getNotificationHost());
        mailAccount.setPort(Integer.valueOf(notificationConfig.getNotificationPort()));
        mailAccount.setAuth(true);
        mailAccount.setSslEnable(true);
        mailAccount.setStarttlsEnable(false);
        String notificationAccount = notificationMethodDo.getNotificationAccount();
        try {
            if (Objects.isNull(notificationAccount)) {
                throw exception(ErrorCodeConstants.NOTIFICATION_ACCOUNT_NOT_EXISTS);
            }
        } catch (Exception e) {
            log.error("邮件账号错误 {}", e.getMessage());
        }

        String content = buildContent(alarmMsgRespVO);
        String recordContent = buildRecordContent(alarmMsgRespVO);
        String deviceName = alarmMsgRespVO.getDeviceName() != null ? alarmMsgRespVO.getDeviceName() + "，" : "";
        String subject = "【" + alarmMsgRespVO.getAlarmLevel() + "】" + deviceName + alarmMsgRespVO.getAlarmName();

        List<NotificationAccountVo> firstAccount = null;
        try {
            firstAccount = parseAccount(notificationAccount);
        } catch (Exception e) {
            log.error("设备告警邮件发送失败", e);
        }
        assert firstAccount != null;
        List<NotificationRecordSaveReqVO> notificationRecordList = new ArrayList<>();
        firstAccount.forEach(account -> {
            try {
                String messageId = MailUtil.send(mailAccount, account.getAccount(), subject, content, true); // 设置为 true 发送 HTML 格式
                log.info("邮件告警消息发送成功messageId：{}", messageId);
                NotificationRecordSaveReqVO notificationRecordSaveReqVO = createNotificationRecord(alarmMsgRespVO.getAlarmName(), account.getAccount(),
                        true, notificationConfig.getUsername(), recordContent, null);
                notificationRecordList.add(notificationRecordSaveReqVO);
            } catch (Exception e) {
                log.error("发送邮件失败:{}", e.getMessage());
                NotificationRecordSaveReqVO notificationRecordSaveReqVO = createNotificationRecord(alarmMsgRespVO.getAlarmName(), account.getAccount(),
                        false, notificationConfig.getUsername(), recordContent, e.getMessage());
                notificationRecordList.add(notificationRecordSaveReqVO);
            }
        });
        return notificationRecordList;
    }

    public static List<NotificationAccountVo> parseAccount(String notificationAccount) throws Exception {
        ObjectMapper mapper = new ObjectMapper();
        return mapper.readValue(notificationAccount,
                mapper.getTypeFactory().constructCollectionType(List.class, NotificationAccountVo.class));
    }


    public static String buildContent(AlarmMsgRespVO alarmMsgRespVO) {

        String deviceName = alarmMsgRespVO.getDeviceName() != null ? alarmMsgRespVO.getDeviceName() : "无";
        String deviceCode = alarmMsgRespVO.getDeviceCode() != null ? alarmMsgRespVO.getDeviceCode() : "无";
        String productName = alarmMsgRespVO.getProductName() != null ? alarmMsgRespVO.getProductName() : "无";
        String content =
                "<html>"
                        + "<body>"
                        + "<p><strong>【告警时间】：</strong><span style='color: #333333;'>" + alarmMsgRespVO.getTriggerTime() + "</span></p>"
                        + "<p><strong>【设备信息】</strong><span style='color: #333333;'></span></p>"
                        + "<ul>"
                        + "<li>" + "触发设备名称：" + deviceName + "</li>"
                        + "<li>" + "触发设备ID：" + deviceCode + "</li>"
                        + "<li>" + "设备类型：" + productName + "</li>"
                        + "<li>" + "触发规则名称：" + alarmMsgRespVO.getRuleName() + "</li>"
                        + "</ul>"
                        + "<p><strong>【告警级别】：</strong><span style='color: #333333;'>" + alarmMsgRespVO.getAlarmLevel() + "</span></p>"
                        + "<p><strong>【告警内容】：</strong><span style='color: #ff0000;'>" + alarmMsgRespVO.getAlarmContent() + "</span></p>"
                        + "</div>"
                        + "</body>"
                        + "</html>";
        return content;
    }

    public static String buildRecordContent(AlarmMsgRespVO alarmMsgRespVO) {

        String deviceName = alarmMsgRespVO.getDeviceName() != null ? alarmMsgRespVO.getDeviceName() : "无";
        String deviceCode = alarmMsgRespVO.getDeviceCode() != null ? alarmMsgRespVO.getDeviceCode() : "无";
        String productName = alarmMsgRespVO.getProductName() != null ? alarmMsgRespVO.getProductName() : "无";
        String alarmContent = "【告警内容】：" + alarmMsgRespVO.getAlarmContent();
        if (alarmContent.length() > 30) {
            alarmContent = alarmContent.replaceAll("(.{30})(?!$)", "$1\n");
        }

        String content =
                "【告警时间】：" + alarmMsgRespVO.getTriggerTime() + "\n"
                        + "【设备信息】：" + "\n"
                        + "     触发设备名称：" + deviceName + "\n"
                        + "     触发设备ID：" + deviceCode + "\n"
                        + "     设备类型：" + productName + "\n"
                        + "     触发规则名称：" + alarmMsgRespVO.getRuleName() + "\n"
                        + "【告警级别】：" + alarmMsgRespVO.getAlarmLevel() + "\n"
                        + alarmContent + "\n";
        return content;
    }

    /**
     * 生成告警记录
     */
    public static NotificationRecordSaveReqVO createNotificationRecord(String subject, String account, Boolean sendStatus, String inbox, String content2, String remark) {
        NotificationRecordSaveReqVO notificationRecordSaveReqVO = new NotificationRecordSaveReqVO();
        notificationRecordSaveReqVO.setSubject(subject);
        notificationRecordSaveReqVO.setInbox(account);
        notificationRecordSaveReqVO.setOutbox(inbox);
        notificationRecordSaveReqVO.setSendStatus(sendStatus);
        notificationRecordSaveReqVO.setSendTime(LocalDateTime.now());
        notificationRecordSaveReqVO.setEmailContent(content2);
        notificationRecordSaveReqVO.setRemark(remark);
        return notificationRecordSaveReqVO;
    }

}
