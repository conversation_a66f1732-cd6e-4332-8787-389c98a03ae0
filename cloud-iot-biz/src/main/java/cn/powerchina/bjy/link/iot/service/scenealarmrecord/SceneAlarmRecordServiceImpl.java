package cn.powerchina.bjy.link.iot.service.scenealarmrecord;

import cn.powerchina.bjy.cloud.framework.common.exception.enums.GlobalErrorCodeConstants;
import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.cloud.framework.web.core.util.WebFrameworkUtils;
import cn.powerchina.bjy.cloud.system.api.permission.PermissionApi;
import cn.powerchina.bjy.cloud.system.api.permission.dto.RolePageRespDTO;
import cn.powerchina.bjy.cloud.system.api.user.AdminUserApi;
import cn.powerchina.bjy.cloud.system.api.user.dto.AdminUserRespDTO;
import cn.powerchina.bjy.cloud.system.enums.permission.DataScopeEnum;
import cn.powerchina.bjy.link.iot.common.role.RoleCommon;
import cn.powerchina.bjy.link.iot.controller.admin.scenealarmrecord.vo.SceneAlarmRecordPageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.scenealarmrecord.vo.SceneAlarmRecordReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.scenealarmrecord.vo.SceneAlarmRecordRespVO;
import cn.powerchina.bjy.link.iot.controller.admin.scenealarmrecord.vo.SceneAlarmRecordSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.datapermissions.DataPermissionsDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.device.DeviceDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.product.ProductDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.roledatapermissions.RoleDataPermissionsDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.scenealarmrecord.SceneAlarmRecordDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.scenealarmrecorddetail.SceneAlarmRecordDetailDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.scenerule.SceneRuleDO;
import cn.powerchina.bjy.link.iot.dal.mysql.datapermissions.DataPermissionsMapper;
import cn.powerchina.bjy.link.iot.dal.mysql.device.DeviceMapper;
import cn.powerchina.bjy.link.iot.dal.mysql.product.ProductMapper;
import cn.powerchina.bjy.link.iot.dal.mysql.roledatapermissions.RoleDataPermissionsMapper;
import cn.powerchina.bjy.link.iot.dal.mysql.scenealarmrecord.SceneAlarmRecordMapper;
import cn.powerchina.bjy.link.iot.dal.mysql.scenealarmrecorddetail.SceneAlarmRecordDetailMapper;
import cn.powerchina.bjy.link.iot.dal.mysql.scenerule.SceneRuleMapper;
import cn.powerchina.bjy.link.iot.enums.AlarmStatusEnum;
import cn.powerchina.bjy.link.iot.enums.RecoveryTypeEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.link.iot.enums.ErrorCodeConstants.*;

/**
 * 告警记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SceneAlarmRecordServiceImpl implements SceneAlarmRecordService {

    @Resource
    private SceneAlarmRecordMapper sceneAlarmRecordMapper;

    @Resource
    private SceneAlarmRecordDetailMapper sceneAlarmRecordDetailMapper;

    @Resource
    private SceneRuleMapper sceneRuleMapper;

    @Resource
    private PermissionApi permissionApi;

    @Resource
    private RoleDataPermissionsMapper roleDataPermissionsMapper;

    @Resource
    private DataPermissionsMapper dataPermissionsMapper;


    @Resource
    private AdminUserApi adminUserApi;

    @Resource
    private DeviceMapper deviceMapper;

    @Resource
    private ProductMapper productMapper;

    @Resource
    private RoleCommon roleCommon;

    @Override
    public Long createSceneAlarmRecord(SceneAlarmRecordSaveReqVO createReqVO) {
        SceneAlarmRecordDO checkDo = sceneAlarmRecordMapper.selectOne(new LambdaQueryWrapper<SceneAlarmRecordDO>().eq(SceneAlarmRecordDO::getRuleId, createReqVO.getRuleId())
                .in(SceneAlarmRecordDO::getAlarmStatus, AlarmStatusEnum.TRIGGER.getType(), AlarmStatusEnum.CHECKING.getType()));
        SceneAlarmRecordDO sceneAlarmRecord = BeanUtils.toBean(createReqVO, SceneAlarmRecordDO.class);
        if (checkDo != null) {
            sceneAlarmRecord.setAlarmNum(checkDo.getAlarmNum() + 1);
            sceneAlarmRecordMapper.updateById(sceneAlarmRecord);
        } else {
            // 插入
            sceneAlarmRecordMapper.insert(sceneAlarmRecord);
        }
        // 返回
        return sceneAlarmRecord.getId();
    }

    @Override
    public void updateSceneAlarmRecord(SceneAlarmRecordSaveReqVO updateReqVO) {
        // 校验存在
        validateSceneAlarmRecordExists(updateReqVO.getId());
        // 更新
        SceneAlarmRecordDO updateObj = BeanUtils.toBean(updateReqVO, SceneAlarmRecordDO.class);
        sceneAlarmRecordMapper.updateById(updateObj);
    }

    @Override
    public void deleteSceneAlarmRecord(Long id) {
        // 校验存在
        validateSceneAlarmRecordExists(id);
        // 删除
        sceneAlarmRecordMapper.deleteById(id);
    }

    private void validateSceneAlarmRecordExists(Long id) {
        if (sceneAlarmRecordMapper.selectById(id) == null) {
            throw exception(SCENE_ALARM_RECORD_NOT_EXISTS);
        }
    }

    @Override
    public SceneAlarmRecordDO getSceneAlarmRecord(Long id) {
        return sceneAlarmRecordMapper.selectById(id);
    }

    @Override
    public SceneAlarmRecordRespVO getAlarmRecordDetail(Long alarmId, Long alarmDetailId) {
        SceneAlarmRecordDO sceneAlarmRecordDO = sceneAlarmRecordMapper.selectById(alarmId);
        SceneAlarmRecordDetailDO sceneAlarmRecordDetailDO = sceneAlarmRecordDetailMapper.selectById(alarmDetailId);
        sceneAlarmRecordDO.setTriggerTime(sceneAlarmRecordDetailDO.getAlarmTime());
        SceneAlarmRecordRespVO sceneAlarmRecordVo = BeanUtils.toBean(sceneAlarmRecordDO, SceneAlarmRecordRespVO.class);
        if (StringUtils.isNotEmpty(sceneAlarmRecordDO.getDeviceCode())) {
            DeviceDO deviceDO = deviceMapper.selectByCode(sceneAlarmRecordDO.getDeviceCode());
            if (deviceDO != null) {
                sceneAlarmRecordVo.setDeviceName(StringUtils.isNotEmpty(deviceDO.getDeviceName()) ? deviceDO.getDeviceName() : null);
            }
        }
        return sceneAlarmRecordVo;
    }

    @Override
    public PageResult<SceneAlarmRecordRespVO> getSceneAlarmRecordPage(SceneAlarmRecordPageReqVO pageReqVO) {
        if (Objects.isNull(pageReqVO.getResourceSpaceId())) {
            pageReqVO.setResourceSpaceIdList(roleCommon.getResourceSpaceIds());
        }
        //检查是否是超级管理员
        boolean superAdmin = roleCommon.checkIfSuperAdmin();
        List<String> deviceCodes = getDeviceCodes();
        List<Long> ruleIds = new ArrayList<>();
        if (!superAdmin) {
            //查询有权限的设备
            if (CollectionUtils.isEmpty(deviceCodes)) {
                return new PageResult<>(Collections.emptyList(), 0L);
            }
        }
        //查询当前用户创建的场景联动
        List<SceneRuleDO> sceneRuleDOList = sceneRuleMapper.selectList(new LambdaQueryWrapperX<SceneRuleDO>()
                .eq(SceneRuleDO::getCreator, WebFrameworkUtils.getLoginUserId())
                .eq(SceneRuleDO::getStatus, 1)
                .eqIfPresent(SceneRuleDO::getResourceSpaceId, pageReqVO.getResourceSpaceId())
                .inIfPresent(SceneRuleDO::getResourceSpaceId, pageReqVO.getResourceSpaceIdList())
        );
        if (!CollectionUtils.isEmpty(sceneRuleDOList)) {
            sceneRuleDOList.forEach(items -> ruleIds.add(items.getId()));
        }
        pageReqVO.setDeviceCodes(deviceCodes);
        pageReqVO.setRuleIds(ruleIds);

        //PageResult<SceneAlarmRecordRespVO> result = sceneAlarmRecordMapper.selectPage(pageReqVO);
        PageResult<SceneAlarmRecordRespVO> result = new PageResult<>();
        pageReqVO.setPageNo(pageReqVO.getPageNo() - 1);
        Long total = (long) sceneAlarmRecordMapper.selectRecordCount(pageReqVO);
        List<SceneAlarmRecordRespVO> selectRecordList = sceneAlarmRecordMapper.selectRecordList(pageReqVO);

        if (!CollectionUtils.isEmpty(selectRecordList)) {
            selectRecordList.forEach(item -> {
                if (item.getProcessUserId() != null) {
                    Long loginUserId = item.getProcessUserId();
                    AdminUserRespDTO userRespDTO = adminUserApi.getUser(loginUserId).getData();
                    item.setProcessUserName(Objects.nonNull(userRespDTO) ? userRespDTO.getName() : null);
                }
                if (StringUtils.isNotEmpty(item.getDeviceCode())) {
                    DeviceDO deviceDO = deviceMapper.selectByCode(item.getDeviceCode());
                    if (deviceDO != null) {
                        item.setDeviceName(StringUtils.isNotEmpty(deviceDO.getDeviceName()) ? deviceDO.getDeviceName() : null);
                        item.setDeviceSerial(StringUtils.isNotEmpty(deviceDO.getDeviceSerial()) ? deviceDO.getDeviceSerial() : null);
                    }
                }
                if (StringUtils.isNotEmpty(item.getProductCode())) {
                    ProductDO productDO = productMapper.selectByCode(item.getProductCode());
                    if (productDO != null) {
                        item.setProductName(StringUtils.isNotEmpty(productDO.getProductName()) ? productDO.getProductName() : null);
                    }
                }
            });
        }
        result.setList(selectRecordList);
        result.setTotal(total);
        return result;
    }


    private List<String> getDeviceCodes() {
        List<String> deviceCodes = new ArrayList<>();
        CommonResult<List<RolePageRespDTO>> result = permissionApi.getPermissionRoleByUserId(WebFrameworkUtils.getLoginUserId());
        if (GlobalErrorCodeConstants.SUCCESS.getCode().equals(result.getCode())) {
            List<RolePageRespDTO> rolePageRespDTOList = result.getData();
            rolePageRespDTOList.forEach(role -> {
                RoleDataPermissionsDO roleDataPermissionsDO = roleDataPermissionsMapper.selectAllByRoleId(role.getId());
                if (roleDataPermissionsDO != null) {
                    if (DataScopeEnum.DEPT_CUSTOM.getScope().equals(roleDataPermissionsDO.getDataScope())) {
                        List<DataPermissionsDO> doList = dataPermissionsMapper.selectListByRoleId(roleDataPermissionsDO.getRoleId(), 1);
                        if (!CollectionUtils.isEmpty(doList)) {
                            doList.forEach(resource -> {
                                List<DataPermissionsDO> doList1 = dataPermissionsMapper.selectByRoleAndParentId(roleDataPermissionsDO.getRoleId(), resource.getDataId(), 2);
                                if (!CollectionUtils.isEmpty(doList1)) {
                                    QueryWrapper<ProductDO> queryWrapper = new QueryWrapper<>();
                                    queryWrapper.eq("resource_space_id", resource.getDataId());
                                    List<ProductDO> productList = productMapper.selectList(queryWrapper);
                                    if (!CollectionUtils.isEmpty(productList)) {
                                        productList.forEach(product -> {
                                            QueryWrapper<DeviceDO> queryWrappers = new QueryWrapper<>();
                                            queryWrappers.eq("product_code", product.getProductCode());
                                            List<DeviceDO> deviceList = deviceMapper.selectList(queryWrappers);
                                            if (!CollectionUtils.isEmpty(deviceList)) {
                                                deviceList.forEach(item -> deviceCodes.add(item.getDeviceCode()));
                                            }
                                        });
                                    }
                                } else {
                                    List<DataPermissionsDO> doList2 = dataPermissionsMapper.selectByRoleAndParentId(roleDataPermissionsDO.getRoleId(), resource.getDataId(), 3);
                                    if (!CollectionUtils.isEmpty(doList2)) {
                                        doList2.forEach(item -> {
                                            List<DataPermissionsDO> doList6 = dataPermissionsMapper.selectByRoleAndParentId(roleDataPermissionsDO.getRoleId(), item.getDataId(), 4);
                                            ProductDO productDO = productMapper.selectById(item.getDataId());
                                            if (!CollectionUtils.isEmpty(doList6)) {
                                                if (productDO != null) {
                                                    QueryWrapper<DeviceDO> queryWrappers = new QueryWrapper<>();
                                                    queryWrappers.eq("product_code", productDO.getProductCode());
                                                    List<DeviceDO> list1 = deviceMapper.selectList(queryWrappers);
                                                    if (!CollectionUtils.isEmpty(list1)) {
                                                        list1.forEach(items -> deviceCodes.add(items.getDeviceCode()));
                                                    }
                                                }
                                            } else {
                                                List<DataPermissionsDO> doList3 = dataPermissionsMapper.selectByRoleAndParentId(roleDataPermissionsDO.getRoleId(), item.getDataId(), 5);
                                                if (!CollectionUtils.isEmpty(doList3)) {
                                                    doList3.forEach(items -> deviceCodes.add(deviceMapper.selectById(items.getDataId()).getDeviceCode()));
                                                }
                                            }
                                        });
                                    }
                                }
                            });
                        }
                    }
                } else {
                    deviceCodes.add("-1");
                }
            });
        }
        return deviceCodes;
    }


    @Override
    public List<SceneAlarmRecordDO> getSceneAlarmRecordList(SceneAlarmRecordReqVO reqVO) {
        LambdaQueryWrapper<SceneAlarmRecordDO> lambdaWrapper = new LambdaQueryWrapper<>();
        if (reqVO.getRuleId() != null) {
            lambdaWrapper.eq(SceneAlarmRecordDO::getRuleId, reqVO.getRuleId());
        }
        if (reqVO.getResourceSpaceId() != null) {
            lambdaWrapper.eq(SceneAlarmRecordDO::getResourceSpaceId, reqVO.getResourceSpaceId());
        }
        if (reqVO.getTriggerTime() != null) {
            lambdaWrapper.gt(SceneAlarmRecordDO::getTriggerTime, reqVO.getTriggerTime());
        }
        if (!CollectionUtils.isEmpty(reqVO.getAlarmStatusList())) {
            lambdaWrapper.in(SceneAlarmRecordDO::getAlarmStatus, reqVO.getAlarmStatusList());
        }
        return sceneAlarmRecordMapper.selectList(lambdaWrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean processAlarmRecord(Long id, String processRecord, Long processUserId, String processUserName) {
        // 1. 校验告警记录是否存在
        SceneAlarmRecordDO record = sceneAlarmRecordMapper.selectById(id);
        if (record == null) {
            throw exception(SCENE_ALARM_RECORD_NOT_EXISTS);
        }

        // 3. 检查告警状态是否为触发状态
        if (!AlarmStatusEnum.TRIGGER.getType().equals(record.getAlarmStatus())) {
            throw exception(SCENE_RULE_ALARM_NOT_EXISTS);
        }
        // 4. 更新告警记录处理信息
        SceneAlarmRecordDO updateRecord = new SceneAlarmRecordDO();
        updateRecord.setId(id);
        updateRecord.setProcessRecord(processRecord);
        updateRecord.setProcessUserId(processUserId);
        updateRecord.setProcessTime(LocalDateTime.now());
        // 待验证状态
        updateRecord.setAlarmStatus(AlarmStatusEnum.CHECKING.getType());
        sceneAlarmRecordMapper.updateById(updateRecord);
        return true;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean recoverAlarmRecord(Long id, Long recoveryUserId, String recoveryUserName) {
        // 1. 校验告警记录是否存在
        SceneAlarmRecordDO record = sceneAlarmRecordMapper.selectById(id);
        if (record == null) {
            throw exception(SCENE_ALARM_RECORD_NOT_EXISTS);
        }
        // 3. 检查告警状态是否为触发状态
        if (!AlarmStatusEnum.CHECKING.getType().equals(record.getAlarmStatus())) {
            throw exception(SCENE_RULE_ALARM_NOT_VERIFY_EXISTS);
        }
        // 4. 更新告警记录恢复信息
        SceneAlarmRecordDO updateRecord = new SceneAlarmRecordDO();
        updateRecord.setId(id);
        updateRecord.setRecoveryUserId(recoveryUserId);
        updateRecord.setRecoveryTime(LocalDateTime.now());
        // 人工恢复
        updateRecord.setRecoveryType(RecoveryTypeEnum.MANUAL_RESTORE.getType());
        // 恢复状态
        updateRecord.setAlarmStatus(AlarmStatusEnum.RESTORE.getType());
        sceneAlarmRecordMapper.updateById(updateRecord);

        return true;
    }

}