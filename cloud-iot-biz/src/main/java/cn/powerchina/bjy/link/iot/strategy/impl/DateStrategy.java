package cn.powerchina.bjy.link.iot.strategy.impl;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.powerchina.bjy.link.iot.dto.up.EdgeReadPropertyValue;
import cn.powerchina.bjy.link.iot.strategy.ProductModelStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * DateStrategy
 * 基础的就校验下位数 和 是否是今年的时间
 *
 * <AUTHOR>
 **/

@Slf4j
@Component("dateStrategy")
public class DateStrategy implements ProductModelStrategy {

    @Value("${bjylink.timestamp-length:13}")
    private int timestampLength;

    @Override
    public boolean shouldIgnore(Object context, String strategy) {
        if (null == context) {
            return true;
        }
        long data = (long) context;
        String strData = String.valueOf(data);
        if (timestampLength != strData.length()) {
            log.warn("物模型数据类型为【date】数据【{}】长度不等于【{}】", strData, timestampLength);
            return true;
        }
        if (LocalDateTimeUtil.of(data).getYear() != LocalDateTimeUtil.now().getYear()) {
            log.warn("物模型数据类型为【date】数据【{}】不是今年的数据【{}】", strData, LocalDateTimeUtil.now().getYear());
            return true;
        }
        return false;
    }

    @Override
    public boolean shouldIgnore(String context, String strategy, EdgeReadPropertyValue.EdgeDevicePropertyValueDTO valueDTO) {
        if (null == context) {
            return true;
        }
        if (timestampLength != context.length()) {
            log.warn("物模型数据类型为【date】数据【{}】长度不等于【{}】", context, timestampLength);
            return true;
        }
        long data = Long.parseLong(context);
        if (LocalDateTimeUtil.of(data).getYear() != LocalDateTimeUtil.now().getYear()) {
            log.warn("物模型数据类型为【date】数据【{}】不是今年的数据【{}】", data, LocalDateTimeUtil.now().getYear());
            return true;
        }
        return false;
    }

}
