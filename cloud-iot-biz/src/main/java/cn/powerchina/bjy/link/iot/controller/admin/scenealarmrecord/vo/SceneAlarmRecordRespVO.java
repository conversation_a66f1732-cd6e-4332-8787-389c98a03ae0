package cn.powerchina.bjy.link.iot.controller.admin.scenealarmrecord.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 告警记录 Response VO")
@Data
@ExcelIgnoreUnannotated
public class SceneAlarmRecordRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "10391")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "资源空间ID", example = "14286")
    @ExcelProperty("资源空间ID")
    private Long resourceSpaceId;

    @Schema(description = "规则ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "19931")
    @ExcelProperty("规则ID")
    private Long ruleId;

    @Schema(description = "规则名称", example = "赵六")
    @ExcelProperty("规则名称")
    private String ruleName;

    @Schema(description = "关联的告警模板ID", example = "9071")
    @ExcelProperty("关联的告警模板ID")
    private Long alarmTemplateId;

    @Schema(description = "告警名称", example = "王五")
    @ExcelProperty("告警名称")
    private String alarmName;

    @Schema(description = "告警内容")
    @ExcelProperty("告警内容")
    private String alarmContent;

    @Schema(description = "告警等级(1-轻微,2-中等,3-严重,4-非常严重)")
    @ExcelProperty("告警等级(1-轻微,2-中等,3-严重,4-非常严重)")
    private Integer alarmLevel;

    @Schema(description = "告警状态(1-触发,2-待验证,3-恢复)", example = "2")
    @ExcelProperty("告警状态(1-触发,2-待验证,3-恢复)")
    private Integer alarmStatus;

    @Schema(description = "触发时间")
    @ExcelProperty("触发时间")
    private LocalDateTime triggerTime;

    @Schema(description = "设备编码")
    @ExcelProperty("设备编码")
    private String deviceCode;

    @Schema(description = "设备名称")
    @ExcelProperty("设备名称")
    private String deviceName;

    @Schema(description = "设备唯一标识")
    @ExcelProperty("设备唯一标识")
    private String deviceSerial;

    @Schema(description = "产品编码")
    @ExcelProperty("产品编码")
    private String productCode;

    @Schema(description = "产品名称")
    @ExcelProperty("产品名称")
    private String productName;

    @Schema(description = "处理时间")
    @ExcelProperty("处理时间")
    private LocalDateTime processTime;

    @Schema(description = "告警处理")
    @ExcelProperty("告警处理")
    private String processRecord;

    @Schema(description = "处理用户ID", example = "28553")
    @ExcelProperty("处理用户ID")
    private Long processUserId;
    @Schema(description = "处理用户账号")
    @ExcelProperty("处理用户账号")
    private String processUserName;

    @Schema(description = "恢复类型(0-自动恢复,1-人工恢复)", example = "2")
    @ExcelProperty("恢复类型(0-自动恢复,1-人工恢复)")
    private Integer recoveryType;

    @Schema(description = "恢复时间")
    @ExcelProperty("恢复时间")
    private LocalDateTime recoveryTime;

    @Schema(description = "恢复用户ID", example = "5245")
    @ExcelProperty("恢复用户ID")
    private Long recoveryUserId;

    @Schema(description = "告警次数", example = "1")
    private Integer alarmNum;

    @Schema(description = "最后告警时间")
    private LocalDateTime lastAlarmTime;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
