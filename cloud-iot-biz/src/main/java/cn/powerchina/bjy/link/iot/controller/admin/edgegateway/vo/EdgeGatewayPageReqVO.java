package cn.powerchina.bjy.link.iot.controller.admin.edgegateway.vo;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


@Schema(description = "管理后台 - 边缘网关分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class EdgeGatewayPageReqVO extends PageParam {

    @Schema(description = "所属产品编码")
    private String productCode;

    @Schema(description = "所属产品名称", example = "")
    private String productName;

    @Schema(description = "网关实例编码")
    private String edgeCode;

    @Schema(description = "边缘网关名称",example = "")
    private String edgeName;

    @Schema(description = "边缘网关应用名",example = "")
    private String edgeServiceName;

    @Schema(description = "边缘网关host")
    private String edgeHost;

    @Schema(description = "边缘网关端口")
    private Integer edgePort;

    @Schema(description = "描述", example = "你说的对")
    private String description;

    @Schema(description = "资源空间id", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("资源空间id")
    private Long resourceSpaceId;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    private String deviceName;

    @Schema(description = "边缘计算实例ID集合")
    private List<String> codes;

}