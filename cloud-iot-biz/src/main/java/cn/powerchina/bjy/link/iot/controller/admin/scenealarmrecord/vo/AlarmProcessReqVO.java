package cn.powerchina.bjy.link.iot.controller.admin.scenealarmrecord.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
@Schema(description = "管理后台 - 告警处理请求 VO")
public class AlarmProcessReqVO {
    @Schema(description = "告警记录ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "31848")
    @NotNull(message = "告警记录ID不能为空")
    private Long id;

    @Schema(description = "处理记录", requiredMode = Schema.RequiredMode.REQUIRED, example = "已检查设备,排除故障")
    @NotNull(message = "处理记录不能为空")
    private String processRecord;
}
