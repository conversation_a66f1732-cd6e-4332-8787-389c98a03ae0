package cn.powerchina.bjy.link.iot.framework.rule;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.connection.MessageListener;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class RuleUpdateSubscriber implements MessageListener {


    @Resource
    private RuleRedisCache ruleRedisCache;

    @Override
    public void onMessage(Message message, byte[] pattern) {
        String channel = new String(message.getChannel());
        String body = new String(message.getBody());
        log.info("Received message on channel {}: {}", channel, body);

        if (channel.equals("RULE_UPDATE_TOPIC")) {
            ruleRedisCache.clearAllSceneRules();
        }
    }
}