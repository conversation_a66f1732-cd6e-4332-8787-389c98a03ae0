package cn.powerchina.bjy.link.iot.dal.dataobject.edgegateway;

import cn.powerchina.bjy.cloud.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 边缘网关 DO
 *
 * <AUTHOR>
 */
@TableName("iot_edge_gateway")
@KeySequence("iot_edge_gateway_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EdgeGatewayDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 网关实例编码
     */
    private String edgeCode;
    /**
     * 边缘网关名称
     */
    private String edgeName;
    /**
     * 边缘网关应用名
     */
    private String edgeServiceName;
    /**
     * 边缘网关host
     */
    private String edgeHost;
    /**
     * 边缘网关端口
     */
    private Integer edgePort;
    /**
     * 描述
     */
    private String description;

    /**
     * 主机唯一标识
     */
    private String edgeIdentifier;

    /**
     * 主机状态（0：离线、1：在线）
     */
    private Integer edgeStatus;

    /**
     * 最近上线时间
     */
    private LocalDateTime onlineTime;

    /**
     * 心跳时间
     */
    private LocalDateTime heartTime;

    /**
     * 资源空间id
     */
    private Long resourceSpaceId;

}