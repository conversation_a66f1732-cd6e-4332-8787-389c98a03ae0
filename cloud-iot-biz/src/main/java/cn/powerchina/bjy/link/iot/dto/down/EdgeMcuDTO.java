package cn.powerchina.bjy.link.iot.dto.down;

import cn.powerchina.bjy.link.iot.model.MessageToEdge;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 边缘网关-采集仪信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EdgeMcuDTO implements MessageToEdge, Serializable {

    /**
     * 租户
     */
    private String tenant;

    /**
     * 节点类型(0直连，1网关，2网关子设备）
     */
    private Integer nodeType;

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 设备编码
     */
    private String deviceCode;

    /**
     * 网关实例编码
     */
    private String edgeCode;

    /**
     * 从站号
     */
    private String slaveId;

    /**
     * 驱动code
     */
    private String driverCode;

    /**
     * 连接方式（1-RTU；2-TCP）
     */
    private Integer connectType;

    /**
     * 连接配置详细信息，格式json
     * 1、RTU包括串口、波特率、数据位、校验位、停止位
     * 2、TCP包括IP地址、端口、数据位
     */
    private String extra;

}
