package cn.powerchina.bjy.link.iot.dto.up;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 设备属性信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EdgeReadPropertyValue implements Serializable {

    /**
     * 采集时刻时间
     */
    private Long currentTime;

    /**
     * 网关实例编码
     */
    private String edgeCode;

    /**
     * 子设备属性值
     */
    private List<EdgeDevicePropertyValueDTO> propertyValueList;

    /**
     * 子设备属性值
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class EdgeDevicePropertyValueDTO {
        /**
         * 设备编码
         */
        private String deviceCode;

        /**
         * MCU通道编码
         */
        private String mcuChannel;

        /**
         * 物模型标识符
         */
        private String thingIdentity;

        /**
         * 物模型采集值
         */
        private String thingValue;

        /**
         * 从站号
         */
        private String slaveId;
    }
}
