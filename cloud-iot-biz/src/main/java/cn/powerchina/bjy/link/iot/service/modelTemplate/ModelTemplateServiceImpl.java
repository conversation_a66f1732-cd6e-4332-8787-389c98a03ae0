package cn.powerchina.bjy.link.iot.service.modelTemplate;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.link.iot.controller.admin.modelTemplate.bo.ModelTemplateBO;
import cn.powerchina.bjy.link.iot.controller.admin.modelTemplate.vo.ModelTemplatePageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.modelTemplate.vo.ModelTemplateSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.modelCategorize.ModelCategorizeDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.modelTemplate.ModelTemplateDO;
import cn.powerchina.bjy.link.iot.dal.mysql.modelCategorize.ModelCategorizeMapper;
import cn.powerchina.bjy.link.iot.dal.mysql.modelTemplate.ModelTemplateMapper;
import cn.powerchina.bjy.link.iot.dal.mysql.modelTemplateDetails.ModelTemplateDetailsMapper;
import cn.powerchina.bjy.link.iot.dal.mysql.product.ProductMapper;
import cn.powerchina.bjy.link.iot.enums.ErrorCodeConstants;
import cn.powerchina.bjy.link.iot.util.SnowFlakeUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import de.danielbechler.util.Collections;
import jakarta.annotation.Resource;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Objects;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;


@Service
@ToString
@Slf4j
public class ModelTemplateServiceImpl implements ModelTemplateService {

    @Resource
    private ModelTemplateMapper modelTemplateMapper;

    @Resource
    private ModelCategorizeMapper modelCategorizeMapper;

    @Resource
    private ProductMapper productMapper;

    @Autowired
    private SnowFlakeUtil snowFlakeUtil;

    @Override
    public PageResult<ModelTemplateBO> getModelTemplatePage(ModelTemplatePageReqVO pageReqVO) {
        Long categorizeTwoId=pageReqVO.getCategorizeTwoId();
        if(pageReqVO.getLevel()==1)
        {
            pageReqVO.setCategorizeOneId(pageReqVO.getCategorizeTwoId());
            pageReqVO.setCategorizeTwoId(null);
        }
        if(pageReqVO.getLevel()==2)
        {
            pageReqVO.setCategorizeTwoId(categorizeTwoId);
        }
        PageResult<ModelTemplateDO> doPageResult = modelTemplateMapper.selectPage(pageReqVO);
        PageResult<ModelTemplateBO> bOPageResult = BeanUtils.toBean(doPageResult, ModelTemplateBO.class);
        if(!Collections.isEmpty(doPageResult.getList()))
        {
            bOPageResult.getList().forEach(item -> {
                item.setProductsNumber(productMapper.selectCountByTemplateId(item.getId()).intValue());
                if(item.getCategorizeTwoId()!=null)
                {
                    ModelCategorizeDO modelCategorizeDO=modelCategorizeMapper.selectById(item.getCategorizeTwoId());
                    StringBuffer sb=new StringBuffer();
                    if(modelCategorizeDO!=null)
                    {

                        //查询一级分类
                        ModelCategorizeDO modelCategorize=modelCategorizeMapper.selectById(modelCategorizeDO.getParentId());
                        if(modelCategorize!=null)
                        {

                            sb.append(modelCategorize.getCategorizeName());
                            sb.append("/");
                        }
                        sb.append(modelCategorizeDO.getCategorizeName());
                    }
                    item.setCategorizeName(sb.toString());
                }
            });
        }
        return bOPageResult;
    }

    @Override
    public ModelTemplateDO getModelTemplate(Long id) {
        return Objects.isNull(id) ? null : modelTemplateMapper.selectById(id);
    }

    @Override
    public ModelTemplateBO getModelTemplateBO(Long id) {
        ModelTemplateDO modelTemplateDO = getModelTemplate(id);
        if (Objects.nonNull(modelTemplateDO)) {
            ModelTemplateBO modelCategorizeBO = BeanUtils.toBean(modelTemplateDO, ModelTemplateBO.class);
            return modelCategorizeBO;
        }
        return null;
    }

    @Override
    public Long createModelTemplate(ModelTemplateSaveReqVO createReqVO) {
        // 校验物模板名称是否重复
        validateTemplateNameExists(createReqVO.getTemplateName());
        // 插入分类
        ModelTemplateDO modelTemplateDO = BeanUtils.toBean(createReqVO, ModelTemplateDO.class);
        if(!StringUtils.isEmpty(createReqVO.getCategorizeId()))
        {
            String[] str=createReqVO.getCategorizeId().split(",");
            if(str!=null&&str.length>1)
            {
                modelTemplateDO.setCategorizeOneId(Long.parseLong(str[0]));
                modelTemplateDO.setCategorizeTwoId(Long.parseLong(str[1]));
            }
        }
        modelTemplateDO.setId(snowFlakeUtil.snowflakeId());
        modelTemplateMapper.insert(modelTemplateDO);
        return modelTemplateDO.getId();
    }

    @Override
    public void updateModelTemplate(ModelTemplateSaveReqVO updateReqVO) {
        // 校验存在
        ModelTemplateDO modelTemplateDO=modelTemplateMapper.selectById(updateReqVO.getId());
        if (modelTemplateDO== null) {
            throw exception(ErrorCodeConstants.TEMPLATE_NOT_EXISTS);
        }

        String categorizeOneId=null;
        String categorizeTwoId=null;
        if(!StringUtils.isEmpty(updateReqVO.getCategorizeId()))
        {
            String[] str=updateReqVO.getCategorizeId().split(",");
            categorizeOneId=str[0];
            categorizeTwoId=str[1];
        }
        if(!StringUtils.isEmpty(updateReqVO.getTemplateName())) {
            if(!updateReqVO.getTemplateName().equals(modelTemplateDO.getTemplateName())){
                // 校验产品是否重名
                QueryWrapper<ModelTemplateDO> wrapper = new QueryWrapper<>();
                wrapper.lambda().eq(ModelTemplateDO::getTemplateName, updateReqVO.getTemplateName()).eq(ModelTemplateDO::getCategorizeOneId,categorizeOneId).eq(ModelTemplateDO::getCategorizeTwoId,categorizeTwoId).eq(ModelTemplateDO::getDeleted, Boolean.FALSE);
                if (modelTemplateMapper.exists(wrapper)) {
                    throw exception(ErrorCodeConstants.TEMPLATE_NAME_EXITS);
                }
            }
        }

        // 更新
        ModelTemplateDO updateObj = BeanUtils.toBean(updateReqVO, ModelTemplateDO.class);
        if(!StringUtils.isEmpty(updateReqVO.getCategorizeId()))
        {
            String[] str=updateReqVO.getCategorizeId().split(",");
            if(str!=null&&str.length>1)
            {
                updateObj.setCategorizeOneId(Long.parseLong(str[0]));
                updateObj.setCategorizeTwoId(Long.parseLong(str[1]));
            }
        }
        modelTemplateMapper.updateById(updateObj);
    }

    @Override
    public void updateModelTemplate(ModelTemplateDO updateModelTemplateDO) {
        modelTemplateMapper.updateById(updateModelTemplateDO);
    }

    @Override
    public void deleteModelTemplate(Long id) {
        // 校验是否存在
        validateTemplateExists(id);
        // 校验是否有子分类
        if (productMapper.selectCountByTemplateId(id) > 0) {
            throw exception(ErrorCodeConstants.TEMPLATE_IS_EXISTS);
        }
        // 删除分类
        modelTemplateMapper.deleteById(id);
    }
    /**
     * 根据产品名称校验产品是否存在
     * 新增名称唯一
     *
     * @param templateName     产品名称
     */
    private void validateTemplateNameExists(String templateName) {

        QueryWrapper<ModelTemplateDO> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(ModelTemplateDO::getTemplateName, templateName).eq(ModelTemplateDO::getDeleted, Boolean.FALSE);
        if (modelTemplateMapper.exists(wrapper)) {
            throw exception(ErrorCodeConstants.TEMPLATE_NAME_EXITS);
        }
    }
    private void validateTemplateExists(Long id) {
        if (modelTemplateMapper.selectById(id) == null) {
            throw exception(ErrorCodeConstants.TEMPLATE_NOT_EXISTS);
        }
    }
}