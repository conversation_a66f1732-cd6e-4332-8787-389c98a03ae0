package cn.powerchina.bjy.link.iot.dal.dataobject.instructiondownlog;

import cn.powerchina.bjy.cloud.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 指令下发操作记录 DO
 *
 * <AUTHOR>
 */
@TableName("iot_instruction_down_log")
@KeySequence("iot_instruction_down_log_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InstructionDownLogDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 设备编号
     */
    private String deviceCode;
    /**
     * 物模型类型
     */
    private Integer thingType;
    /**
     * 物模型标识符
     */
    private String thingIdentity;
    /**
     * 物模型名称
     */
    private String thingName;
    /**
     * 控制源（0-web；1-ios；2-android）
     */
    private Integer operatorSource;
    /**
     * messageid
     */
    private String messageId;
    /**
     * 输入参数
     */
    private String inputParams;
    /**
     * 输出参数
     */
    private String outputParams;
    /**
     * 上行消耗时间
     */
    private String upConsumeTime;
    /**
     * 下行消耗时间
     */
    private String downConsumeTime;

}