package cn.powerchina.bjy.link.iot.controller.admin.devicegroupdetail.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 设备分组明细新增/修改 Request VO")
@Data
public class DeviceGroupDetailDelReqVO {

    @Schema(description = "主键id集合")
    @NotEmpty(message = "请选择设备")
    private List<Long> idList;

}