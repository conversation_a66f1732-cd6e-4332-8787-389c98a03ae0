package cn.powerchina.bjy.link.iot.listener;

import cn.hutool.core.util.ObjectUtil;
import cn.powerchina.bjy.link.iot.dto.up.EdgeCheckOnlineStatus;
import cn.powerchina.bjy.link.iot.dto.up.EdgeConvertVideo;
import cn.powerchina.bjy.link.iot.enums.IotTopicConstant;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.annotation.RocketMQMessageListener;
import org.apache.rocketmq.client.apis.consumer.ConsumeResult;
import org.apache.rocketmq.client.apis.message.MessageView;
import org.apache.rocketmq.client.core.RocketMQListener;
import org.springframework.stereotype.Component;

import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;

@Slf4j
@Component
@RocketMQMessageListener(topic = IotTopicConstant.TOPIC_DEVICE_ONLINE_RESULT, consumerGroup = IotTopicConstant.GROUP_DEVICE_ONLINE_RESULT, requestTimeout = 10, consumptionThreadCount = 10)
public class EdgeConvertVideoReceiver implements RocketMQListener {
    @Override
    public ConsumeResult consume(MessageView messageView) {
        if (ObjectUtil.isNull(messageView)) {
            log.error("MessageView is null, skip consumption");
        }
        try {
            // 解析消息体
            EdgeConvertVideo entity = parseMessageBody(messageView);
            log.info("receive message: {}", entity);
            if (entity == null) {
                log.error("MessageView is null, skip consumption");
                return ConsumeResult.SUCCESS;
            }
        } catch (Exception e) {
            log.error("edgeOnlineCheckReceive--->error,msg={}", e.getMessage(), e);
        }
        return ConsumeResult.SUCCESS;
    }

    /**
     * 解析消息体为实体类
     */
    private EdgeConvertVideo parseMessageBody(MessageView messageView) {
        try {
            ByteBuffer body = messageView.getBody();
            if (body.remaining() == 0) {
                log.error("Message body is empty, messageId: {}", messageView.getMessageId());
                return null;
            }

            String message = StandardCharsets.UTF_8.decode(body).toString();
            return JSON.parseObject(message, EdgeConvertVideo.class);

        } catch (JSONException e) {
            log.error("JSON解析失败, messageId: {}, content: {}",
                    messageView.getMessageId(),
                    messageView.getBody(),
                    e
            );
            return null;
        }
    }
}
