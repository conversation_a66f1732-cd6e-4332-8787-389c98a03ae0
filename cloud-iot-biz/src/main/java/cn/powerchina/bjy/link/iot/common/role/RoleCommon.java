package cn.powerchina.bjy.link.iot.common.role;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.web.core.util.WebFrameworkUtils;
import cn.powerchina.bjy.cloud.system.api.permission.RoleApi;
import cn.powerchina.bjy.cloud.system.api.permission.dto.RoleRespDTO;
import cn.powerchina.bjy.link.iot.controller.admin.resourcespace.vo.ResourceSpacePageReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.resourcespace.ResourceSpaceDO;
import cn.powerchina.bjy.link.iot.enums.EnableStateEnum;
import cn.powerchina.bjy.link.iot.service.resourcespace.ResourceSpaceService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * @Description: 权限公共类
 * @Author: handl
 * @CreateDate: 2025/6/13
 */
@Slf4j
@Component
public class RoleCommon {

    @Resource
    private ResourceSpaceService resourceSpaceService;

    @Autowired
    private RoleApi roleApi;


    /**
     * 获取当前用户权限下的资源空间
     */
    public List<Long> getResourceSpaceIds(){
        ResourceSpacePageReqVO ResourceSpaceVO = new ResourceSpacePageReqVO();
        ResourceSpaceVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        ResourceSpaceVO.setState(EnableStateEnum.YES.getType());
        PageResult<ResourceSpaceDO> page = resourceSpaceService.getResourceSpacePage(ResourceSpaceVO);
        List<Long> resourceSpaceIds = new ArrayList<>();
        for (ResourceSpaceDO resourceSpaceDO : page.getList()) {
            resourceSpaceIds.add(resourceSpaceDO.getId());
        }
        return resourceSpaceIds;
    }

    /**
     * 检查当前用户是否是超级管理员
     */
    public boolean checkIfSuperAdmin() {
        boolean superAdmin = false;
        CommonResult<Map<Long, List<RoleRespDTO>>> commonResult = roleApi.userRoles(Collections.singleton(WebFrameworkUtils.getLoginUserId()));
        if (Objects.nonNull(commonResult) && !CollectionUtils.isEmpty(commonResult.getData())) {
            List<RoleRespDTO> roleList = commonResult.getData().get(WebFrameworkUtils.getLoginUserId());
            for (RoleRespDTO role : roleList) {
                if (List.of("tenant_admin", "admin").contains(role.getCode())) {
                    superAdmin = true;
                    break;
                }
            }
        }
        return superAdmin;
    }

}
