package cn.powerchina.bjy.link.iot.controller.admin.drive.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 驱动 Response VO")
@Data
@ExcelIgnoreUnannotated
public class DriveRespVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "2468")
    @ExcelProperty("主键id")
    private Long id;

    @Schema(description = "驱动名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @ExcelProperty("驱动名称")
    private String driveName;

    @Schema(description = "驱动编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("驱动编码")
    private String driveCode;

    @Schema(description = "备注名称", example = "张三")
    @ExcelProperty("备注名称")
    private String remark;

    @Schema(description = "驱动版本")
    @ExcelProperty("驱动版本")
    private String version;

    @Schema(description = "状态(1:运行中;0:未启动)", example = "2")
    @ExcelProperty("状态(1:运行中;0:未启动)")
    private Integer status;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}