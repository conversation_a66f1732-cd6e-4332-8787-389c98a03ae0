package cn.powerchina.bjy.link.iot.api.devicegroup;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.link.iot.api.devicegroup.dto.DeviceGroupRespDTO;
import cn.powerchina.bjy.link.iot.api.devicegroup.dto.DeviceRespDTO;
import cn.powerchina.bjy.link.iot.controller.admin.devicegroupdetail.bo.DeviceGroupDetailBO;
import cn.powerchina.bjy.link.iot.controller.admin.devicegroupdetail.vo.DeviceGroupDetailPageReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.device.DeviceDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.devicegroup.DeviceGroupDO;
import cn.powerchina.bjy.link.iot.service.device.DeviceService;
import cn.powerchina.bjy.link.iot.service.devicegroup.DeviceGroupService;
import cn.powerchina.bjy.link.iot.service.devicegroupdetail.DeviceGroupDetailService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;

/**
 * @Description: 设备分组
 * @Author: yhx
 * @CreateDate: 2024/9/2
 */
@RestController
@Validated
public class DeviceGroupApiImpl implements DeviceGroupApi {

    @Autowired
    private DeviceGroupService deviceGroupService;

    @Autowired
    private DeviceGroupDetailService deviceGroupDetailService;

    @Autowired
    private DeviceService deviceService;

    @Override
    public CommonResult<List<DeviceGroupRespDTO>> getDeviceGroupListByResourceSpaceId(Long resourceSpaceId) {
        List<DeviceGroupRespDTO> respDTOList = new ArrayList<>();
        List<DeviceGroupDO> groupDOList = deviceGroupService.getDeviceGroupListByResourceSpaceId(resourceSpaceId);
        if (!CollectionUtils.isEmpty(groupDOList)) {
            respDTOList.addAll(groupDOList.stream().map(item -> DeviceGroupRespDTO.builder().id(item.getId()).resourceSpaceId(item.getResourceSpaceId()).parentId(item.getParentId()).groupName(item.getGroupName()).build()).toList());
        }
        return CommonResult.success(respDTOList);
    }

    @Override
    public CommonResult<List<DeviceRespDTO>> getDeviceListByDeviceGroupId(Long deviceGroupId) {
        DeviceGroupDetailPageReqVO pageReqVO = new DeviceGroupDetailPageReqVO();
        pageReqVO.setDeviceGroupId(deviceGroupId);
        pageReqVO.setPageNo(1);
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        PageResult<DeviceGroupDetailBO> detailBOPageResult = deviceGroupDetailService.getDeviceGroupDetailBOPage(pageReqVO);
        if (CollectionUtils.isEmpty(detailBOPageResult.getList())) {
            return CommonResult.success(Collections.emptyList());
        }

        List<DeviceRespDTO> respDTOList = new ArrayList<>();
        respDTOList.addAll(detailBOPageResult.getList().stream().map(item -> {
            DeviceRespDTO respDTO = new DeviceRespDTO();
            BeanUtils.copyProperties(item, respDTO);
            respDTO.setId(item.getDeviceId());//设备id
            if (StringUtils.isBlank(respDTO.getParentCode())) {
                return respDTO;
            }

            //设置父设备
            DeviceDO deviceParent = deviceService.getDevice(respDTO.getParentCode());
            if (Objects.isNull(deviceParent)) {
                return respDTO;
            }

            respDTO.setRegisterTime(deviceParent.getRegisterTime());
            respDTO.setParentName(deviceParent.getDeviceName());
            respDTO.setParentProductCode(deviceParent.getProductCode());
            respDTO.setParentSerial(deviceParent.getDeviceSerial());
            return respDTO;
        }).toList());

        return CommonResult.success(respDTOList);
    }

    @Override
    public CommonResult<Map<Long, List<DeviceRespDTO>>> batchGetDeviceListByDeviceGroupIds(List<Long> deviceGroupIds) {
        Map<Long, List<DeviceRespDTO>> deviceMap = new HashMap<>();
        deviceGroupIds.forEach(item -> {
            deviceMap.put(item, getDeviceListByDeviceGroupId(item).getData());
        });
        return CommonResult.success(deviceMap);
    }
}
