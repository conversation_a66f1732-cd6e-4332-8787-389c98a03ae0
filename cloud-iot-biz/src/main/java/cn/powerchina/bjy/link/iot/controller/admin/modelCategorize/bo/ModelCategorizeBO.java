package cn.powerchina.bjy.link.iot.controller.admin.modelCategorize.bo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 物模板分类 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ModelCategorizeBO {

    /**
     * 主键
     */
    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("主键")
    private Long id;
    /**
     * 父ID
     */
    @Schema(description = "父ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("父ID")
    private Long parentId;
    /**
     * 分类名称
     */
    @Schema(description = "分类名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("分类名称")
    private String categorizeName;
    /**
     * 分类层级
     */
    @Schema(description = "分类层级", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("分类层级")
    private Integer level;
    /**
     * 显示顺序
     */
    @Schema(description = "显示顺序", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("显示顺序")
    private Integer sort;
    /**
     * 启用状态
     */
    @Schema(description = "启用状态（0不启用，1启用，默认0）", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("启用状态（0不启用，1启用，默认0）")
    private Integer state;
    /**
     * 备注
     */
    @Schema(description = "备注", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;
}
