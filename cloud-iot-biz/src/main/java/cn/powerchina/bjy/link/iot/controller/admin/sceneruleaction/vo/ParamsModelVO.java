package cn.powerchina.bjy.link.iot.controller.admin.sceneruleaction.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description: 描述
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @CreateDate: 2025/5/30
 */
@Schema(description = "管理后台 - 参数模型 VO")
@Data
public class ParamsModelVO {

    private Integer required;

    private String datatype;

    private String thingName;
    private String thingValue;
//    @Schema(description = "物模型类型，1-属性；2-服务；3-事件；", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
//    private String thingType;

    private String inputParams;
    private String thingIdentity;

}
