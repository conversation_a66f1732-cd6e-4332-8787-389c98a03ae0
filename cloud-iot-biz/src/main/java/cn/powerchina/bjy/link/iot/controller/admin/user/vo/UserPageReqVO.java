package cn.powerchina.bjy.link.iot.controller.admin.user.vo;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 用户信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class UserPageReqVO extends PageParam {

    @Schema(description = "用户账号", example = "王五")
    private String username;

    @Schema(description = "姓名", example = "芋艿")
    private String name;

    @Schema(description = "手机号码")
    private String mobile;

    @Schema(description = "部门名称", example = "张三")
    private String deptName;

    @Schema(description = "职务", example = "李四")
    private String postName;

    @Schema(description = "用户邮箱")
    private String email;

    @Schema(description = "启用状态（0正常 1停用）", example = "1")
    private Integer status;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}