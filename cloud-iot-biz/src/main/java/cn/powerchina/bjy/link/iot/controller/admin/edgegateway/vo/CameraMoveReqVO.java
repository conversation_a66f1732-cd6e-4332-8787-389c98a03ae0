package cn.powerchina.bjy.link.iot.controller.admin.edgegateway.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

@Schema(description = "摄像头移动控制 Request VO")
@Data
public class CameraMoveReqVO {

    @Schema(description = "设备ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "camera_001")
    @NotEmpty(message = "设备ID不能为空")
    private String deviceId;

    @Schema(description = "控制指令(1:上,2:下,3:左,4:右,5:左上,6:右上,7:左下,8:右下,9:变倍减,10:变倍增,11:变焦增,12:变焦减,13:光圈减,14:光圈增)",
            requiredMode = Schema.RequiredMode.REQUIRED, example = "3")
    private Integer command;

    @Schema(description = "控制速度(1-7)", requiredMode = Schema.RequiredMode.REQUIRED, example = "3")
    private Integer speed;

    @Schema(description = "网关实例编码", example = "gateway_001")
    private String edgeCode;

    @Schema(description = "操作描述", example = "摄像头向左移动")
    private String description;

}
