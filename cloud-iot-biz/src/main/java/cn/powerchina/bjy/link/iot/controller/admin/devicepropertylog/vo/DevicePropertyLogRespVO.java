package cn.powerchina.bjy.link.iot.controller.admin.devicepropertylog.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 设备属性日志 Response VO")
@Data
@ExcelIgnoreUnannotated
public class DevicePropertyLogRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "29963")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "设备编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("设备编号")
    private String deviceCode;

    @Schema(description = "设备名称", requiredMode = Schema.RequiredMode.REQUIRED,example = "")
    @ExcelProperty("设备名称")
    private String deviceName;

    @Schema(description = "物模型标识符", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("物模型标识符")
    private String thingIdentity;

    @Schema(description = "物模型名称", requiredMode = Schema.RequiredMode.REQUIRED,example = "")
    @ExcelProperty("物模型名称")
    private String thingName;

    @Schema(description = "物模型日志值", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("物模型日志值")
    private String thingValue;

    @Schema(description = "模式(1=影子模式，2=在线模式，3=其他)", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("模式(1=影子模式，2=在线模式，3=其他)")
    private Integer deviceMode;

    @Schema(description = "备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "你说的对")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}