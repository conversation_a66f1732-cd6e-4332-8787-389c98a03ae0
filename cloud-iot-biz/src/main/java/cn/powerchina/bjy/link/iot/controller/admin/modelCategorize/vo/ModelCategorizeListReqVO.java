package cn.powerchina.bjy.link.iot.controller.admin.modelCategorize.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 物模板分类列表 Request VO")
@Data
public class ModelCategorizeListReqVO {

    @Schema(description = "物模板分类名称")
    private String categorizeName;

    @Schema(description = "产品启用状态（0未启用，1启用，默认1）")
    private Integer state;

    @Schema(description = "分类层级",  example = "")
    private Integer level;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;
}
