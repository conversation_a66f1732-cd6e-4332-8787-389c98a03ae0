package cn.powerchina.bjy.link.iot.controller.admin.edgegateway.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import org.jetbrains.annotations.NotNull;

import java.util.Map;

@Data
@Schema(description = "PTZ控制命令VO")
public class PtzCommandVO {

    @NotBlank(message = "设备ID不能为空")
    @Schema(description = "目标设备标识", example = "camera_001")
    private String deviceId;

    @NotBlank(message = "设备ID不能为空")
    @Schema(description = "设备名称", example = "camera_001")
    private String deviceName;

    @NotBlank(message = "设备唯一标识")
    @Schema(description = "目标设备标识", example = "camera_001")
    private String deviceSerial;

    @NotBlank(message = "指令类型不能为空")
    @Schema(description = "指令类型", example = "ptz_move")
    private String command;

    @Schema(description = "指令参数")
    private Map<String, Object> params;
}
