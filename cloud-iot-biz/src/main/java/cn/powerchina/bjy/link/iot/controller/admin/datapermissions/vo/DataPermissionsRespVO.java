package cn.powerchina.bjy.link.iot.controller.admin.datapermissions.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.util.*;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 数据权限 Response VO")
@Data
@ExcelIgnoreUnannotated
public class DataPermissionsRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "13458")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "27371")
    @ExcelProperty("用户ID")
    private Long userId;

    @Schema(description = "资源空间id", requiredMode = Schema.RequiredMode.REQUIRED, example = "30079")
    @ExcelProperty("资源空间id")
    private Long resourceSpaceId;

    @Schema(description = "边缘计算ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "27429")
    @ExcelProperty("边缘计算ID")
    private Long gatewayId;

    @Schema(description = "产品id", requiredMode = Schema.RequiredMode.REQUIRED, example = "25685")
    @ExcelProperty("产品id")
    private Long productId;

    @Schema(description = "设备id", requiredMode = Schema.RequiredMode.REQUIRED, example = "12186")
    @ExcelProperty("设备id")
    private Long deviceId;

}
