package cn.powerchina.bjy.link.iot.service.devicegroupdetail;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.iot.aop.devicegroupdetail.DeviceGroupDetailPermissionCheck;
import cn.powerchina.bjy.link.iot.controller.admin.devicegroupdetail.bo.DeviceGroupDetailBO;
import cn.powerchina.bjy.link.iot.controller.admin.devicegroupdetail.vo.DeviceGroupDetailPageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.devicegroupdetail.vo.DeviceGroupDetailSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.device.DeviceDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.devicegroupdetail.DeviceGroupDetailDO;
import cn.powerchina.bjy.link.iot.dal.mysql.devicegroupdetail.DeviceGroupDetailMapper;
import cn.powerchina.bjy.link.iot.enums.NodeTypeEnum;
import cn.powerchina.bjy.link.iot.model.DeviceGroupChangeModel;
import cn.powerchina.bjy.link.iot.service.device.DeviceService;
import cn.powerchina.bjy.link.iot.service.devicegroup.DeviceGroupService;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import java.util.stream.Collectors;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.link.iot.enums.ErrorCodeConstants.DEVICE_GROUP_DETAIL_NOT_EXISTS;

/**
 * 设备分组明细 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class DeviceGroupDetailServiceImpl implements DeviceGroupDetailService {

    @Resource
    private DeviceGroupDetailMapper deviceGroupDetailMapper;

    @Autowired
    private DeviceService deviceService;

    @Autowired
    private DeviceGroupService deviceGroupService;

    @Override
    @Transactional
    public void createDeviceGroupDetail(DeviceGroupDetailSaveReqVO createReqVO) {
        //校验设备分组是否存在
        deviceGroupService.validateDeviceGroupExists(createReqVO.getDeviceGroupId());
        //选择的设备
        List<DeviceDO> deviceDOList = new ArrayList<>();
        for (String deviceCode : createReqVO.getDeviceCodeList()) {
            DeviceDO deviceDO = deviceService.getDevice(deviceCode);
            if (Objects.isNull(deviceDO)) {
                continue;
            }
            deviceDOList.add(deviceDO);
            //如果选择的是网关设备，则将子设备添加进来
            if (Objects.equals(deviceDO.getNodeType(), NodeTypeEnum.EDGE.getType())) {
                List<DeviceDO> deviceDOChildrenList = deviceService.findDeviceByParentCode(Collections.singleton(deviceCode));
                if (!CollectionUtils.isEmpty(deviceDOChildrenList)) {
                    //过滤已注册的设备
                    deviceDOList.addAll(deviceDOChildrenList);
                }
            }
        }
        //转换成分组明细
        List<String> deviceCodeList = new ArrayList<>();
        List<DeviceGroupDetailDO> detailDOList = deviceDOList.stream().distinct().map(item -> {
            DeviceGroupDetailDO detailDO = new DeviceGroupDetailDO();
            detailDO.setDeviceGroupId(createReqVO.getDeviceGroupId());
            detailDO.setDeviceCode(item.getDeviceCode());
            deviceCodeList.add(item.getDeviceCode());
            return detailDO;
        }).toList();
        //查询当前分组数据库里的设备
        List<DeviceGroupDetailDO> detailDODbList = Optional.ofNullable(deviceGroupDetailMapper.selectList(
                        new LambdaQueryWrapperX<DeviceGroupDetailDO>().eq(DeviceGroupDetailDO::getDeviceGroupId, createReqVO.getDeviceGroupId())))
                .orElse(new ArrayList<>());
        //当前分组已经存在的设备编码
        List<String> deviceDbCodeList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(detailDODbList)) {
            deviceDbCodeList.addAll(detailDODbList.stream().map(DeviceGroupDetailDO::getDeviceCode).toList());
        }
        //需要新增的设备列表
        List<DeviceGroupDetailDO> detailDOAddList = detailDOList.stream().filter(item -> !deviceDbCodeList.contains(item.getDeviceCode())).toList();
        //需要移除的设备
        //  List<DeviceGroupDetailDO> detailDORemoveList = detailDODbList.stream().filter(item -> !deviceCodeList.contains(item.getDeviceCode())).toList();

        DeviceGroupChangeModel deviceGroupChangeModel = new DeviceGroupChangeModel();
        deviceGroupChangeModel.setDeviceGroupId(createReqVO.getDeviceGroupId());
//        if (!CollectionUtils.isEmpty(detailDORemoveList)) {
//            deviceGroupDetailMapper.deleteBatchIds(detailDORemoveList.stream().map(DeviceGroupDetailDO::getId).toList());
//            //通知大坝平台分组移除设备
//            List<String> removeDeviceCodeList = detailDORemoveList.stream().map(DeviceGroupDetailDO::getDeviceCode).toList();
//            deviceGroupChangeModel.setChangeType(DeviceChangeEnum.DELETE.getType());
//            deviceGroupChangeModel.setDeviceCodeList(removeDeviceCodeList);
//            messageSender.sendToEdgeRocketMQ(IotTopicConstant.TOPIC_DEVICE_GROUP_CHANGE, null, deviceGroupChangeModel);
//        }
        //批量新增
        if (!CollectionUtils.isEmpty(detailDOAddList)) {
            deviceGroupDetailMapper.insertBatch(detailDOAddList);
//            //通知大坝平台分组添加设备
//            List<String> addDeviceCodeList = detailDOAddList.stream().map(DeviceGroupDetailDO::getDeviceCode).toList();
//            deviceGroupChangeModel.setChangeType(DeviceChangeEnum.ADD.getType());
//            deviceGroupChangeModel.setDeviceCodeList(addDeviceCodeList);
//            messageSender.sendToEdgeRocketMQ(IotTopicConstant.TOPIC_DEVICE_GROUP_CHANGE, null, deviceGroupChangeModel);
        }
    }

    @Override
    public void deleteDeviceGroupDetail(Long id) {
        // 校验存在
        validateDeviceGroupDetailExists(id);
        // 删除
        deviceGroupDetailMapper.deleteById(id);
    }

    private void validateDeviceGroupDetailExists(Long id) {
        if (deviceGroupDetailMapper.selectById(id) == null) {
            throw exception(DEVICE_GROUP_DETAIL_NOT_EXISTS);
        }
    }

    @Override
    public DeviceGroupDetailDO getDeviceGroupDetail(Long id) {
        return deviceGroupDetailMapper.selectById(id);
    }

    @Override
    public PageResult<DeviceGroupDetailDO> getDeviceGroupDetailPage(DeviceGroupDetailPageReqVO pageReqVO) {
        return deviceGroupDetailMapper.selectPage(pageReqVO);
    }

    @Override
    @DeviceGroupDetailPermissionCheck
    public PageResult<DeviceGroupDetailBO> getDeviceGroupDetailBOPage(DeviceGroupDetailPageReqVO pageReqVO) {
        return deviceGroupDetailMapper.selectDeviceAndProductPage(pageReqVO);
    }

    @Override
    public void batchDeleteDeviceGroupDetail(List<Long> id) {
        deviceGroupDetailMapper.deleteBatchIds(id);
        //todo 通知大坝平台分组移除设备
    }

    @Override
    public Long selectCountByDeviceGroupId(Long deviceGroupId) {
        return deviceGroupDetailMapper.selectCount(new LambdaQueryWrapperX<DeviceGroupDetailDO>().eq(DeviceGroupDetailDO::getDeviceGroupId, deviceGroupId));
    }

    @Override
    public void deleteDeviceGroupDetailByDeviceCode(List<String> deviceCodeDeleteList) {
        deviceGroupDetailMapper.delete(new LambdaQueryWrapperX<DeviceGroupDetailDO>().in(DeviceGroupDetailDO::getDeviceCode, deviceCodeDeleteList));
    }

    @Override
    public Map<String, List<Long>> batchFindDeviceGroupId(List<String> deviceCodeList) {
        Map<String, List<Long>> deviceGroupIdMap = new HashMap<>();
        List<DeviceGroupDetailDO> detailDOList = deviceGroupDetailMapper.selectList(new LambdaQueryWrapperX<DeviceGroupDetailDO>()
                .in(DeviceGroupDetailDO::getDeviceCode, deviceCodeList));
        if (!CollectionUtils.isEmpty(detailDOList)) {
            deviceGroupIdMap.putAll(detailDOList.stream().collect(Collectors.groupingBy(DeviceGroupDetailDO::getDeviceCode,
                    Collectors.mapping(DeviceGroupDetailDO::getDeviceGroupId, Collectors.toList()))));
        }
        return deviceGroupIdMap;

    }

}