package cn.powerchina.bjy.link.iot.service.devicegroup;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.common.util.object.BeanUtils;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.link.iot.aop.devicegroup.DeviceGroupPermissionCheck;
import cn.powerchina.bjy.link.iot.controller.admin.devicegroup.vo.DeviceGroupPageReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.devicegroup.vo.DeviceGroupSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.devicegroup.DeviceGroupDO;
import cn.powerchina.bjy.link.iot.dal.mysql.devicegroup.DeviceGroupMapper;
import cn.powerchina.bjy.link.iot.service.devicegroupdetail.DeviceGroupDetailService;
import cn.powerchina.bjy.link.iot.util.SnowFlakeUtil;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.link.iot.enums.ErrorCodeConstants.*;

/**
 * 设备分组 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class DeviceGroupServiceImpl implements DeviceGroupService {

    @Resource
    private DeviceGroupMapper deviceGroupMapper;

    @Autowired
    private SnowFlakeUtil snowFlakeUtil;

    @Autowired
    @Lazy
    private DeviceGroupDetailService deviceGroupDetailService;

    @Override
    public Long createDeviceGroup(DeviceGroupSaveReqVO createReqVO) {
        // 插入
        DeviceGroupDO deviceGroup = BeanUtils.toBean(createReqVO, DeviceGroupDO.class);
        deviceGroup.setId(snowFlakeUtil.snowflakeId());
        if (Objects.isNull(createReqVO.getParentId())) {
            deviceGroup.setGroupLevel(1);
        } else {
            DeviceGroupDO parentDO = validateDeviceGroupExists(createReqVO.getParentId());
            deviceGroup.setGroupLevel(parentDO.getGroupLevel() + 1);
        }
        deviceGroupMapper.insert(deviceGroup);
        // 返回
        return deviceGroup.getId();
    }

    @Override
    public void updateDeviceGroup(DeviceGroupSaveReqVO updateReqVO) {
        // 校验存在
        validateDeviceGroupExists(updateReqVO.getId());
        // 更新
        DeviceGroupDO updateObj = BeanUtils.toBean(updateReqVO, DeviceGroupDO.class);
        deviceGroupMapper.updateById(updateObj);
    }

    @Override
    public void deleteDeviceGroup(Long id) {
        // 校验存在
        validateDeviceGroupExists(id);
        //校验是否存在子树
        if (deviceGroupMapper.selectCount(new LambdaQueryWrapperX<DeviceGroupDO>().eq(DeviceGroupDO::getParentId, id)) > 0) {
            throw exception(DEVICE_GROUP_BRANCH_EXISTS);
        }
        //校验当前节点是否有数据
        if (deviceGroupDetailService.selectCountByDeviceGroupId(id) > 0) {
            throw exception(DEVICE_GROUP_DATA_EXISTS);
        }
        // 删除
        deviceGroupMapper.deleteById(id);
    }

    @Override
    public DeviceGroupDO validateDeviceGroupExists(Long id) {
        DeviceGroupDO deviceGroupDO = deviceGroupMapper.selectById(id);
        if (Objects.isNull(deviceGroupDO)) {
            throw exception(DEVICE_GROUP_NOT_EXISTS);
        }
        return deviceGroupDO;
    }

    @Override
    public DeviceGroupDO getDeviceGroup(Long id) {
        return deviceGroupMapper.selectById(id);
    }

    @Override
    @DeviceGroupPermissionCheck
    public PageResult<DeviceGroupDO> getDeviceGroupPage(DeviceGroupPageReqVO pageReqVO) {
        pageReqVO.setPageSize(-1);
        return deviceGroupMapper.selectPage(pageReqVO);
    }

    @Override
    public List<DeviceGroupDO> getDeviceGroupListByResourceSpaceId(Long resourceSpaceId) {
        return deviceGroupMapper.selectList(new LambdaQueryWrapperX<DeviceGroupDO>().eq(DeviceGroupDO::getResourceSpaceId, resourceSpaceId));
    }

    @Override
    public List<DeviceGroupDO> getDeviceGroupListByIds(List<Long> ids) {
        List<DeviceGroupDO> groupDOList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(ids)) {
            groupDOList.addAll(deviceGroupMapper.selectList(new LambdaQueryWrapperX<DeviceGroupDO>()
                    .in(DeviceGroupDO::getId, ids)));
        }
        return groupDOList;
    }

}