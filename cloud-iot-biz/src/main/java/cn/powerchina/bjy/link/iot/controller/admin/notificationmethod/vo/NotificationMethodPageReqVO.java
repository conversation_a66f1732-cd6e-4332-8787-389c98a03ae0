package cn.powerchina.bjy.link.iot.controller.admin.notificationmethod.vo;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageParam;
import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.powerchina.bjy.cloud.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 通知方式分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class NotificationMethodPageReqVO extends PageParam {

    @Schema(description = "关联的告警模板ID", example = "23110")
    private Long alarmTemplateId;

    @Schema(description = "通知方式（1:钉钉 2：邮件 3：短信）")
    private Integer notificationMethod;

    @Schema(description = "通知账号/webhook地址", example = "22183")
    private String notificationAccount;

    @Schema(description = "通知内容")
    private String notificationContent;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
