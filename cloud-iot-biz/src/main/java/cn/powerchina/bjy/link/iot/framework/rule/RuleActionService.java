package cn.powerchina.bjy.link.iot.framework.rule;

import cn.hutool.core.collection.CollectionUtil;
import cn.powerchina.bjy.link.iot.controller.admin.scenealarmrecord.vo.SceneAlarmRecordReqVO;
import cn.powerchina.bjy.link.iot.controller.admin.scenealarmrecord.vo.SceneAlarmRecordSaveReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.scenealarmrecord.SceneAlarmRecordDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.scenerule.SceneRuleDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.sceneruleaction.SceneRuleActionDO;
import cn.powerchina.bjy.link.iot.dal.tdengine.IotDevicePropertyMapper;
import cn.powerchina.bjy.link.iot.enums.*;
import cn.powerchina.bjy.link.iot.listener.rule.TimeWindowValidator;
import cn.powerchina.bjy.link.iot.mq.RocketMQv5Client;
import cn.powerchina.bjy.link.iot.service.alarmtemplate.AlarmTemplateService;
import cn.powerchina.bjy.link.iot.service.device.DeviceService;
import cn.powerchina.bjy.link.iot.service.devicecurrentattribute.DeviceShadowService;
import cn.powerchina.bjy.link.iot.service.scenealarmrecord.SceneAlarmRecordService;
import cn.powerchina.bjy.link.iot.service.scenerule.SceneRuleService;
import cn.powerchina.bjy.link.iot.service.sceneruleaction.SceneRuleActionService;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.jeasy.rules.api.Facts;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;

import static cn.powerchina.bjy.cloud.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.powerchina.bjy.link.iot.enums.ErrorCodeConstants.SCENE_RULE_NO_STATUS;

/**
 * @Description: 描述
 * @Author: zhaoqiang
 * @CreateDate: 2025/6/19
 */
@Component
@Slf4j
public class RuleActionService {

    @Resource
    private RuleRedisCache ruleRedisCache;

    @Resource
    private RuleServer ruleServer;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private SceneRuleActionService sceneRuleActionService;

    @Resource
    private AlarmTemplateService alarmTemplateService;

    @Resource
    private SceneRuleService sceneRuleService;

    @Resource
    private SceneAlarmRecordService sceneAlarmRecordService;

    @Resource
    private ThreadPoolTaskExecutor iotThreadPoolTaskExecutor;

    @Resource
    private DeviceService deviceService;

    @Resource
    private RocketMQv5Client rocketMQv5Client;

    @Resource
    private IotDevicePropertyMapper devicePropertyMapper;

    @Resource
    private DeviceShadowService currentAttributeService;


    /**
     * 匹配规则成功后，执行相关动作
     *
     * @param facts
     */
    @Transactional
    public void matchRuleAndAction(Facts facts) {
        List<SceneRuleDO> sceneRuleDOList = ruleRedisCache.getAllRulesFromRedisOrDB();
        if (CollectionUtil.isEmpty(sceneRuleDOList)) {
            log.warn("没有正在启用的规则。。。。。");
            return;
        }

        facts.put("executeRuleList", new ArrayList<>());
        ruleServer.setFacts(facts);

        //有效时间内的规则
        List<Long> ruleIdList = new ArrayList<>();
        //禁用抑制的规则
        List<Long> notInhibitionIds = new ArrayList<>();
        sceneRuleDOList.forEach(item -> {
            boolean inTimeWindow = TimeWindowValidator.isInTimeWindow(item, LocalDateTime.now());
            if (inTimeWindow) {
                ruleIdList.add(item.getId());
            }
            if (item.getInhibition() == 0) {
                notInhibitionIds.add(item.getId());
            }
        });
        //要执行抑制规则的id集合
        List<Long> inhibitionIds = new ArrayList<>(ruleIdList);
        ruleServer.executeInhibitionRules(facts, inhibitionIds);
        //解除抑制的规则集合
        List<Long> removeInhibitionIds = facts.get("executeRuleList");
        log.info("message 解除抑制的规则{}", removeInhibitionIds);
        //对于触发和待验证状态的告警，三次不满足触发条件，自动置为恢复
        this.restoreAlarmRecord(removeInhibitionIds);
        //排除禁用抑制的规则
        if (!CollectionUtils.isEmpty(inhibitionIds)) {
            removeInhibitionIds.removeAll(notInhibitionIds);
        }
        List<Long> redisInhibitionList = new ArrayList<>();
        String redisKey = IotRedisConstant.RULE_INHIBITION_KEY;
        if (stringRedisTemplate != null) {
            Boolean hasKey = stringRedisTemplate.hasKey(redisKey);
            if (hasKey != null && hasKey) {
                List<Long> redisIds = JSONObject.parseArray(stringRedisTemplate.opsForValue().get(redisKey), Long.class);
                redisInhibitionList = CollectionUtil.isEmpty(redisIds) ? redisInhibitionList : redisIds;
                if (CollectionUtil.isNotEmpty(redisInhibitionList) && CollectionUtil.isNotEmpty(removeInhibitionIds)) {
                    redisInhibitionList.removeAll(removeInhibitionIds);
                }
            }
        }

        facts.put("executeRuleList", new ArrayList<>());
        //移除被抑制的规则；被抑制的规则不再执行
        if (!CollectionUtils.isEmpty(redisInhibitionList)) {
            //redis中移除禁用抑制的规则
            redisInhibitionList.removeAll(notInhibitionIds);
            ruleIdList.removeAll(redisInhibitionList);
        }
        ruleServer.executeRules(facts, ruleIdList);
        List<Long> executeRuleIds = facts.get("executeRuleList");
        //验证触发次数是否有中断，如果中断则未触发次数归0
        this.checkAlarmRecord(executeRuleIds);

        // log.info("执行ruleExpression后返回的Fact对象 {}", facts);
        log.info("message 匹配成功的规则{}", executeRuleIds);
        executeAction(sceneRuleDOList, executeRuleIds, facts.get("deviceCode"));

        List<Long> newExecuteRuleIds = new ArrayList<>();
        if (!CollectionUtils.isEmpty(executeRuleIds)) {
            newExecuteRuleIds.addAll(executeRuleIds);
            newExecuteRuleIds.removeAll(notInhibitionIds);
        }
        //抑制成功的规则，保存在redis缓存中(原有的+新增的)
        redisInhibitionList.addAll(newExecuteRuleIds);
        stringRedisTemplate.opsForValue().set(redisKey, JSONObject.toJSONString(redisInhibitionList));
        log.info("匹配成功并且启用抑制的规则{}", redisInhibitionList);
    }

    /**
     * 执行动作
     *
     * @param sceneRuleDOList
     * @param executeRuleIds
     */
    private void executeAction(List<SceneRuleDO> sceneRuleDOList, List<Long> executeRuleIds, String deviceCode) {
        sceneRuleDOList.forEach(sceneRuleDO -> {
            if (executeRuleIds.contains(sceneRuleDO.getId())) {
                Long ruleId = sceneRuleDO.getId();
                executeActionByRuleId(ruleId, deviceCode);
            }
        });
    }

    /**
     * 执行规则id对应的动作
     *
     * @param ruleId
     */
    public void executeActionByRuleId(Long ruleId, String deviceCode) {
        List<SceneRuleActionDO> ruleActionList = sceneRuleActionService.getSceneRuleActionByRuleId(ruleId);
        if (CollectionUtil.isNotEmpty(ruleActionList)) {
            ruleActionList.forEach(action -> {
                switch (action.getActionType()) {
                    case 1:
                        long timeMillis = 0L;
                        try {
                            String[] parts = action.getDelaySeconds().split(":");
                            Duration duration = Duration.ofMinutes(Long.parseLong(parts[0]))
                                    .plusSeconds(Long.parseLong(parts[1]));
                            timeMillis = duration.getSeconds();
                        } catch (Exception e) {
                            log.error("无效的时间格式 {}，应为 mm:ss", e.getMessage());
                        }

                        log.info("规则 {} 的动作 {} 延迟 {} 秒执行", action.getRuleId(), action.getId(), timeMillis);
                        try {
                            //判断指令类型，DATE赋值长整型时间戳
                            action.setCommandConfig(processDateValues(action.getCommandConfig()));
                            rocketMQv5Client.syncSendDelayMessage(IotTopicConstant.TOPIC_RULE_ACTION_DELAY, action, timeMillis);
                        } catch (Exception e) {
                            log.error("send rocketmq message error {}", e.getMessage());
                        }
                        break;
                    case 2:
                        alarmTemplateService.sendAlarm(action.getId(), deviceCode);
                        break;
                    case 3:
                        if (Objects.nonNull(action.getSceneId())) {
                            log.info("规则 {} 的执行动作{} 绑定执行场景 {}-{}", action.getRuleId(), action.getId(), action.getSceneId(), action.getSceneStatus());
                            if (SceneStatusEnum.EXECUTE_SCENE.getType().equals(action.getSceneStatus())) {
                                //不能执行禁用的规则
                                SceneRuleDO sceneRule = sceneRuleService.getSceneRule(action.getSceneId());
                                if (EnableStateEnum.NO.getType().equals(sceneRule.getStatus())) {
                                    throw exception(SCENE_RULE_NO_STATUS);
                                }
                                executeActionByRuleId(action.getSceneId(), deviceCode);
                            } else {
                                Integer status = SceneStatusEnum.ON_SCENE.getType().equals(action.getSceneStatus()) ? 1 : 0;
                                sceneRuleService.switchSceneRuleStatus(action.getSceneId(), status);
                            }
                        }
                        break;
                    default:
                }
            });
        }
    }

    /**
     * 对于触发和待验证状态的告警，三次不满足触发条件，自动置为恢复
     *
     * @param ruleIds
     */
    public void restoreAlarmRecord(List<Long> ruleIds) {
        for (Long ruleId : ruleIds) {
            SceneRuleDO sceneRuleDO = sceneRuleService.getSceneRule(ruleId);
            //查询是否有告警记录，如果没有告警记录，则不做任何操作
            SceneAlarmRecordReqVO sceneAlarmRecordReqVO = new SceneAlarmRecordReqVO();
            sceneAlarmRecordReqVO.setRuleId(ruleId);
            sceneAlarmRecordReqVO.setResourceSpaceId(sceneRuleDO.getResourceSpaceId());
            sceneAlarmRecordReqVO.setAlarmStatusList(Arrays.asList(
                    AlarmStatusEnum.TRIGGER.getType(),
                    AlarmStatusEnum.CHECKING.getType()));
            List<SceneAlarmRecordDO> sceneAlarmRecordList = sceneAlarmRecordService.getSceneAlarmRecordList(sceneAlarmRecordReqVO);
            if (CollectionUtils.isEmpty(sceneAlarmRecordList)) continue;

            if (sceneRuleDO.getUnsatisfiedTimes() >= 2) {
                SceneAlarmRecordDO sceneAlarmRecordDO = sceneAlarmRecordList.get(0);
                // 判断触发和待验证状态
                if (AlarmStatusEnum.TRIGGER.getType().equals(sceneAlarmRecordDO.getAlarmStatus()) || AlarmStatusEnum.CHECKING.getType().equals(sceneAlarmRecordDO.getAlarmStatus())) {
                    SceneAlarmRecordSaveReqVO updateReqVO = new SceneAlarmRecordSaveReqVO();
                    updateReqVO.setId(sceneAlarmRecordDO.getId());
                    updateReqVO.setRuleId(ruleId);
                    updateReqVO.setAlarmStatus(AlarmStatusEnum.RESTORE.getType());
                    updateReqVO.setRecoveryType(RecoveryTypeEnum.AUTO_RESTORE.getType());
                    updateReqVO.setRecoveryTime(LocalDateTime.now());
                    sceneAlarmRecordService.updateSceneAlarmRecord(updateReqVO);
                    //恢复后，未触发次数置为0
                    sceneRuleService.updateUnsatisfiedTimes(ruleId, 0);
                }
            } else {
                sceneRuleService.updateUnsatisfiedTimes(ruleId, sceneRuleDO.getUnsatisfiedTimes() + 1);
            }
        }
    }

    public void checkAlarmRecord(List<Long> ruleIds) {
        for (Long ruleId : ruleIds) {
            SceneRuleDO sceneRuleDO = sceneRuleService.getSceneRule(ruleId);
            //查询是否有告警记录，如果没有告警记录，则不做任何操作
            SceneAlarmRecordReqVO sceneAlarmRecordReqVO = new SceneAlarmRecordReqVO();
            sceneAlarmRecordReqVO.setRuleId(ruleId);
            sceneAlarmRecordReqVO.setResourceSpaceId(sceneRuleDO.getResourceSpaceId());
            sceneAlarmRecordReqVO.setAlarmStatusList(Arrays.asList(
                    AlarmStatusEnum.TRIGGER.getType(),
                    AlarmStatusEnum.CHECKING.getType()
            ));
            List<SceneAlarmRecordDO> sceneAlarmRecordList = sceneAlarmRecordService.getSceneAlarmRecordList(sceneAlarmRecordReqVO);
            if (CollectionUtils.isEmpty(sceneAlarmRecordList)) continue;

            if (sceneRuleDO.getUnsatisfiedTimes() != 0) {
                sceneRuleService.updateUnsatisfiedTimes(ruleId, 0);
            }
        }
    }

    /**
     * 解析commandConfig，时间类型进行赋值长整型时间戳
     * @param jsonStr
     * @return
     * @throws Exception
     */
    public static String processDateValues(String jsonStr) throws Exception {
        ObjectMapper mapper = new ObjectMapper();
        JsonNode rootNode = mapper.readTree(jsonStr);

        if (rootNode.isArray()) {
            for (JsonNode item : rootNode) {
                JsonNode paramsConfig = item.path("paramsConfig");
                if (paramsConfig.isArray()) {
                    Iterator<JsonNode> iterator = paramsConfig.iterator();
                    while (iterator.hasNext()) {
                        ObjectNode param = (ObjectNode) iterator.next();
                        if ("DATE".equals(param.path("datatype").asText())) {
                            if (param.has("thingValue")) {
                                // 转换现有日期为时间戳
                                String dateStr = param.path("thingValue").asText();
                                try {
                                    long timestamp = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
                                            .parse(dateStr).getTime();
                                    param.put("thingValue", timestamp);
                                } catch (Exception e) {
                                    param.put("thingValue", System.currentTimeMillis());
                                }
                            } else {
                                // 添加当前时间戳
                                param.put("thingValue", System.currentTimeMillis());
                            }
                        }
                    }
                }
            }
        }
        return mapper.writeValueAsString(rootNode);
    }

}
