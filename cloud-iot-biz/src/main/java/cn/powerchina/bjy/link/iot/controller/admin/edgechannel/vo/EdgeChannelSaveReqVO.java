package cn.powerchina.bjy.link.iot.controller.admin.edgechannel.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(description = "管理后台 - 通道新增/修改 Request VO")
@Data
public class EdgeChannelSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "3853")
    private Long id;

    @Schema(description = "关联的驱动code", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "关联的驱动code不能为空")
    private String driverCode;

    @Schema(description = "关联的边缘实例code")
    @NotEmpty(message = "边缘实例不能为空")
    private String edgeCode;

    @Schema(description = "连接方式（1-RTU；2-TCP）", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "连接方式（1-RTU；2-TCP）不能为空")
    private Integer connectType;

    @Schema(description = "通道编码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String channelCode;

    @Schema(description = "通道名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "")
    @NotEmpty(message = "通道名称不能为空")
    private String channelName;

    @Schema(description = "差异化扩展")
    private String extra;

}