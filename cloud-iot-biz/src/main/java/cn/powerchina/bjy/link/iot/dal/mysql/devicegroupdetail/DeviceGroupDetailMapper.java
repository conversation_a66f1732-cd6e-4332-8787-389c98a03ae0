package cn.powerchina.bjy.link.iot.dal.mysql.devicegroupdetail;

import cn.powerchina.bjy.cloud.framework.common.pojo.PageResult;
import cn.powerchina.bjy.cloud.framework.mybatis.core.mapper.BaseMapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.powerchina.bjy.cloud.framework.mybatis.core.query.MPJLambdaWrapperX;
import cn.powerchina.bjy.link.iot.controller.admin.devicegroupdetail.bo.DeviceGroupDetailBO;
import cn.powerchina.bjy.link.iot.controller.admin.devicegroupdetail.vo.DeviceGroupDetailPageReqVO;
import cn.powerchina.bjy.link.iot.dal.dataobject.device.DeviceDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.devicegroupdetail.DeviceGroupDetailDO;
import cn.powerchina.bjy.link.iot.dal.dataobject.product.ProductDO;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.util.CollectionUtils;

/**
 * 设备分组明细 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface DeviceGroupDetailMapper extends BaseMapperX<DeviceGroupDetailDO> {

    default PageResult<DeviceGroupDetailDO> selectPage(DeviceGroupDetailPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<DeviceGroupDetailDO>()
                .eqIfPresent(DeviceGroupDetailDO::getDeviceGroupId, reqVO.getDeviceGroupId())
                .eqIfPresent(DeviceGroupDetailDO::getDeviceCode, reqVO.getDeviceCode())
                .orderByDesc(DeviceGroupDetailDO::getId));
    }

    default PageResult<DeviceGroupDetailBO> selectDeviceAndProductPage(DeviceGroupDetailPageReqVO reqVO) {
        MPJLambdaWrapperX<DeviceGroupDetailDO> wrapper = null;
        if (CollectionUtils.isEmpty(reqVO.getCodes())) {
            wrapper = (MPJLambdaWrapperX<DeviceGroupDetailDO>) new MPJLambdaWrapperX<DeviceGroupDetailDO>()
                    .selectAll(DeviceGroupDetailDO.class)  //这样id会用iot_device_group_detail的id，要排除这个id
                    .selectAs(DeviceDO::getId, DeviceGroupDetailBO::getDeviceId)
                    //.selectFilter(DeviceGroupDetailDO.class, deviceGroupDetail-> !"id".equals(deviceGroupDetail.getColumn()))
                    .selectAs(DeviceDO::getDeviceName, DeviceGroupDetailBO::getDeviceName)
                    .selectAs(DeviceDO::getDeviceCode, DeviceGroupDetailBO::getDeviceCode)
                    .selectAs(DeviceDO::getParentCode, DeviceGroupDetailBO::getParentCode)
                    .selectAs(DeviceDO::getMcuChannel, DeviceGroupDetailBO::getMcuChannel)
                    .selectAs(DeviceDO::getLastUpTime, DeviceGroupDetailBO::getLastUpTime)
                    .selectAs(DeviceDO::getDeviceSerial, DeviceGroupDetailBO::getDeviceSerial)
                    .selectAs(DeviceDO::getNodeType, DeviceGroupDetailBO::getNodeType)
                    .selectAs(DeviceDO::getLinkState, DeviceGroupDetailBO::getLinkState)
                    .selectAs(DeviceDO::getRegisterTime, DeviceGroupDetailBO::getRegisterTime)
                    .selectAs(DeviceDO::getRemark, DeviceGroupDetailBO::getRemark)
                    .selectAs(ProductDO::getProductName, DeviceGroupDetailBO::getProductName)
                    .selectAs(ProductDO::getProductCode, DeviceGroupDetailBO::getProductCode)
                    .eq(DeviceGroupDetailDO::getDeviceGroupId, reqVO.getDeviceGroupId())
                    .leftJoin(DeviceDO.class, "d", on -> on.eq(DeviceDO::getDeviceCode, DeviceGroupDetailDO::getDeviceCode))
                    .leftJoin(ProductDO.class, "p", on -> on.eq(ProductDO::getProductCode, DeviceDO::getProductCode))
                    .eqIfExists(ProductDO::getProductCode, reqVO.getProductCode())
                    .likeIfExists(DeviceDO::getDeviceSerial, reqVO.getDeviceSerial())
                    .eqIfExists(DeviceDO::getNodeType, reqVO.getNodeType())
                    .orderByDesc(DeviceDO::getId);
        } else {
            wrapper = (MPJLambdaWrapperX<DeviceGroupDetailDO>) new MPJLambdaWrapperX<DeviceGroupDetailDO>()
                    .selectAll(DeviceGroupDetailDO.class)  //这样id会用iot_device_group_detail的id，要排除这个id
                    .selectAs(DeviceDO::getId, DeviceGroupDetailBO::getDeviceId)
                    //.selectFilter(DeviceGroupDetailDO.class, deviceGroupDetail-> !"id".equals(deviceGroupDetail.getColumn()))
                    .selectAs(DeviceDO::getDeviceName, DeviceGroupDetailBO::getDeviceName)
                    .selectAs(DeviceDO::getDeviceCode, DeviceGroupDetailBO::getDeviceCode)
                    .selectAs(DeviceDO::getParentCode, DeviceGroupDetailBO::getParentCode)
                    .selectAs(DeviceDO::getMcuChannel, DeviceGroupDetailBO::getMcuChannel)
                    .selectAs(DeviceDO::getLastUpTime, DeviceGroupDetailBO::getLastUpTime)
                    .selectAs(DeviceDO::getDeviceSerial, DeviceGroupDetailBO::getDeviceSerial)
                    .selectAs(DeviceDO::getNodeType, DeviceGroupDetailBO::getNodeType)
                    .selectAs(DeviceDO::getLinkState, DeviceGroupDetailBO::getLinkState)
                    .selectAs(DeviceDO::getRegisterTime, DeviceGroupDetailBO::getRegisterTime)
                    .selectAs(DeviceDO::getRemark, DeviceGroupDetailBO::getRemark)
                    .selectAs(ProductDO::getProductName, DeviceGroupDetailBO::getProductName)
                    .selectAs(ProductDO::getProductCode, DeviceGroupDetailBO::getProductCode)
                    .eq(DeviceGroupDetailDO::getDeviceGroupId, reqVO.getDeviceGroupId())
                    .in(DeviceGroupDetailDO::getDeviceCode, reqVO.getCodes())
                    .leftJoin(DeviceDO.class, "d", on -> on.eq(DeviceDO::getDeviceCode, DeviceGroupDetailDO::getDeviceCode))
                    .leftJoin(ProductDO.class, "p", on -> on.eq(ProductDO::getProductCode, DeviceDO::getProductCode))
                    .eqIfExists(ProductDO::getProductCode, reqVO.getProductCode())
                    .likeIfExists(DeviceDO::getDeviceSerial, reqVO.getDeviceSerial())
                    .eqIfExists(DeviceDO::getNodeType, reqVO.getNodeType())
                    .orderByDesc(DeviceDO::getId);
        }


        return selectJoinPage(reqVO, DeviceGroupDetailBO.class, wrapper);
    }
}