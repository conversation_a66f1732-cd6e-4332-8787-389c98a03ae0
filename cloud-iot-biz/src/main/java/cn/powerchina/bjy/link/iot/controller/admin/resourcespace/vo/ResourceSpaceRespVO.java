package cn.powerchina.bjy.link.iot.controller.admin.resourcespace.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 资源空间 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ResourceSpaceRespVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1652")
    @ExcelProperty("主键id")
    private Long id;

    @Schema(description = "空间名称")
    @ExcelProperty("空间名称")
    private String spaceName;

    @Schema(description = "空间APPID")
    @ExcelProperty("空间APPID")
    private String spaceAppid;

    @Schema(description = "启用状态（0不启用，1启用，默认0）")
    @ExcelProperty("启用状态")
    private Integer state;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}