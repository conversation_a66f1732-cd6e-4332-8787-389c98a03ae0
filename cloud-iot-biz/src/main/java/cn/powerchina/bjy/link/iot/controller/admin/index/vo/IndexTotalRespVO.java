package cn.powerchina.bjy.link.iot.controller.admin.index.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description: 首页统计数量
 * @Author: yhx
 * @CreateDate: 2024/8/19
 */
@Data
@Schema(description = "管理后台 - 首页统计")
public class IndexTotalRespVO {

    @Schema(description = "产品总数")
    private Long productTotal;

    @Schema(description = "产品今日新增")
    private Long productToday;

    @Schema(description = "设备总数")
    private Long deviceTotal;

    @Schema(description = "在线设备总数")
    private Long deviceOnlineTotal;

    @Schema(description = "离线设备总数")
    private Long deviceOfflineTotal;

    @Schema(description = "设备今日新增")
    private Long deviceToday;

    @Schema(description = "网关设备总数")
    private Long deviceEdgeTotal;

    @Schema(description = "网关子设备总数")
    private Long deviceSubTotal;

    @Schema(description = "直连设备总数")
    private Long deviceDirectTotal;

    @Schema(description = "网关设备百分比")
    private String percentDeviceEdge;

    @Schema(description = "子设备百分比")
    private String percentDeviceSub;

    @Schema(description = "直连设备百分比")
    private String percentDeviceDirect;

    @Schema(description = "设备消息总数")
    private Long messageTotal;

    @Schema(description = "设备今日消息数量")
    private Long messageToday;

    @Schema(description = "消息转发总数")
    private Long messageTransportTotal;

    @Schema(description = "消息今日转发数量")
    private Long messageTransportToday;
}
