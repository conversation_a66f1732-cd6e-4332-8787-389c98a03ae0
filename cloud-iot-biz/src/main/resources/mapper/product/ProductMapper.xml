<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.powerchina.bjy.link.iot.dal.mysql.product.ProductMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：/MyBatis/x-plugins/
     -->
    <update id="updateInfo" parameterType="cn.powerchina.bjy.link.iot.dal.dataobject.product.ProductDO" >
        update iot_product
        <set >
            <if test="productName!= null and productName!=''" >
                product_name = #{productName},
            </if>
            <if test="productCode!= null and productCode!=''" >
                product_code = #{productCode},
            </if>
            <if test="productModel!= null and productModel!=''" >
                product_model = #{productModel},
            </if>
            <if test="firmName!= null and firmName!=''" >
                firm_name = #{firmName},
            </if>
            <if test="description!= null and description!=''" >
                description = #{description},
            </if>
            <if test="nodeType!= null" >
                node_type = #{nodeType},
            </if>
            <if test="protocolCode!= null and protocolCode!=''" >
                protocol_code = #{protocolCode},
            </if>
            <if test="networkMethod!= null and networkMethod!=''" >
                network_method = #{networkMethod},
            </if>
            <if test="dataFormat!= null and dataFormat!=''" >
                data_format = #{dataFormat},
            </if>
            <if test="productState!= null" >
                product_state = #{productState},
            </if>
            <if test="productSecret!= null and productSecret!=''" >
                product_secret = #{productSecret},
            </if>
            <if test="resourceSpaceId!= null" >
                resource_space_id = #{resourceSpaceId},
            </if>
            <if test="deleted!= null" >
                deleted = #{deleted},
            </if>
            <if test="creator!= null and creator!=''" >
                creator = #{creator},
            </if>
            <if test="createTime!= null" >
                create_time = #{createTime},
            </if>
            <if test="updater!= null and updater!=''" >
                updater = #{updater},
            </if>
            <if test="updateTime!= null" >
                update_time = #{updateTime},
            </if>
            <if test="templateId!= null" >
                template_id = #{templateId},
            </if>
            <if test="templateId ==null" >
                template_id = null,
            </if>
            <if test="authMethod !=null" >
                auth_method = #{authMethod},
            </if>
        </set>
        where id = #{id}
    </update>
</mapper>