<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.powerchina.bjy.link.iot.dal.mysql.scenealarmrecord.SceneAlarmRecordMapper">

    <select id="selectRecordList" resultType="cn.powerchina.bjy.link.iot.controller.admin.scenealarmrecord.vo.SceneAlarmRecordRespVO">
        SELECT temp.* FROM (
        SELECT t.*, p.device_serial AS deviceSerial
        FROM iot_scene_alarm_record t
        LEFT JOIN iot_device p ON p.device_code = t.device_code AND p.deleted = 0
        WHERE t.deleted = 0
        AND t.device_code IS NOT NULL
        <if test="resourceSpaceId != null">
            AND t.resource_space_id = #{resourceSpaceId}
        </if>
        <if test="resourceSpaceIdList != null and resourceSpaceIdList.size() > 0">
            AND t.resource_space_id IN
            <foreach collection="resourceSpaceIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="deviceCodes != null and deviceCodes.size() > 0">
            AND t.device_code IN
            <foreach collection="deviceCodes" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="ruleName != null and ruleName != ''">
            AND t.rule_name LIKE CONCAT('%',#{ruleName},'%')
        </if>
        <if test="alarmName != null and alarmName != ''">
            AND t.alarm_name LIKE CONCAT('%',#{alarmName},'%')
        </if>
        <if test="alarmLevel != null">
            AND t.alarm_level = #{alarmLevel}
        </if>
        <if test="alarmStatus != null">
            AND t.alarm_status = #{alarmStatus}
        </if>
        <if test="productCode != null">
            AND t.product_code = #{productCode}
        </if>
        <if test="deviceSerial != null and deviceSerial != ''">
            AND p.device_serial LIKE CONCAT('%',#{deviceSerial},'%')
        </if>
        <if test="triggerStartTime != null and triggerEndTime != null">
            AND t.trigger_time BETWEEN #{triggerStartTime} AND #{triggerEndTime}
        </if>

        <if test="ruleIds != null and ruleIds.size() > 0">
            UNION ALL
            SELECT r.*, d.device_serial AS deviceSerial
            FROM iot_scene_alarm_record r
            LEFT JOIN iot_device d ON d.device_code = r.device_code AND d.deleted = 0
            WHERE r.deleted = 0
            AND r.device_code IS NULL
                AND r.rule_id IN
                <foreach collection="ruleIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            <if test="ruleName != null and ruleName != ''">
                AND r.rule_name LIKE CONCAT('%',#{ruleName},'%')
            </if>
            <if test="alarmName != null and alarmName != ''">
                AND r.alarm_name LIKE CONCAT('%',#{alarmName},'%')
            </if>
            <if test="alarmLevel != null">
                AND r.alarm_level = #{alarmLevel}
            </if>
            <if test="alarmStatus != null">
                AND r.alarm_status = #{alarmStatus}
            </if>
            <if test="productCode != null">
                AND r.product_code = #{productCode}
            </if>
            <if test="deviceSerial != null and deviceSerial != ''">
                AND d.device_serial LIKE CONCAT('%',#{deviceSerial},'%')
            </if>
            <if test="triggerStartTime != null and triggerEndTime != null">
                AND r.trigger_time BETWEEN #{triggerStartTime} AND #{triggerEndTime}
            </if>
        </if>
        ) temp
        ORDER BY temp.id DESC
        limit #{pageNo}, #{pageSize}
    </select>

    <select id="selectRecordCount" resultType="java.lang.Integer">
        SELECT count(*) FROM (
        SELECT t.*, p.device_serial AS deviceSerial
        FROM iot_scene_alarm_record t
        LEFT JOIN iot_device p ON p.device_code = t.device_code AND p.deleted = 0
        WHERE t.deleted = 0
        AND t.device_code IS NOT NULL
        <if test="resourceSpaceId != null">
            AND t.resource_space_id = #{resourceSpaceId}
        </if>
        <if test="resourceSpaceIdList != null and resourceSpaceIdList.size() > 0">
            AND t.resource_space_id IN
            <foreach collection="resourceSpaceIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="deviceCodes != null and deviceCodes.size() > 0">
            AND t.device_code IN
            <foreach collection="deviceCodes" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="ruleName != null and ruleName != ''">
            AND t.rule_name LIKE CONCAT('%',#{ruleName},'%')
        </if>
        <if test="alarmName != null and alarmName != ''">
            AND t.alarm_name LIKE CONCAT('%',#{alarmName},'%')
        </if>
        <if test="alarmLevel != null">
            AND t.alarm_level = #{alarmLevel}
        </if>
        <if test="alarmStatus != null">
            AND t.alarm_status = #{alarmStatus}
        </if>
        <if test="productCode != null">
            AND t.product_code = #{productCode}
        </if>
        <if test="deviceSerial != null and deviceSerial != ''">
            AND p.device_serial LIKE CONCAT('%',#{deviceSerial},'%')
        </if>
        <if test="triggerStartTime != null and triggerEndTime != null">
            AND t.trigger_time BETWEEN #{triggerStartTime} AND #{triggerEndTime}
        </if>

        <if test="ruleIds != null and ruleIds.size() > 0">
            UNION ALL
            SELECT r.*, d.device_serial AS deviceSerial
            FROM iot_scene_alarm_record r
            LEFT JOIN iot_device d ON d.device_code = r.device_code AND d.deleted = 0
            WHERE r.deleted = 0
            AND r.device_code IS NULL
            AND r.rule_id IN
            <foreach collection="ruleIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            <if test="ruleName != null and ruleName != ''">
                AND r.rule_name LIKE CONCAT('%',#{ruleName},'%')
            </if>
            <if test="alarmName != null and alarmName != ''">
                AND r.alarm_name LIKE CONCAT('%',#{alarmName},'%')
            </if>
            <if test="alarmLevel != null">
                AND r.alarm_level = #{alarmLevel}
            </if>
            <if test="alarmStatus != null">
                AND r.alarm_status = #{alarmStatus}
            </if>
            <if test="productCode != null">
                AND r.product_code = #{productCode}
            </if>
            <if test="deviceSerial != null and deviceSerial != ''">
                AND d.device_serial LIKE CONCAT('%',#{deviceSerial},'%')
            </if>
            <if test="triggerStartTime != null and triggerEndTime != null">
                AND r.trigger_time BETWEEN #{triggerStartTime} AND #{triggerEndTime}
            </if>
        </if>
        ) temp
    </select>

</mapper>