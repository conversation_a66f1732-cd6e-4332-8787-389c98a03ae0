<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.powerchina.bjy.link.iot.dal.mysql.device.DeviceMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：/MyBatis/x-plugins/
     -->

    <insert id="saveDevice" useGeneratedKeys="true" keyProperty="id">
        insert into iot_device(product_code, project_code, driver_code, device_name, device_code, parent_code, device_serial,
                               shadow, channel_code, mcu_channel, slave_id, distribute_state, link_state, node_type, longitude,
                               latitude, extra, last_up_time, deleted, creator, create_time, updater, update_time,
                               register_time, edge_code, remark, device_secret, status, device_type)
        VALUES (#{deviceDO.productCode}, #{deviceDO.projectCode}, #{deviceDO.driverCode}, #{deviceDO.deviceName},
                #{deviceDO.deviceCode}, #{deviceDO.parentCode}, #{deviceDO.deviceSerial},
                #{deviceDO.shadow}, #{deviceDO.channelCode}, #{deviceDO.mcuChannel}, #{deviceDO.slaveId},
                #{deviceDO.distributeState}, #{deviceDO.linkState}, #{deviceDO.nodeType}, #{deviceDO.longitude},
                #{deviceDO.latitude}, #{deviceDO.extra}, #{deviceDO.lastUpTime}, #{deviceDO.deleted},
                #{deviceDO.creator}, #{deviceDO.createTime}, #{deviceDO.updater}, #{deviceDO.updateTime},
                #{deviceDO.registerTime}, #{deviceDO.edgeCode}, #{deviceDO.remark},
                #{deviceDO.deviceSecret}, #{deviceDO.status}, #{deviceDO.deviceType})
    </insert>
</mapper>