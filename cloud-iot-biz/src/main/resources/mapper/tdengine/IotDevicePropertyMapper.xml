<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.powerchina.bjy.link.iot.dal.tdengine.IotDevicePropertyMapper">

    <select id="getProductPropertySTableFieldList" resultType="cn.powerchina.bjy.link.iot.framework.tdengine.core.TDengineTableField">
        DESCRIBE product_property_${productKey}
    </select>

    <update id="createProductPropertySTable">
        CREATE STABLE product_property_${productKey} (
        ts TIMESTAMP,
        create_time TIMESTAMP
        )
        TAGS (
        device_code NCHAR(50)
        )
    </update>

    <update id="createProductPropertySTableByField">
        CREATE STABLE product_property_${productKey} (
        ts TIMESTAMP,
        create_time TIMESTAMP,
        <foreach item="field" collection="fields" separator=",">
            ${field.field} ${field.type}
            <if test="field.length != null and field.length > 0">
                (${field.length})
            </if>
        </foreach>
        )
        TAGS (
        device_code NCHAR(50)
        )
    </update>



    <update id="alterProductPropertySTableAddField">
        ALTER STABLE product_property_${productKey}
        ADD COLUMN ${field.field} ${field.type}
        <if test="field.length != null and field.length > 0">
            (${field.length})
        </if>
    </update>

    <update id="alterProductPropertySTableModifyField">
        ALTER STABLE product_property_${productKey}
        MODIFY COLUMN ${field.field} ${field.type}
        <if test="field.length != null and field.length > 0">
            (${field.length})
        </if>
    </update>

    <update id="alterProductPropertySTableDropField">
        ALTER STABLE product_property_${productKey}
        DROP COLUMN ${field.field}
    </update>

    <insert id="insert">
        INSERT INTO device_property_${deviceCode}
        USING product_property_${productCode}
        TAGS ('${deviceCode}')
        (ts, create_time,
        <foreach item="key" collection="properties.keys" separator=",">
            ${@cn.hutool.core.util.StrUtil@toUnderlineCase(key)}
        </foreach>
        )
        VALUES
        ( #{reportTime}, now(),
        <foreach item="value" collection="properties.values" separator=",">
            #{value}
        </foreach>
        )
    </insert>

    <select id="describeSuperTable" resultType="java.util.Map">
        DESCRIBE product_property_${productKey}
    </select>

    <select id="selectSuperTable" resultType="java.util.Map">
        SELECT * FROM product_property_${productKey}
    </select>


</mapper>