<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.powerchina.bjy.link.iot.dal.mysql.messagestatisticday.MessageStatisticDayMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：/MyBatis/x-plugins/
     -->
    <select id="selectCountMessage" resultType="java.lang.Long" parameterType="java.util.Date">
        select sum(statistic_count) from iot_message_statistic_day
        <where>
            <if test="statisticDay != null">
                statistic_day = #{statisticDay}
            </if>
            and statistic_type = #{statisticType}
        </where>
    </select>

    <update id="updateStatisticCount" parameterType="map">
        update iot_message_statistic_day
        set statistic_count = statistic_count + #{statisticCount}
        where statistic_day = #{statisticDay}
          and statistic_type = #{statisticType}
    </update>
</mapper>