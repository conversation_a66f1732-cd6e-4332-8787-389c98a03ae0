<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>cn.powerchina.bjy</groupId>
        <artifactId>cloud-link-iot</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>cloud-iot-biz</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>
        iot 模块：通用业务，支撑上层业务。
        例如说：产品、设备等等
    </description>
    <dependencies>
        <!-- Spring Cloud 基础 -->
        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-v5-client-spring-boot-starter</artifactId>
            <version>2.3.4</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bootstrap</artifactId>
        </dependency>

        <!-- 依赖服务 -->
        <dependency>
            <groupId>cn.powerchina.bjy</groupId>
            <artifactId>cloud-spring-boot-starter-env</artifactId>
            <version>${revision.framework}</version>
        </dependency>

        <dependency>
            <groupId>cn.powerchina.bjy</groupId>
            <artifactId>cloud-system-api</artifactId>
            <version>${revision.system}</version>
        </dependency>
        <dependency>
            <groupId>cn.powerchina.bjy</groupId>
            <artifactId>cloud-infra-api</artifactId>
            <version>${revision.infra}</version>
        </dependency>

        <dependency>
            <groupId>cn.powerchina.bjy</groupId>
            <artifactId>cloud-iot-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- 业务组件 -->
        <dependency>
            <groupId>cn.powerchina.bjy</groupId>
            <artifactId>cloud-spring-boot-starter-biz-data-permission</artifactId>
            <version>${revision.framework}</version>
        </dependency>
        <dependency>
            <groupId>cn.powerchina.bjy</groupId>
            <artifactId>cloud-spring-boot-starter-biz-tenant</artifactId>
            <version>${revision.framework}</version>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>cn.powerchina.bjy</groupId>-->
<!--            <artifactId>cloud-spring-boot-starter-biz-ip</artifactId>-->
<!--            <version>${revision.framework}</version>-->
<!--        </dependency>-->

        <!-- Web 相关 -->
        <dependency>
            <groupId>cn.powerchina.bjy</groupId>
            <artifactId>cloud-spring-boot-starter-security</artifactId>
            <version>${revision.framework}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>

        <!-- DB 相关 -->
        <dependency>
            <groupId>cn.powerchina.bjy</groupId>
            <artifactId>cloud-spring-boot-starter-mybatis</artifactId>
            <version>${revision.framework}</version>
        </dependency>

        <dependency>
            <groupId>cn.powerchina.bjy</groupId>
            <artifactId>cloud-spring-boot-starter-redis</artifactId>
            <version>${revision.framework}</version>
        </dependency>

<!--        &lt;!&ndash; RPC 远程调用相关 &ndash;&gt;-->
        <dependency>
            <groupId>cn.powerchina.bjy</groupId>
            <artifactId>cloud-spring-boot-starter-rpc</artifactId>
            <version>${revision.framework}</version>
        </dependency>

        <!-- Registry 注册中心相关 -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>

        <!-- Config 配置中心相关 -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.fastjson2</groupId>
            <artifactId>fastjson2</artifactId>
            <version>2.0.57</version>
        </dependency>

        <!-- Job 定时任务相关 -->
        <dependency>
            <groupId>cn.powerchina.bjy</groupId>
            <artifactId>cloud-spring-boot-starter-job</artifactId>
            <version>${revision.framework}</version>
        </dependency>
        <!-- 工具类相关 -->
        <dependency>
            <groupId>cn.powerchina.bjy</groupId>
            <artifactId>cloud-spring-boot-starter-excel</artifactId>
            <version>${revision.framework}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-mail</artifactId>
        </dependency>

        <!-- 监控相关 -->
        <dependency>
            <groupId>cn.powerchina.bjy</groupId>
            <artifactId>cloud-spring-boot-starter-monitor</artifactId>
            <version>${revision.framework}</version>
        </dependency>

        <!-- Easy Rules -->
        <dependency>
            <groupId>org.jeasy</groupId>
            <artifactId>easy-rules-core</artifactId>
            <version>${rule.version}</version>
        </dependency>
        <dependency>
            <groupId>org.jeasy</groupId>
            <artifactId>easy-rules-mvel</artifactId>
            <version>${rule.version}</version>
        </dependency>

        <dependency>
            <groupId>org.dromara.hutool</groupId>
            <artifactId>hutool-extra</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.integration</groupId>
            <artifactId>spring-integration-mqtt</artifactId>
        </dependency>
        <dependency>
            <groupId>org.eclipse.paho</groupId>
            <artifactId>org.eclipse.paho.client.mqttv3</artifactId>
            <version>1.2.5</version>
        </dependency>
        <!-- JSON处理 -->
        <dependency>
            <groupId>org.json</groupId>
            <artifactId>json</artifactId>
            <version>20231013</version>
        </dependency>
        <dependency>
            <groupId>com.taosdata.jdbc</groupId>
            <artifactId>taos-jdbcdriver</artifactId>
            <version>3.6.3</version>
        </dependency>
        <dependency>
            <groupId>uk.co.caprica</groupId>
            <artifactId>vlcj</artifactId>
            <version>4.7.1</version>
        </dependency>

        <dependency>
            <groupId>org.bytedeco</groupId>
            <artifactId>javacv-platform</artifactId>
            <version>1.5.12</version>
        </dependency>

    </dependencies>

    <build>
        <!-- 设置构建的 jar 包名 -->
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <!-- 打包 -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring.boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal> <!-- 将引入的 jar 打入其中 -->
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>